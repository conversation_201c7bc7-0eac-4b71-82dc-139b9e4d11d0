{"hux": {"smartshop": [{"description": "Smartshop - HUX Connector Endpoint", "hostname": "localhost", "url": "/RPC", "port": "4021", "timeout": "2000"}]}, "ucip": {"air": [{"hostname": "localhost", "url": "/Air", "port": 10011, "timeout": 5000, "auth": {"username": "admin", "password": "airsim"}, "subscriberNumberNAI": null, "hostIdentity": {"originNodeType": "HxC", "originHostName": "HxC"}, "prefixes": {"national": "", "country": "", "international": ""}, "operatorFormats": [], "description": "Default AIR - UCIP Connector Endpoint"}]}, "cai3g": {"cai3g": [{"hostname": "localhost", "url": "/", "port": "8089", "timeout": 1000, "auth": {"username": "", "password": ""}, "description": "The Default cai3g Endpoint"}]}, "mobileMoney": {"mm": [{"protocol": "https", "hostname": "localhost", "url": "/", "port": "8084", "timeout": "5000", "authType": "oAuth", "authDetails": {"username": "myusername", "password": "mypassword", "key": "tlc12345tlc12345tlc12345tlc12345", "token": "g1kSbiaLu02YTEdwhs08gv1Cf1lsx4E/Phv3ug9k95s="}, "description": "Default Mobile Money Endpoint"}]}, "crediverse": {"ecds": [{"hostname": "localhost", "url": "/api", "port": "9085", "timeout": 5000, "auth": {"client_id": "ecdsclient", "client_secret": "7a361a9c87824855a9cfba63129730af", "username": "coalesce", "password": "DAY85py@"}, "description": "ecds crediverse"}], "genet": [{"hostname": "localhost", "url": "/api", "port": 9085, "timeout": 4000, "auth": {"client_id": "ecdsclient", "client_secret": "7a361a9c87824855a9cfba63129730af", "username": "coalesce", "password": "DAY85py@"}, "description": "genet"}]}, "smpp": {"smsc": [{"hostname": "localhost", "protocol": "SMPP", "port": 2775, "timeout": "2000", "encodings": ["ASCII", "LATIN1", "UCS2"], "auth": {"system_id": "sysID", "password": "admin"}, "description": "smsc"}]}, "comzaAcs": {"acs": [{"protocol": "http", "hostname": "localhost", "url": "/RequestHandlerAPI", "port": "8087", "timeout": "3000", "authType": "basic", "authDetails": {"token": "e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[", "username": "admin", "password": "admin", "key": "jfkklgu;hi'jk"}, "description": "ACS Endpoint", "countryCode": "21"}]}}