{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"message_id": "a50a3960-4030-11ea-ba05-3d5175c9081b"}}]}}, "messages": {"a50a3960-4030-11ea-ba05-3d5175c9081b": {"description": "msg-a50a3960-4030-11ea-ba05-3d5175c9081b", "content": {"eng": "Invalid USSD Code\n", "fra": "Invalid USSD Code\n", "ewe": "Invalid USSD Code\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again", "ewe": "ewe: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again", "ewe": "ewe: Invalid request, please try again"}}, "menu_retry": {"description": "Invalid menu item retry option", "content": {"eng": "Retry", "fra": "Retenter", "ewe": "Retenter"}}, "menu_cancel": {"description": "Invalid menu item cancel option", "content": {"eng": "Cancel", "fra": "Annuler", "ewe": "Annuler"}}, "service_unavailable": {"description": "Remote service is not accessible", "content": {"eng": "Service Unavailable", "fra": "Service Indisponible", "ewe": "Service Indisponible"}}}, "ussd_matches": [{"request": "*53*31#", "body": [{"assign": {"variable": "huxcode", "value": "*107*5#"}}, {"component": {"name": "hux_call", "comptype": "builtin"}}]}, {"request": "*53*50#", "body": [{"assign": {"variable": "huxcode", "value": "*107*4#"}}, {"component": {"name": "hux_call", "comptype": "builtin"}}]}, {"request": "*53*60#", "body": [{"assign": {"variable": "huxcode", "value": "*107*3#"}}, {"component": {"name": "hux_call", "comptype": "builtin"}}]}, {"request": "*53*100#", "body": [{"assign": {"variable": "huxcode", "value": "*107*2#"}}, {"component": {"name": "hux_call", "comptype": "builtin"}}]}, {"request": "*53*120#", "body": [{"assign": {"variable": "huxcode", "value": "*107*1#"}}, {"component": {"name": "hux_call", "comptype": "builtin"}}]}]}