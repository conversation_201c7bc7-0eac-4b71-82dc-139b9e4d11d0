{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"component": {"name": "common_properties", "comptype": "builtin"}}, {"display": {"message_id": "a6dcbd80-f594-11e9-ace9-0716d0badcfa"}}]}}, "messages": {"a6dcbd80-f594-11e9-ace9-0716d0badcfa": {"description": "msg-a6dcbd80-f594-11e9-ace9-0716d0badcfa", "content": {"eng": "User Balance Is %accountValue%\\n", "fra": "User Balance Is %accountValue%\\n", "ewe": "User Balance Is %accountValue%\\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again", "ewe": "ewe: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again", "ewe": "ewe: Invalid request, please try again"}}, "menu_retry": {"description": "Invalid menu item retry option", "content": {"eng": "Retry", "fra": "Retenter", "ewe": "Retenter"}}, "menu_cancel": {"description": "Invalid menu item cancel option", "content": {"eng": "Cancel", "fra": "Annuler", "ewe": "Annuler"}}, "service_unavailable": {"description": "Remote service is not accessible", "content": {"eng": "Service Unavailable", "fra": "Service Indisponible", "ewe": "Service Indisponible"}}}, "ussd_matches": [{"request": "*123#", "body": [{"assign": {"variable": "huxcode", "value": "*107*1#"}}, {"assign": {"variable": "enableAutoRespond", "value": "true"}}, {"assign": {"variable": "autoRespondValue", "value": "1"}}, {"component": {"name": "hux_component_test", "comptype": "custom"}}]}]}