syntax = "proto3"; 

service Debugger {
  rpc run (RunRequest) returns (RunResponse) {}
  rpc command(CommandRequest) returns (CommandResponse) {}
}

message RunRequest {
	int32 id = 1;
	string msisdn = 2;
	string imsi = 3;
	string config = 4;
	string lang = 5;
}

message RunResponse {
	int32 id = 1;
	int32 status = 2;
}

message CommandRequest {
	enum Command {
		CONTINUE = 0;
		STEP = 1;
		BREAKPOINT = 3;
	}

	int32 id = 1;
	Command command = 2;
	string input = 3;
	string vars_set = 4;
	string vars_clear = 5; // applies 'clear' after 'set'
}

message CommandResponse {
	bool done = 1;
	bool response = 2;
	string output = 3;
	string vars = 4;
	string node_id = 5;
}