## Helm Notes

Although it is possible, apparently it's bad prsctice to specify the namespace inside a values file. Seems it should be on the command line. If you do, `helm list` gives wrong output, which is appaently not a bug, see https://github.com/helm/helm/issues/5465

## Running Helm

1. Make sure k<PERSON><PERSON><PERSON> is working properly
2. Run Helm Upgrade with:
```
helm upgrade --install -f ./menuserver/values.yaml -f ./menuserver/values_qa.yaml qa ./menuserver
```
The above example overrides the default installation values and uses the qa file.

helm upgrade --install -f ./menuserver/values_qa.yaml qa ./menuserver


helm template --debug development ./coalesce --set registry.version=1.3.5
export VERSION=1.2.56
helm template --debug development ./coalesce --set registry.version=$VERSION

## Full test with
export VERSION=1.2.56
export IMAGE=coalesce-menu-server

helm upgrade -n cs-core-dev --install development charts/coalesce \
          --set registry.version=${VERSION} \
          --set registry.image=${IMAGE} \
          --set registry.githubRegistryKey=${{secrets.DOCKER_CONFIG_JSON}}
