# Default values for menuserver.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

registry:
  # TODO
  # use CLI to override this variable
  # in GItHub Action, set it using DOCKER_CONFIG_JSON environment variab;le
  githubRegistryKey: "nothinghere"
  githubRegistrySecretName: "github-registry-key-menu"
  githubRegistryUrl: "ghcr.io/concurrent-systems"
  image: "coalesce-menu-server"
  version: "latest"

labels:
  app: coalesce-menu-server
  release: coalesce-menu-server
  tier: web
  track: stable

selectorLabels:
  app: coalesce-menu-server
  release: coalesce-menu-server
  tier: web
  track: stable

replicaCount: 1

strategyRollingUpdate:
  maxSurge: 1
  maxUnavailable: 1

environmentVariables:
  CS_CUSTOMER_NAME: cs
  CS_ENVIRONMENT_NAME: dev
  CS_CONFIG_PEERS: http://internal.coalescelab.com:2379,http://internal.coalescelab.com:2479,http://internal.coalescelab.com:2579
  CS_DATA_DIR: /data
  CS_MENU_ENVIRONMENT: previewer
  ELASTIC_URL: http://elasticsearch.internal.coalescelab.com:9200
  ELASTIC_USERNAME: elastic
  ELASTIC_PASSWORD: changeme
  STAGING_NAMESPACE: cs-menu-dev-staging
  PROD_NAMESPACE: cs-menu-dev-prod

configMapRef:
  name: language-map

image:
  repository: ghcr.io/concurrent-systems
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

ports:
  name: web
  containerPort: 5000

probes:
  # common defaults for probes
  failureThreshold: 3
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 15
  initialDelaySeconds: 10
  # specific values (Same names as defaults above, but will override the default) for liveness and readiness
  readiness:
    timeoutSeconds: 3
    initialDelaySeconds: 5
  liveness:
    timeoutSeconds: 15
    initialDelaySeconds: 15
imagePullSecrets: []
nameOverride: ""
fullnameOverride: "coalesce-menu-server"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  className: ""
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resourceManagement:
  enabled: false
  resources:
    {}
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
