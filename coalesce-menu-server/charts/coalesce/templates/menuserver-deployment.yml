apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "menuserver.fullname" . }}
  labels:
    {{- include "menuserver.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "menuserver.selectorLabels" . | trim | nindent 6 }}
  strategy:
    rollingUpdate:
      maxSurge: {{ .Values.strategyRollingUpdate.maxSurge | default 1 }}
      maxUnavailable: {{ .Values.strategyRollingUpdate.maxUnavailable | default 1 }}
    type: RollingUpdate
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "menuserver.selectorLabels" . | trim | nindent 8 }}
    spec:
      restartPolicy: Always
      imagePullSecrets:
        - name: {{ .Values.registry.githubRegistrySecretName }}
      containers:
      - name: {{ .Chart.Name }}
        #image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
        #image: ${APP_DOCKER_REGISTRY}/coalesce/coalesce-menu-server:${APP_MENU_SERVER_VERSION}
        #image: ${APP_DOCKER_REGISTRY}/coalesce/coalesce-menu-server:build-14
        #image: ghcr.io/concurrent-systems/coalesce-menuserver:build-14
        #image: us-east1-docker.pkg.dev/coalesce-studio/concurrent-test/coalesce/coalesce-menuserver:build-14
        #image: us-east1-docker.pkg.dev/coalesce-studio/concurrent-test/coalesce/coalesce-menuserver:latest
        image: "{{- .Values.registry.image }}:{{- .Values.registry.version }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- if not .Values.autoscaling.enabled }}
        resources:
          {{- toYaml .Values.resourceManagement.resources | nindent 10 }}
        {{- end }}
        livenessProbe:
          failureThreshold: {{ .Values.probes.failureThreshold }}
          httpGet:
            path: /
            port: {{ .Values.ports.containerPort }}
            scheme: HTTP
          initialDelaySeconds: {{ .Values.probes.liveness.initialDelaySeconds | default .Values.probes.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.liveness.periodSeconds | default .Values.probes.periodSeconds }}
          successThreshold: {{ .Values.probes.liveness.successThreshold | default .Values.probes.successThreshold }}
          timeoutSeconds: {{ .Values.probes.liveness.timeoutSeconds | default .Values.probes.timeoutSeconds }}
        readinessProbe:
          failureThreshold: {{ .Values.probes.failureThreshold }}
          httpGet:
            path: /
            port: {{ .Values.ports.containerPort }}
            scheme: HTTP
          initialDelaySeconds: {{ .Values.probes.readiness.initialDelaySeconds | default .Values.probes.initialDelaySeconds }}
          periodSeconds: {{ .Values.probes.readiness.periodSeconds | default .Values.probes.periodSeconds }}
          successThreshold: {{ .Values.probes.readiness.successThreshold | default .Values.probes.successThreshold }}
          timeoutSeconds: {{ .Values.probes.readiness.timeoutSeconds | default .Values.probes.timeoutSeconds }}
        env:
        {{- include "menuserver.environmentVariables" . | trim | nindent 8 }}
        envFrom:
          - configMapRef:
              name: language-map
        ports:
          - containerPort:  {{ .Values.ports.containerPort }}
            name:  web
            protocol: TCP
      volumes:
      - emptyDir: {}
        name: data-volume
