## last edit : 2022-10-04

ELASTIC_URL=http://127.0.0.1:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=changeme

# Use 'previewer' if using the GUI to send requests to the Menu Server
# Use 'preprod' if sending USSD HUX requests directly to the Menu Server
CS_MENU_ENVIRONMENT=previewer

# This environment is used in ETCD as a prefix to configuration variables like log level, etc
# CS_ENVIRONMENT_NAME options:    development | local-dev
CS_ENVIRONMENT_NAME=development

# Time in seconds -- 10 is good, but the startup time will be extended by this
WAIT_FOR_ISTIO_PROXY=10

# Kubernetes variables
#NAMESPACE=cs-menu-dev-staging
#CS_CUSTOMER_NAME=cs

# ETCD -- likely not needed
#CS_CONFIG_PEERS=http://localhost:2379

# GUI Previewer uses this port when running in previewer mode
DEBUGGER_PORT=5011

# Menu server has an HTTP port ??
#PORT=8084

ROARR_LOG=true

