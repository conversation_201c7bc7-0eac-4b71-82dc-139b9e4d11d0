FROM node:14.17.1-alpine3.11

# Change working directory
WORKDIR "/app"

RUN apk add --update \
	  curl \
    python \
    make \
    g++ 

# Install npm production packages
COPY package*.json /app/

RUN npm ci --production

COPY . /app
RUN cd /app; rm -rf .env .git .vscode;
RUN cd /app; if [ -f .env.ci ]; then cat .env.ci >> .env; fi;

ENV NODE_PATH=/usr/lib/node_modules/npm/node_modules:/app/common
ENV NODE_ENV production
ENV NODE_OPTIONS=--max_old_space_size=8192
#ENV HOSTNAME csys-products-coalesce-studio-coalesce-menu-server-staging.coalescelab.com

ENV PORT 5000
EXPOSE 5000

ENV DEBUGGER_FORK 1
ENV DEFAULT_LANGUAGE 'eng'
ENV DEBUGGER_PORT 5001
EXPOSE 5001

USER node

CMD ["npm", "start"]