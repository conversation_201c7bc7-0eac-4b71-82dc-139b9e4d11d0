.auto-push:
  image: "registry.gitlab.com/gitlab-org/cluster-integration/auto-build-image/master:stable"
  services:
    - docker:19.03.5-dind
  script:
    - |
      if [[ -z "$CI_COMMIT_TAG" ]]; then
        export CI_APPLICATION_REPOSITORY=${CI_APPLICATION_REPOSITORY:-$CI_REGISTRY_IMAGE/$CI_COMMIT_REF_SLUG}
        export CI_APPLICATION_TAG=${CI_APPLICATION_TAG:-$CI_COMMIT_SHA}
      else
        export CI_APPLICATION_REPOSITORY=${CI_APPLICATION_REPOSITORY:-$CI_REGISTRY_IMAGE}
        export CI_APPLICATION_TAG=${CI_APPLICATION_TAG:-$CI_COMMIT_TAG}
      fi

      if ! docker info &>/dev/null; then
        if [ -z "$DOCKER_HOST" ] && [ "$KUBERNETES_PORT" ]; then
         export DOCKER_HOST='tcp://localhost:2375'
        fi
      fi

      if [[ -n "$CI_REGISTRY" && -n "$CI_REGISTRY_USER" ]]; then
        echo "Logging to GitLab Container Registry with CI credentials..."
        docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
      fi

      echo "Gitlab docker image: $CI_APPLICATION_REPOSITORY:$CI_APPLICATION_TAG"
      echo "Coalesce docker image: $COALESCE_DOCKER_REGISTRY/$CI_PROJECT_NAME:latest-$CI_ENVIRONMENT_NAME"

      docker image pull "$CI_APPLICATION_REPOSITORY:$CI_APPLICATION_TAG" 

      docker image tag "$CI_APPLICATION_REPOSITORY:$CI_APPLICATION_TAG" "$COALESCE_DOCKER_REGISTRY/$CI_PROJECT_NAME:latest-$CI_ENVIRONMENT_NAME" 
      docker image push "$COALESCE_DOCKER_REGISTRY/$CI_PROJECT_NAME:latest-$CI_ENVIRONMENT_NAME" 
  cache: {}

Push Dev Registry Image:
  extends: .auto-push
  stage: push_image_dev
  environment:
    name: development 
    kubernetes:
      namespace: cs-core-dev
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

Push QA Registry Image:
  extends: .auto-push
  stage: push_image_qa
  environment:
    name: qa 
    kubernetes:
      namespace: cs-core-qa
  rules:
    - if: '$CI_COMMIT_BRANCH == "qa"'

Push Staging Registry Image:
  extends: .auto-push
  stage: push_image_staging
  environment:
    name: staging 
    kubernetes:
      namespace: cs-core-staging
  rules:
    - if: '$CI_COMMIT_BRANCH == "qa"'

Push Pre-Production Registry Image:
  extends: .auto-push
  stage: push_image_preprod
  environment:
    name: preprod 
    kubernetes:
      namespace: cs-core-preprod
  rules:
    - if: '$CI_COMMIT_TAG'

Push Production Registry Image:
  extends: .auto-push
  stage: push_image_production
  environment:
    name: production 
    kubernetes:
      namespace: cs-core-prod
  rules:
    - if: '$CI_COMMIT_TAG'
