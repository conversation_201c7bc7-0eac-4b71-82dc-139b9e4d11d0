## Menu Server Change Log for branch: CU-chp0e7_Menu-Server-connection-handling

### Features

A mechanism was introduced where multiple connections for an endpoint of a connector can be configured for a _round robin on failure_ recovery mechanism. This mechanism is only fully implemented for connections towards AIR at the moment but it could be extended to connections towards other systems as well.

This does not affect Menu server running in "Previewer" mode. It will only ever use the preprod connection in the first position, no failover will occur.

On a communication failure towards AIR the mechanism will:

1. Fail the current transaction as usual
2. Cycle the connection index for subsequent air connections to the next value, or back to the first if it was on the last already.

_NOTE:_ Connections are not marked stale, or taken out of the pool, and will be cycled back to if failures keep occurring on all the connections in the pool.

# Version 0.4

## Menu Server Change Log - (versions 0.3 to 0.4)

### Technical Changes

- **CRITICAL:** All common libraries (ucip, crediverse, etc) have moved to the sub-directory `@coalesce`. Therefore, any _require_ reference for ANY CAD library must be updated (E.G. `require('ucip')` will become `require('@coalesce/ucip')`).
- **IMPORTANT:** the `smsc` library has been renamed to the 'corrected' version `smpp`. Thus when imported, the _require_ should change to `require('@coalesce/smpp')`
- **IMPORTANT:** the `dmenu` library has been renamed for clarity to `dynamicmenu`. Thus when imported, the _require_ should change to `require('@coalesce/dynamicmenu')`

### Features

- WIP: UCIP Endpoints now have multiple connections (pool of connections). All requests will use the first connection in the pool until it fails, then handover to the next. Handovers will only occur for failures, no roundrobin, no load balancing.

<br />
<br />
<br />
<br />

# Menu Server Change Log - (versions 1.2.3 to 0.3)

## Preliminary Notes

All modules will need to be PUSH/LAUNCH'ed.

### Technical Changes

- Updated to Node 14
- `Roarr` Library has been upgraded to the latest version. This lirbary writes msisdn and TID in logs.
- Extensive Unit tests added for testing menu server libraries
- Improved Error/Exception handling across all libraries. Includes endpoint checks, failure handling, logging improvements
- Hot Config: We can now adjust log levels on the fly, using ETCD keys
- We now Encode URLs in all libraries for the `path` parameter provided by the GUI endpoint configuration
- Crediverse library now uses "connection pooling" instead of replacing the token on a timer
- **IMPORTANT:** SMSC Library: Updated parameters to sendSms(...) method, they are now: `{ subscriberNumber: "", smsText: "" }`
- **IMPORTANT:** Crediverse Library: Error handling requires change to CAD components, see crediverse component reference [70 Product/71 CoaleSCE/CoaleSCE Crediverse Library Reference.md]

### Features

- Transaction ID added to all logs
- Hux Library added, allowing CAD to manage Hux requests/responses inside components

### Bug Fixes

- A failing cai3g login attempt (ECONNRESET or similar) will no longer HANG the connection in a crash-loop
- Menu server crashes when memory limits reached. Added new environment variable in Dockerfile to configure nodejs-heap-memory limit, new default limit is set to 8GB i.e. `(NODE_OPTIONS=--max_old_space_size=8192)`.
- **IMPORTANT:** Components that fail (throw errors) will STOP processing the menu module (Previously the menu would continue with errors stored in `vars.result`). NOTE: The CAD can wrap component code in try/catch to handle errors themselves.
