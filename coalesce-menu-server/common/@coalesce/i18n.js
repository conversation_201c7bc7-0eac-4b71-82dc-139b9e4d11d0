const { getLanguageMap } = require('@coalesce/utils');

const languageMap = getLanguageMap();

const defaultLanguage = languageMap[0];

console.debug('Language Map ... ', languageMap);
console.debug('Default Language ... ', defaultLanguage);

/**
 * Internationalization Class - allows retrieval of languages based on the languageMap provided in the environment
 * @class
 * @from
 */
class I18n {
	/**
	 * @param {string} lang (Optional) Will be the language retrieved from textData
	 */
	constructor(lang) {
		/**
		 * @private
		 */
		this.lang = lang || 0;
	}

	/**
	 * @param {string|number} lang
	 */
	set lang(lang) {
		delete this._lang;
		delete this._langInt;

		// Is valid INTEGER for language ?
		const intData = parseInt(lang, 10);
		if (!isNaN(intData) && [1,2,3,4].includes(intData)) {
				this._langInt = intData;
				this._lang = languageMap[intData];
		}

		// Is valid STRING for language ?
		let languageInt = languageMap.findIndex((mappedLang, idx) => {
			return String(lang).toUpperCase() === mappedLang;
		});
		if (languageInt !== -1) {
			this._langInt = languageInt === 0 ? 1 : languageInt; // if default, intentionally use 1 ... converted later
			this._lang = languageMap[languageInt];
		}

		if (typeof this._langInt === 'undefined') {
			this._langInt = 1;
		}
		if (!this._lang) {
			this._lang = languageMap[defaultLanguage];
		}

		if (typeof this._langInt === 'undefined' || !this._lang) {
			console.warn('Tried to normalise without language setting, using default ... Set langauge by assigning to <class>.lang, or in the constructor(lang)');
		}
	}

	/**
	 * @param {(object|array|string)} text keyed object, array, or string
	 * @return {string} Will return the textData entry that matches the 'lang' setting, default if invalid
	 */
	from(text, lang) {
		const textData = text;
		if (lang) {
			this.lang = lang;
		}
		if (!this._lang) {
			this.lang = null; // By 'setting', it forces a log entry advising DEFAULT is being used (also sets appropriate defaults in other needed private class variables)
		}

		switch(true) {
			case Array.isArray(textData) && textData.length > 0:
				if (!textData[this._langInt-1]) {
					console.warn('language does not exist in mapping, printing first position in array');
				}

				return textData[this._langInt-1] || textData[0];

			case typeof textData === 'object' && Object.keys(textData).length > 0:
				const newTextData = {};
				// Capitalize all KEYS
				Object.keys(textData).forEach(key => {
					const upperKey = String(key).toUpperCase();
					newTextData[upperKey] = textData[key];
				});

				if (!newTextData[this._lang]) {
					console.warn('language does not exist in mapping, printing first position in object');
				}

				return newTextData[this._lang] || newTextData[defaultLanguage];

			default:
				if (typeof textData === 'string' || textData !== '') {
					return textData;
				}

				console.warn('i18n :: Empty/Invalid textData, returning blank :/ -- Data ?', textData);
				return '';

		}
	}
}

module.exports = I18n