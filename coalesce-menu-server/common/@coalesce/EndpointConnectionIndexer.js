class EndpointConnectionIndex {
  constructor(nConnections) {
    this._currentIndex = 0;
    this._nConnections= nConnections;
  }
  
  cycle() {
    this._currentIndex++;

    if (this._currentIndex >= this._nConnections) {
      this._currentIndex = 0;
    }
  }

  getCurrentIndex() {
    return this._currentIndex;
  }
};

class EndpointConnectionIndexer {
  constructor () {
    this._connectionIndexes = new Map();
  }

  cycleConnectionIndex(key) {
    let connectionIndex = this._connectionIndexes.get(key);

    if (connectionIndex instanceof EndpointConnectionIndex) {
      connectionIndex.cycle();
      console.info("EndpointConnectionIndexer cycled " + key + " to the next configured connection.");
    }
  }

  getConnectionIndex(key) {
    const connectionIndex = this._connectionIndexes.get(key);

    if (connectionIndex instanceof EndpointConnectionIndex)
    {
      return connectionIndex.getCurrentIndex();
    } 
    return 0;
  }
   
  createConnectionIndex(key,nConnections){
    this._connectionIndexes.set(key,new EndpointConnectionIndex(nConnections));
  }


  flattenConnectionArray(connectors) {

    let flattenedConnectors = JSON.parse(JSON.stringify(connectors)); 

    for (const [connectorType,connector] of Object.entries(connectors)) {

      flattenedConnectors[connectorType] = {};

      for (const [key, endpoints] of Object.entries(connector)) {
        const endpointConnectionIndex = this.getConnectionIndex(connectorType+"."+key);
        flattenedConnectors[connectorType][key] = endpoints[endpointConnectionIndex];
      }
    }

    return flattenedConnectors;
  }
}

const endpointConnectionIndexer = new EndpointConnectionIndexer();
Object.freeze(endpointConnectionIndexer);

module.exports = endpointConnectionIndexer;

