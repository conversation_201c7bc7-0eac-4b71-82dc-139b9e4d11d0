const parser = require("fast-xml-parser");
const encoder = new parser.j2xParser(/*{ indentBy: '  ', format: true  }*/ { trimValues: false });
const util = require("util");
const CoalesceError = require("@coalesce/coalesceError");

const languageEncodingMap = {
  ENG: "LATN",
  FRA: "LATN",
  EWE: "LATN",
};

class InvalidXmlRpcRequestException extends CoalesceError {
  constructor(message, code) {
    super(InvalidXmlRpcRequestException, null, message);
    this.code = code;
  }
}

function collectMembers(a) {
  //console.debug(a);

  let params = {};
  if (Array.isArray(a)) {
    for (const p of a) Object.assign(params, collectMembers(p));
  } else {
    if (typeof a !== "object" || !"name" in a || !"value" in a) {
      console.debug("Invalid XML members (as json): ", a);
      throw new InvalidXmlRpcRequestException("Invalid member in struct");
    }

    params[a.name] = collectMember(a.value);
  }

  return params;
}

function collectMember(v) {
  //console.debug(v);

  if ("string" in v) return v.string;

  if ("i4" in v || "int" in v) {
    let i = v.i4 || v.int;
    i = parseInt(i, 10);
    if (isNaN(i)) {
      console.debug("Invalid XML member (as json):", v);
      throw new InvalidXmlRpcRequestException("Invlid integer in struct member", -170);
    }
    return i;
  }

  if ("array" in v) {
    const value = v.array?.data?.value;
    if (!value) return [];

    return Array.isArray(value) ? value.map((i) => collectMember(i)) : [collectMember(value)];
  }

  if ("struct" in v) return v.struct?.member ? collectMembers(v.struct.member) : {};

  if ("dateTime.iso8601" in v) return fromISOString(v["dateTime.iso8601"]);

  if ("boolean" in v) return Boolean(v.boolean);

  console.debug("Invalid XML member (as json):", v);
  throw new InvalidXmlRpcRequestException("Invalid struct member field type", -180);
}

function toMembers(r) {
  let members = [];
  for (const k in r) {
    const v = r[k];
    //console.debug('toMembers() :: ' + k + ': ' + typeof v);
    members.push({ name: k, value: toMember(v) });
  }

  return members;
}

function toMember(v) {
  if (typeof v === "string") return { string: v };
  else if (typeof v === "boolean") return { boolean: v ? 1 : 0 };
  else if (typeof v === "number") return { i4: v };
  else if (Array.isArray(v))
    return v.length > 0 ? { array: { data: { value: v.map((i) => toMember(i)) } } } : { array: { data: {} } };
  else if (v instanceof Date) return { "dateTime.iso8601": toISOString(v) };

  // object
  return { struct: { member: toMembers(v) } };
}

function toISOString(d) {
  //return d.toISOString().replace(/-/g, '');
  //return dateformat(d, "yyyymmdd'T'HH:MM:ss+0000");
  function pad(i) {
    return i < 10 ? "0" + i : String(i);
  }

  return util.format(
    "%d%s%sT%s:%s:%s+0000",
    d.getUTCFullYear(),
    pad(d.getUTCMonth() + 1),
    pad(d.getUTCDate()),
    pad(d.getUTCHours()),
    pad(d.getUTCMinutes()),
    pad(d.getUTCSeconds())
  );
}

function fromISOString(s) {
  const m = s.match(/(\d{4})(\d{2})(\d{2})T(\d{2}):(\d{2}):(\d{2})/);
  return new Date(Date.UTC(m[1], m[2] - 1, m[3], m[4], m[5], m[6]));
}

function parseRequest(xml) {
  const obj = parser.parse(xml, { ignoreAttributes: true, parseNodeValue: false });
  return normalizeXmlObject(obj);
}

function normalizeXmlObject(obj) {
  console.debug("Begin normalizing XML Object: ", JSON.stringify(obj));
  const method = obj?.methodCall?.methodName;
  const structMember = obj?.methodCall?.params?.param?.value?.struct?.member;
  if (typeof method === "undefined" || typeof structMember === "undefined") {
    console.debug("Invalid XML (as json): ", obj);
    throw new InvalidXmlRpcRequestException("Missing methodName or structMembers");
  }
  const members = collectMembers(structMember);

  return { method, members };
}

var fieldsAreValid = function (o, method, required_fields) {
  const errors = [];

  if (typeof o.method === "string") {
    if (o.method !== method) {
      errors.push("Expected method '" + method + "' but got '" + o.method + "'");
    }
  } else errors.push("Expected method '" + method + "' but no method was specified");

  const { members } = o;

  if (!members) errors.push("No members defined");

  if (errors.length > 0) {
    console.debug("XML fields invalid (1):", o);
    throw new InvalidXmlRpcRequestException(errors.join(", "), -199);
  }

  required_fields.forEach(function (field) {
    if (field in members) {
      if (typeof members[field] === "undefined") {
        errors.push("Mandatory field " + field + " exists, but has no value");
      }
    } else {
      errors.push("Mandatory field " + field + " is missing");
    }
  });

  if (errors.length > 0) {
    console.debug("XML fields invalid (2):", o);
    throw new InvalidXmlRpcRequestException(errors.join(", "), -199);
  }

  return true;
};

function parseResponse(xml, headers) {
  const obj = parser.parse(xml, { ignoreAttributes: true, parseNodeValue: false });
  let members = null;

  try {
    if (obj.methodResponse.params) {
      members = collectMembers(obj.methodResponse.params.param.value.struct.member);
    } else if (obj.methodResponse.fault) {
      members = { ...collectMembers(obj.methodResponse.fault.value.struct.member), isXMLRPCFault: true };
    }
  } catch (err) {
    // For when we get back an empty response body or some other odd error
    console.error(err);
    members = {
      responseCode: 9999,
      headers: headers || "No headers provided",
      obj,
    };
  }
  return members;
}

function encodeResponse(o) {
  //console.debug('HuX encode response:\n', o);

  const members = toMembers(o);
  //console.debug('encodeResponse() :: toMembers() :: Members: ', members);
  if (typeof o.language === "string") {
    let encoding = languageEncodingMap[o.language];
    if (typeof encoding === "string") {
      const encodingSelection = {
        name: "encodingSelection",
        value: {
          array: {
            data: {
              value: {
                struct: {
                  member: [
                    { name: "alphabet", value: { string: encoding } },
                    { name: "language", value: { string: o.language } },
                  ],
                },
              },
            },
          },
        },
      };

      members.push(encodingSelection);
    }
  }

  const response = { methodResponse: { params: { param: { value: { struct: { member: members } } } } } };

  return encoder.parse(response);
}

function encodeRequest(method, o) {
  const members = toMembers(o);
  const r = {
    methodCall: { methodName: method, params: { param: { value: { struct: { member: members } } } } },
  };
  return encoder.parse(r);
}

function encodeFaultResponse(o) {
  //console.debug('HuX encode response:\n', o);

  const members = toMembers(o);
  //console.debug('encodeResponse() :: toMembers() :: Members: ', members);
  if (typeof o.language === "string") {
    let encoding = languageEncodingMap[o.language];
    if (typeof encoding === "string") {
      const encodingSelection = {
        name: "encodingSelection",
        value: {
          array: {
            data: {
              value: {
                struct: [
                  { member: { name: "alphabet", value: { string: encoding } } },
                  { member: { name: "language", value: { string: o.language } } },
                ],
              },
            },
          },
        },
      };

      members.push(encodingSelection);
    }
  }

  const response = { methodResponse: { fault: { value: { struct: { member: members } } } } };

  return encoder.parse(response);
}

// Currently incomplete.
/*
	params.subscriberNumberNAI can have the following values according to UCIP spec:
		0 Unknown
		1 International number
		2 National significant number
		3 Network specific number
		4 Subscriber number
		5 Reserved
	Due to site issues (Air gives an internal server error if anything other than 2 is set)
	this method was added to deal with the removal of the international prefix.
*/
function formatMsisdn(params, internationalNumberPrefix) {
  if (
    typeof params.subscriberNumberNAI !== "undefined" &&
    params.subscriberNumberNAI !== null &&
    params.subscriberNumberNAI === 2
  ) {
    if (
      typeof params.subscriberNumber === "string" &&
      typeof internationalNumberPrefix === "string" &&
      params.subscriberNumber.startsWith(internationalNumberPrefix)
    ) {
      params.subscriberNumber = params.subscriberNumber.substr(
        internationalNumberPrefix.length,
        params.subscriberNumber.length - internationalNumberPrefix.length
      );
    }
  }
}

module.exports = {
  normalizeXmlObject,
  parseRequest,
  parse: parseRequest,
  parseResponse,
  encodeResponse,
  encode: encodeResponse,
  encodeRequest,
  formatMsisdn,
  encodeFaultResponse,
  fieldsAreValid,
  InvalidXmlRpcRequestException,
};
