const { EndpointManager } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");
const { Session } = require("./session");

const smppEndpointManager = new EndpointManager("smpp");

class SmppLibraryError extends CoalesceError {
  constructor(...params) {
    super(SmppLibraryError, ...params);
  }
}
const DEFAULT_ENDPOINT_NAME = "smsc";

const setEndpoint = (newName, ctx) => {
  if (!newName) console.warn("Setting to default endpoint: ", DEFAULT_ENDPOINT_NAME);
  smppEndpointManager.setEndpointInContext(newName || DEFAULT_ENDPOINT_NAME, ctx);
};

const sendSms = async (options, ctx) => {
  const { subscriberNumber, smsText, sourceAddress } = options || {};

  if (!subscriberNumber || !smsText) {
    throw new SmppLibraryError(null, "Missing mandatory parameters [subscriberNumber or smsText]");
  }

  const endpointName = smppEndpointManager.getEndpointNameFromContext(ctx);
  const connectorData = smppEndpointManager.getComponentManagerConnectorData(`smpp.${endpointName}`);

  if (!connectorData.session) {
    const endpoint = smppEndpointManager.getComponentManagerEndpoint(endpointName);

    connectorData.session = new Session(endpointName, endpoint);
  }

  session = connectorData.session;

  const sendSmsParams = {
    msg: smsText, 
    source: sourceAddress || connectorData.session.config[0].defaultSourceAddress,
    destination:subscriberNumber
  }
  return session.sendSms(sendSmsParams);
};

module.exports = {
  setEndpoint,
  sendSms,
};
