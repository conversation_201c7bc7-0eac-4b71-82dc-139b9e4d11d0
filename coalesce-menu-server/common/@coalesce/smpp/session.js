const smpp = require("smpp");

const { deepEqual } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");
const { ENDPOINTS_ETCD_KEY } = require("@coalesce/env");
const { getHotConfig } = require("../../../server/HotConfig");

class SMPPSessionException extends CoalesceError {
  constructor(...params) {
    super(SMPPSessionException, ...params);
  }
}

const SMPP_TON = {
  UNKNOWN: 0
};

const SMPP_NPI = {
  UNKNOWN: 0,
  ISDN: 1 // for mobile numbers
}

class Session {
  constructor(name, config) {
    this.config = { ...config };

    this._endpointName = name;
    this._hotConfigWatcherInitialized = false;
  }

  async _startHotConfigWatcher() {
    this._hotConfigWatcherInitialized = true;
    await getHotConfig().createAndWatch(ENDPOINTS_ETCD_KEY, null, (updatedConnectorsString) => {
      try {
        const connectors = JSON.parse(updatedConnectorsString);

        /**
         * FIXME: We use only the FIRST connection in the array
         *  Once we have connection pooling... we must update this to use all connections);
         */
        const c = connectors.smpp[this._endpointName][0];
        /**
         *
         */

        if (!deepEqual(this.config, c)) {
          this.config = { ...c };

          if (this.connection) this.connection.close();

          delete this.connection;
        }
      } catch (e) {
        // This usually happens while updating smpp connector-endpoint
        const exception = new SMPPSessionException(
          e,
          `SMPP library error in Session class HotConfig.watch callback(...) failed`
        );
        console.error(exception);
      }
    });
  }

  async sendSms({msg, source, destination }) {
    if (!this.connection) {
      if (!this._hotConfigWatcherInitialized) {
        await this._startHotConfigWatcher();
      }
      await this.connect();
    }

    return this.sendSubmitSm({msg, source, destination });
  }

  connect() {
    return new Promise((resolve, reject) => {
      const config = this.config;

      console.debug("SMPP connection to", config);

      const connection = smpp.connect({
        url: `smpp://${config.hostname}:${config.port}`,
        auto_enquire_link_period: 10000, // ???
      });

      this.connection = connection;

      this.connection.bind_transmitter(
        {
          system_id: config.auth.system_id,
          password: config.auth.password,
        },
        (res) => {
          if (res.command_status != 0) {
            let error = new SMPPSessionException(
              null,
              `SMPP connector failed to connect: ${this._getError(res.command_status)}`
            );
            console.error("SMPP connection error", error);
            return reject(error);
          }

          this.connection.on("close", () => {
            console.debug("SMPP connection closed");
            delete this.connection;
          });

          return resolve();
        }
      );

      this.connection.on("error", (e) => {
        delete this.connection;

        let error = new SMPPSessionException(e, `SMPP connection error`);
        console.error("SMPP connection error", error);
        reject(error);
      });
    });
  }

  sendSubmitSm({msg, source, destination }) {
    return new Promise((resolve, reject) => {
      //console.debug('SENDING SMS');

      const encoding = smpp.encodings.detect(msg);
      let maxsz = 255;

      switch (encoding) {
        case "ASCII":
          maxsz += Math.floor(maxsz / 8);
          break;

        case "UCS2":
          maxsz = Math.floor(maxsz / 2);
          break;
      }

      const request = {
        destination_addr: destination,
        data_coding: encoding,
      };

      if (source) {
        request.source_addr_ton = SMPP_TON.UNKNOWN;
        request.source_addr_npi = SMPP_NPI.UNKNOWN;
        request.source_addr = String(source).trim();

        const sourceIsNumeric = !!request.source_addr.match(/^[0-9]+$/);
        const sourceHasEnoughCharacters = request.source_addr.length >= 5;

        if (sourceIsNumeric && sourceHasEnoughCharacters) {
          request.source_addr_npi = SMPP_NPI.ISDN // ISDN is for mobile numbers
        }
      }

      if (msg.length <= 255) {
        request.sm_length = msg.length;
        request.short_message = msg;
      } else {
        /**
         * When using message_payload, the sm_length must be 0
         * See point 4.7.28 --- https://smpp.org/SMPP_v5.pdf#page=77&zoom=100,166,530
         */
        request.sm_length = 0;
        request.message_payload = msg;
      }

      this.connection.submit_sm(request, function (res) {
        //console.trace('SMPP send response', res);

        if (res.command_status == 0) {
          console.debug("sendSms(...) completed succesfully, SMS information sent: ", request);
          return resolve({ ok: true, response: res, message: "message sent successfully" });
        }

        let error = new SMPPSessionException(res, `SMPP submit_sm failed`);
        console.error("SMPP connection error", error);
        reject(error);
      });
    });
  }

  _getError(status) {
    switch (status) {
      case smpp.ESME_RBINDFAIL:
        return "Bind Failed";

      case smpp.ESME_RINVPASWD:
        return "Invalid Password";

      case smpp.ESME_RINVSYSID:
        return "Invalid System ID";

      case smpp.ESME_RSUBMITFAIL:
        return "submit_sm or submit_multi failed";
    }

    return "Unknown err: " + status;
  }
}

module.exports = { Session };
