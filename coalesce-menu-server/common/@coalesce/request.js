const axios = require("axios");
const axiosRetry = require("axios-retry");
const http = require("http");
const https = require("https");
const CoalesceError = require("@coalesce/coalesceError");

/**
 * Because some programmers who write OTHER SOFTWARE are LAZY...
 *  they include only the HTTP Status, WITHOUT the statusText ...
 *
 *  That means we have a nice MAPPING of these texts here,
 *  in case these other devs are so _INSERT-SWEAR-WORD-HERE_ lazy that they can't include it with their HTTP responses
 */
const HTTP_RESPONSES = {
  302: { code: 302, statusText: "Found" },
  400: { code: 400, statusText: "Bad Request" },
  401: { code: 401, statusText: "Unauthorized" },
  403: { code: 403, statusText: "Forbidden" },
  404: { code: 404, statusText: "Not Found" },
  500: { code: 500, statusText: "Internal Server Error" },
  501: { code: 501, statusText: "Not Implemented" },
};

class ComponentRequestError extends CoalesceError {
  constructor(httpError, ...params) {
    super(ComponentRequestError, httpError, ...params);
    this.httpError = httpError;
    this.isAxiosError = !!httpError?.isAxiosError;
    this.response = httpError?.response || {};
    this.code = httpError?.code;
    this.status = this.response?.status;
    this.statusText = this.response?.statusText;
  }

  getErrorDescription() {
    const { status, statusText } = this.response;
    if (status && statusText) {
      return `${status}:${statusText}`;
    } else {
      if (status && Object.keys(HTTP_RESPONSES).includes(status)) {
        return `${status}:${HTTP_RESPONSES[status].statusText}`
      }
      return this.status || this.code || "Unknown HTTP Error";
    }
  }
}

let axiosInstance = null;

let axiosConfig = {
  transitional: {
    clarifyTimeoutError: true,
  },
};

const getAxiosInstance = () => {
  if (axiosInstance === null) {
    console.info("Initializing Axios instance");
    if (!process.env.AXIOS_NO_KEEP_ALIVE) {
      console.debug("axios keepAlive - enabled");
      axiosConfig = {
        httpAgent: new http.Agent({ keepAlive: true }),
        httpsAgent: new https.Agent({ keepAlive: true }),
        ...axiosConfig,
      };
    } else {
      console.debug("axios keepAlive - disabled");
    }

    axiosInstance = axios.create(axiosConfig);
  }
  return axiosInstance;
};

// all in milliseconds
const DEFAULT_REQUEST_TIMEOUT = 2000;
const MIN_REQUEST_TIMEOUT = 500;
const MAX_REQUEST_TIMEOUT = 30000;

function sendRequest(args) {
  return new Promise((resolve, reject) => {
    const { url, body } = args;
    const headers = args.headers || {};
    const timeout = args.timeout || DEFAULT_REQUEST_TIMEOUT;

    getAxiosInstance()
      .post(url, body, { headers, timeout })
      .then((response) => {
        console.debug("HTTP Response Code: ", response.status);
        console.debug("HTTP Response Message: ", response.statusText);
        if (response.status >= 400) {
          reject(
            new ComponentRequestError(response, "== Axios Request Error ==")
          );
        } else {
          resolve(response);
        }
      })
      .catch((error) => {
        console.error(
          "== POST REQUEST: Limited Axios Error (see DEBUG level for more) ==",
          error.response
        );
        console.debug("ERROR: == Complete Axios Error ==", error);
        if (error instanceof ComponentRequestError) return reject(error);

        reject(new ComponentRequestError(error, "== Axios Request Error =="));
      });
  });
}

function sendGetRequest(args) {
  return new Promise((resolve, reject) => {
    const { url } = args;
    const headers = args.headers || {};
    const timeout = args.timeout || DEFAULT_REQUEST_TIMEOUT;

    getAxiosInstance()
      .get(url, { headers, timeout })
      .then((response) => {
        console.debug("HTTP Response Code: ", response.status);
        console.debug("HTTP Response Message: ", response.statusText);
        if (response.status > 400) {
          reject(
            new ComponentRequestError(response, "== Axios Request Error ==")
          );
        } else {
          resolve({
            status: response.status,
            statusText: response.statusText,
            ...response.data,
          });
        }
      })
      .catch((error) => {
        console.error(
          "== POST REQUEST: Limited Axios Error (see DEBUG level for more) ==",
          error.response
        );
        console.debug("ERROR: == Complete Axios Error ==", error);
        if (error instanceof ComponentRequestError) return reject(error);

        reject(new ComponentRequestError(error, "== Axios Request Error =="));
      });
  });
}

function getOptions(connector) {
  console.debug("== in getOptions for connector : ", JSON.stringify(connector));

  let headers = {};

  let { auth, timeout } = connector;
  let headerArray = connector.headers;

  headerArray = Array.isArray(headerArray) ? headerArray : [];

  headerArray.forEach((header) => {
    if (["Content-Type", "User-Agent"].includes(header.name)) return;

    headers[header.name] = headers.header.value;
  });

  headers["Content-Type"] = "text/xml";
  headers["User-Agent"] = "UGw Server/5.0/1.0";

  if (typeof auth === "object" && auth.type !== "undefined") {
    switch (auth.type) {
      case "basic":
        headers.Authorization =
          "Basic " +
          Buffer.from(auth.username + ":" + auth.password).toString("base64");
        break;
    }
  }

  timeout = parseInt(timeout, 10);

  if (
    isNaN(timeout) ||
    timeout < MIN_REQUEST_TIMEOUT ||
    timeout > MAX_REQUEST_TIMEOUT
  ) {
    console.warn(
      "timeout invalid or out of bounds, will use default:",
      DEFAULT_REQUEST_TIMEOUT
    );
    timeout = DEFAULT_REQUEST_TIMEOUT;
  }

  // NOTE: 'auth' is encorporated into headers, so not returned
  return { headers, timeout };
}

module.exports = {
  sendRequest,
  sendGetRequest,
  getOptions,
  ComponentRequestError,
  HTTP_RESPONSES,
};
