const parser = require("xml2json");
const crypto = require("crypto");
const axios = require("axios");

const mobileMoneyDebitLib = require("./mobileMoneyDebitLib");
const CoalesceError = require("@coalesce/coalesceError");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");

const mmEndpointManager = new EndpointManager("mobileMoney");

class MobileMoneyError extends CoalesceError {
  constructor(...params) {
    super(MobileMoneyError, ...params);
  }
}
const DEFAULT_ENDPOINT_NAME = "mm";
/*
 * Overwritten by default endpoint .... OR by setEndpoint() method inside a GUI component
 *
 */

let currentEndpointName = DEFAULT_ENDPOINT_NAME;

/*
 * 	generateToken():
 * 	generate auth-token via aes-256-cbc padding algorithm
 *  and encrypt it to base64
 *
 */

const generateToken = (authDetials) => {
  const { key, username, password } = authDetials;

  let secretMessage = `0:${username}:${password}`;

  const initializationVectorLength = 16;

  try {
    // aes 256-bit cipher block chaining (cbc) encryption/decryption
    let cipher = crypto.createCipheriv("aes-256-cbc", key, Buffer.alloc(initializationVectorLength));
    let encryptedToken = cipher.update(secretMessage, "utf-8", "base64");
    encryptedToken += cipher.final("base64");

    console.debug("Encrypted token: " + encryptedToken);

    return encryptedToken;
  } catch (error) {
    let generateTokenError = new MobileMoneyError(error, "mobileMoney generateToken() failed: ");
    console.error("mobileMoney generateToken() failed: ", generateTokenError);
    throw generateTokenError;
  }
};

const setEndpoint = (replacementEndpointName) => {
  currentEndpointName = replacementEndpointName;
};

const setDefaultEndpoint = () => {
  currentEndpointName = DEFAULT_ENDPOINT_NAME;
};

const getEndpointConnectionInfo = (ctx) => {
  let connectionInfo = {};

  if (!currentEndpointName) console.warn("Setting to default endpoint: ", DEFAULT_ENDPOINT_NAME);

  mmEndpointManager.setEndpointInContext(currentEndpointName || DEFAULT_ENDPOINT_NAME, ctx);
  connectionInfo = mmEndpointManager.getEndpointFromContext(ctx);

  return connectionInfo;
};

// send the request to mobileMoney system
const sendGetBalanceRequest = ({ token, msisdn, url, timeout }) => {
  let xml = `
      <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:api="http://api.debit.flooz.com/">
        <soapenv:Header/>
        <soapenv:Body>
            <api:getBalance>
              <token>${token}</token>
              <msisdn>${msisdn}</msisdn>
            </api:getBalance>
        </soapenv:Body>
      </soapenv:Envelope>`;

  console.trace("Sending mm-getBalance Request XML:", xml);

  return new Promise((resolve, reject) => {
    axios
      .post(url, xml, {
        headers: { "content-type": "text/xml" },
        timeout: timeout,
      })
      .then((response) => {
        resolve(response);
      })
      .catch((e) => {
        let error = new MobileMoneyError(e, "mm sendGetBalanceRequest method(axios post request) failed");
        console.error(error);
        reject(error);
      });
  });
};

/*
 * 	getBalance():
 * 	get user's mobile-money balance from mm-server
 *
 */

const getBalance = (ctx, msisdn) => {
  return new Promise(async (resolve, reject) => {
    let responseObj = {};

    try {
      console.info("Start mobileMoney getBalance() method");

      if (!msisdn) throw new MobileMoneyError(null, "Missing msisdn in getBalance(...) request");

      const connectionInfo = getEndpointConnectionInfo(ctx);
      if (!connectionInfo) throw new MobileMoneyError(null, "Connector details missing");

      const { hostname, port, url, authType, authDetails, protocol, timeout } = connectionInfo;

      if (!hostname || !port || !url) throw new MobileMoneyError(null, "Connector hostname/port/url missing");

      // check connector auth
      if (authType === "basic" && (!authDetails.key || !authDetails.username || !authDetails.password)) {
        throw new MobileMoneyError(null, "Unauthorized Error");
      } else if (authType === "oAuth" && !authDetails.token) throw new MobileMoneyError(null, "Unauthorized Error");

      const encodedUriPath = encodeURIPath(String(url).replace(/^\//, ""));

      const fullUrl = `${protocol}://${String(hostname)}:${String(port)}/${encodedUriPath}`;

      // console.debug("connectionInfo:", connectionInfo);
      console.debug("fullUrl:", fullUrl);
      const request = new mobileMoneyDebitLib.getBalance();

      const requestBody = {
        url: fullUrl,
        msisdn,
        token: authType === "basic" ? generateToken(authDetails) : authDetails.token,
        timeout: parseInt(timeout),
      };
      let response = await sendGetBalanceRequest(requestBody);

      console.trace("XML response inside mm-library: ", response.data);

      response = parser.toJson(response.data, { object: true });
      if (response["soap:Envelope"]["soap:Body"]["soap:Fault"]) {
        responseObj = response["soap:Envelope"]["soap:Body"]["soap:Fault"];
      } else {
        responseObj = response["soap:Envelope"]["soap:Body"]["ns2:getBalanceResponse"]["response"];
        responseObj = JSON.parse(responseObj);
      }

      if (!responseObj.faultcode) {
        resolve({ success: true, data: responseObj });
      } else {
        let err = new MobileMoneyError(null, responseObj.faultstring);
        err.status = responseObj.faultcode;
        throw err;
      }
    } catch (e) {
      if (e instanceof MobileMoneyError) return reject(e);

      let error = new MobileMoneyError(e, "mobileMoney getBalance method failed");
      console.error(error);
      reject(error);
    }
  });
};

module.exports = {
  setEndpoint,
  setDefaultEndpoint,
  getBalance,
};
