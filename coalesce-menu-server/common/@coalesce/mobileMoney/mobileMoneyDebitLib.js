// This file is generated by a tool. Don't try to edit it manually!

const axios = require('axios');
const parser = require('fast-xml-parser');

class getBalance {
  get token() {
    return this._token;
  }
  set token(v) {
    if (typeof v != 'string') throw new Error('token must be a string');
    this._token = v;
  }

  get msisdn() {
    return this._msisdn;
  }
  set msisdn(v) {
    if (typeof v != 'string') throw new Error('msisdn must be a string');
    this._msisdn = v;
  }

  encode() {
    let xml = '<tns:getBalance>';
    if (this._token) xml += '<token>' + this._token + '</token>';
    if (this._msisdn) xml += '<msisdn>' + this._msisdn + '</msisdn>';
    xml += '</tns:getBalance>';
    return xml;
  }
}

class getBalanceResponse {
  get response() {
    return this._response;
  }
  set response(v) {
    if (typeof v != 'string') throw new Error('response must be a string');
    this._response = v;
  }

  encode() {
    let xml = '<tns:getBalanceResponse>';
    if (this._response) xml += '<tns:response>' + this._response + '</tns:response>';
    xml += '</tns:getBalanceResponse>';
    return xml;
  }
}

class transferAmount {
  get token() {
    return this._token;
  }
  set token(v) {
    if (typeof v != 'string') throw new Error('token must be a string');
    this._token = v;
  }

  get msisdn() {
    return this._msisdn;
  }
  set msisdn(v) {
    if (typeof v != 'string') throw new Error('msisdn must be a string');
    this._msisdn = v;
  }

  get destmsisdn() {
    return this._destmsisdn;
  }
  set destmsisdn(v) {
    if (typeof v != 'string') throw new Error('destmsisdn must be a string');
    this._destmsisdn = v;
  }

  get amount() {
    return this._amount;
  }
  set amount(v) {
    if (typeof v != 'string') throw new Error('amount must be a string');
    this._amount = v;
  }

  get pin() {
    return this._pin;
  }
  set pin(v) {
    if (typeof v != 'string') throw new Error('pin must be a string');
    this._pin = v;
  }

  get referenceid() {
    return this._referenceid;
  }
  set referenceid(v) {
    if (typeof v != 'string') throw new Error('referenceid must be a string');
    this._referenceid = v;
  }

  get walletid() {
    return this._walletid;
  }
  set walletid(v) {
    if (typeof v != 'string') throw new Error('walletid must be a string');
    this._walletid = v;
  }

  encode() {
    let xml = '<tns:transferAmount>';
    if (this._token) xml += '<tns:token>' + this._token + '</tns:token>';
    if (this._msisdn) xml += '<tns:msisdn>' + this._msisdn + '</tns:msisdn>';
    if (this._destmsisdn) xml += '<tns:destmsisdn>' + this._destmsisdn + '</tns:destmsisdn>';
    if (this._amount) xml += '<tns:amount>' + this._amount + '</tns:amount>';
    if (this._pin) xml += '<tns:pin>' + this._pin + '</tns:pin>';
    if (this._referenceid) xml += '<tns:referenceid>' + this._referenceid + '</tns:referenceid>';
    if (this._walletid) xml += '<tns:walletid>' + this._walletid + '</tns:walletid>';
    xml += '</tns:transferAmount>';
    return xml;
  }
}

class transferAmountResponse {
  get response() {
    return this._response;
  }
  set response(v) {
    if (typeof v != 'string') throw new Error('response must be a string');
    this._response = v;
  }

  encode() {
    let xml = '<tns:transferAmountResponse>';
    if (this._response) xml += '<tns:response>' + this._response + '</tns:response>';
    xml += '</tns:transferAmountResponse>';
    return xml;
  }
}

class sendOTP {
  get token() {
    return this._token;
  }
  set token(v) {
    if (typeof v != 'string') throw new Error('token must be a string');
    this._token = v;
  }

  get msisdn() {
    return this._msisdn;
  }
  set msisdn(v) {
    if (typeof v != 'string') throw new Error('msisdn must be a string');
    this._msisdn = v;
  }

  get amount() {
    return this._amount;
  }
  set amount(v) {
    if (typeof v != 'string') throw new Error('amount must be a string');
    this._amount = v;
  }

  get merchant() {
    return this._merchant;
  }
  set merchant(v) {
    if (typeof v != 'string') throw new Error('merchant must be a string');
    this._merchant = v;
  }

  encode() {
    let xml = '<tns:sendOTP>';
    if (this._token) xml += '<tns:token>' + this._token + '</tns:token>';
    if (this._msisdn) xml += '<tns:msisdn>' + this._msisdn + '</tns:msisdn>';
    if (this._amount) xml += '<tns:amount>' + this._amount + '</tns:amount>';
    if (this._merchant) xml += '<tns:merchant>' + this._merchant + '</tns:merchant>';
    xml += '</tns:sendOTP>';
    return xml;
  }
}

class sendOTPResponse {
  get response() {
    return this._response;
  }
  set response(v) {
    if (typeof v != 'string') throw new Error('response must be a string');
    this._response = v;
  }

  encode() {
    let xml = '<tns:sendOTPResponse>';
    if (this._response) xml += '<tns:response>' + this._response + '</tns:response>';
    xml += '</tns:sendOTPResponse>';
    return xml;
  }
}

async function sendRequest(action, req, headers) {
  let xml =
    '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tns="http://api.debit.flooz.com/">';
  if (headers) xml += headers.encode();
  else xml += '<soap:Header/>';
  xml += '<soap:Body>' + req.encode() + '</soap:Body></soap:Envelope>';

  console.trace('Sending mm-getBalance XML Request:', xml);

  return new Promise((resolve, reject) => {
    axios
      .post(req.url, xml, {
        headers: { SOAPAction: action, 'content-type': 'text/xml' },
        timeout: req.timeout,
      })
      .then((response) => {
        // const r = parser.parse(response.data, { ignoreNameSpace: true });
        // .Envelope;
        resolve(response);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

module.exports = {
  getBalance,
  getBalanceResponse,
  sendGetBalanceRequest: function (req, headers) {
    // if (!req._msisdn || !req._token) throw new Error("missing parameters");
    if (!(req instanceof getBalance)) throw new Error('Invalid request type');
    // if (!(headers instanceof Get.Header)) throw new Error('Invalid headers type')
    return sendRequest('tns#getBalance', req, headers);
  },
};
