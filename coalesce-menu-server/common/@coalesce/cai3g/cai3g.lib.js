// This file is generated by a tool. Don't try to edit it manually!


const axios = require('axios'), parser = require('xml2json');
class CreateEPSMultiSC {
	get imsi() { return this._imsi; }
	set imsi(v) {
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	get associationId() { return this._associationId; }
	set associationId(v) {
		if (typeof v != 'string') throw new Error('associationId must be a string');
		if (v.length > 72) throw new Error('associationId maxLength');
		this._associationId = v;
	}

	get epsProfileId() { return this._epsProfileId; }
	set epsProfileId(v) {
		if (typeof v != 'string') throw new Error('epsProfileId must be a string');
		if (v.length < 1) throw new Error('epsProfileId minLength');
		if (v.length > 255) throw new Error('epsProfileId maxLength');
		this._epsProfileId = v;
	}

	get epsTechnology() { return this._epsTechnology; }
	set epsTechnology(v) {
		if (typeof v != 'string') throw new Error('epsTechnology must be a string');
		if (v.length < 1) throw new Error('epsTechnology minLength');
		if (v.length > 32) throw new Error('epsTechnology maxLength');
		this._epsTechnology = v;
	}

	get nodeType() { return this._nodeType; }
	set nodeType(v) {
		if (typeof v != 'string') throw new Error('nodeType must be a string');
		if (v.length < 3) throw new Error('nodeType minLength');
		if (v.length > 12) throw new Error('nodeType maxLength');
		if (v.match(/^[A-Z]*$/)) {}
		else throw new Error('nodeTypeType pattern');
		this._nodeType = v;
	}

	get akaType() { return this._akaType; }
	set akaType(v) {
		if (typeof v != 'string') throw new Error('akaType must be a string');
		if (v.length < 1) throw new Error('akaType minLength');
		if (v.length > 1) throw new Error('akaType maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('akaTypeType pattern');
		this._akaType = v;
	}

	get epsOdb() { return this._epsOdb; }
	set epsOdb(v) {
		if (typeof v != 'string') throw new Error('epsOdb must be a string');
		if (!['NONE','ODB-ALL','ODB-HPLMN-APN','ODB-VPLMN-APN'].includes(v)) throw new Error('epsOdb enum');
		this._epsOdb = v;
	}

	get epsRoamingAllowed() { return this._epsRoamingAllowed; }
	set epsRoamingAllowed(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingAllowed must be a boolean');
		this._epsRoamingAllowed = v;
	}

	get epsIndividualApnOperatorIdentifierReplacement() { return this._epsIndividualApnOperatorIdentifierReplacement; }
	set epsIndividualApnOperatorIdentifierReplacement(v) {
		if (typeof v != 'string') throw new Error('epsIndividualApnOperatorIdentifierReplacement must be a string');
		if (v.length > 255) throw new Error('epsIndividualApnOperatorIdentifierReplacement maxLength');
		this._epsIndividualApnOperatorIdentifierReplacement = v;
	}

	get epsIndividualDefaultContextId() { return this._epsIndividualDefaultContextId; }
	set epsIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualDefaultContextId must be a integer');
		this._epsIndividualDefaultContextId = v;
	}

	get epsIndividualContextId() { return this._epsIndividualContextId; }
	set epsIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualContextId must be a integer');
		this._epsIndividualContextId = v;
	}

	get epsIndividualSubscribedChargingCharacteristic() { return this._epsIndividualSubscribedChargingCharacteristic; }
	set epsIndividualSubscribedChargingCharacteristic(v) {
		if (typeof v != 'number') throw new Error('epsIndividualSubscribedChargingCharacteristic must be a integer');
		this._epsIndividualSubscribedChargingCharacteristic = v;
	}

	get epsIndividualAmbrMaximalUplinkIpFlow() { return this._epsIndividualAmbrMaximalUplinkIpFlow; }
	set epsIndividualAmbrMaximalUplinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalUplinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalUplinkIpFlow = v;
	}

	get epsIndividualAmbrMaximalDownlinkIpFlow() { return this._epsIndividualAmbrMaximalDownlinkIpFlow; }
	set epsIndividualAmbrMaximalDownlinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalDownlinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalDownlinkIpFlow = v;
	}

	get epsIndividualRatFrequencyPriorityId() { return this._epsIndividualRatFrequencyPriorityId; }
	set epsIndividualRatFrequencyPriorityId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualRatFrequencyPriorityId must be a integer');
		this._epsIndividualRatFrequencyPriorityId = v;
	}

	get epsIndividualMappingContextId() { return this._epsIndividualMappingContextId; }
	set epsIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsIndividualMappingContextId must be a string');
		this._epsIndividualMappingContextId = v;
	}

	get epsRoamingRestriction() { return this._epsRoamingRestriction; }
	set epsRoamingRestriction(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingRestriction must be a boolean');
		this._epsRoamingRestriction = v;
	}

	get epsRegionalRoamingServiceAreaId() { return this._epsRegionalRoamingServiceAreaId; }
	set epsRegionalRoamingServiceAreaId(v) {
		if (typeof v != 'number') throw new Error('epsRegionalRoamingServiceAreaId must be a integer');
		if (v < 1) throw new Error('epsRegionalRoamingServiceAreaId minIclusive');
		if (v > 65535) throw new Error('epsRegionalRoamingServiceAreaId maxIclusive');
		this._epsRegionalRoamingServiceAreaId = v;
	}

	get epsUserIpV4Address() { return this._epsUserIpV4Address; }
	set epsUserIpV4Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV4Address must be a string');
		if (v.match(/^.*[$]((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/)) {}
		else throw new Error('epsUserIpV4AddressType pattern');
		this._epsUserIpV4Address = v;
	}

	get epsUserIpV6Address() { return this._epsUserIpV6Address; }
	set epsUserIpV6Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV6Address must be a string');
		if (v.match(/^.*[$][0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]:(:[0-9A-Fa-f]{1,4}){1,7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,6}(:[0-9A-Fa-f]{1,4}){1}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,5}(:[0-9A-Fa-f]{1,4}){1,2}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1,3}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,4}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,5}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,6}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,7}:([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){6,6}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]::([0-9a-fA-F]{1,4}:){0,5}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,2}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,3}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,4}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){1,5}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else throw new Error('epsUserIpV6AddressType pattern');
		this._epsUserIpV6Address = v;
	}

	get epsTenantId() { return this._epsTenantId; }
	set epsTenantId(v) {
		if (typeof v != 'number') throw new Error('epsTenantId must be a integer');
		if (v < 1) throw new Error('epsTenantId minIclusive');
		if (v > 500) throw new Error('epsTenantId maxIclusive');
		this._epsTenantId = v;
	}

	get epsSessionTransferNumber() { return this._epsSessionTransferNumber; }
	set epsSessionTransferNumber(v) {
		if (typeof v != 'string') throw new Error('epsSessionTransferNumber must be a string');
		if (v.length < 5) throw new Error('epsSessionTransferNumber minLength');
		if (v.length > 15) throw new Error('epsSessionTransferNumber maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsSessionTransferNumberType pattern');
		this._epsSessionTransferNumber = v;
	}

	get epsMultimediaPriorityService() { return this._epsMultimediaPriorityService; }
	set epsMultimediaPriorityService(v) {
		if (typeof v != 'number') throw new Error('epsMultimediaPriorityService must be a integer');
		this._epsMultimediaPriorityService = v;
	}

	get epsZoneCodeSetId() { return this._epsZoneCodeSetId; }
	set epsZoneCodeSetId(v) {
		if (typeof v != 'number') throw new Error('epsZoneCodeSetId must be a integer');
		this._epsZoneCodeSetId = v;
	}

	get epsAaaOdb() { return this._epsAaaOdb; }
	set epsAaaOdb(v) {
		if (typeof v != 'string') throw new Error('epsAaaOdb must be a string');
		if (!['NONE','ODB-ALL'].includes(v)) throw new Error('epsAaaOdb enum');
		this._epsAaaOdb = v;
	}

	get epsAaaIndividualDefaultContextId() { return this._epsAaaIndividualDefaultContextId; }
	set epsAaaIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualDefaultContextId must be a integer');
		this._epsAaaIndividualDefaultContextId = v;
	}

	get epsAaaIndividualContextId() { return this._epsAaaIndividualContextId; }
	set epsAaaIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualContextId must be a integer');
		this._epsAaaIndividualContextId = v;
	}

	get epsAaaIndividualMappingContextId() { return this._epsAaaIndividualMappingContextId; }
	set epsAaaIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsAaaIndividualMappingContextId must be a string');
		this._epsAaaIndividualMappingContextId = v;
	}

	get zoneid() { return this._zoneid; }
	set zoneid(v) {
		if (typeof v != 'number') throw new Error('zoneid must be a integer');
		if (v < 0) throw new Error('zoneid minIclusive');
		if (v > 65535) throw new Error('zoneid maxIclusive');
		this._zoneid = v;
	}

	get epsAdminDisable() { return this._epsAdminDisable; }
	set epsAdminDisable(v) {
		if (typeof v != 'boolean') throw new Error('epsAdminDisable must be a boolean');
		this._epsAdminDisable = v;
	}

	get epsNam() { return this._epsNam; }
	set epsNam(v) {
		if (typeof v != 'number') throw new Error('epsNam must be a integer');
		if (v < 0) throw new Error('epsNam minIclusive');
		if (v > 2) throw new Error('epsNam maxIclusive');
		this._epsNam = v;
	}

	get epsAaaMIP6FeatureVector() { return this._epsAaaMIP6FeatureVector; }
	set epsAaaMIP6FeatureVector(v) {
		if (typeof v != 'string') throw new Error('epsAaaMIP6FeatureVector must be a string');
		if (!['NO_CAPABILITY','MIP6_INTEGRATED','LOCAL_HOME_AGENT_ASSIGNMENT','PMIP6_SUPPORTED','IP4_HOA_SUPPORTED','LOCAL_MAG_ROUTING_SUPPORTED','ASSIGN_LOCAL_IP','MIP4_SUPPORTED','OPTIMIZED_IDLE_MODE_MOBILITY','GTPv2_SUPPORTED'].includes(v)) throw new Error('epsAaaMIP6FeatureVector enum');
		this._epsAaaMIP6FeatureVector = v;
	}

	get epsAccessRestriction() { return this._epsAccessRestriction; }
	set epsAccessRestriction(v) {
		for (const i of ['_epsExtendedAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('epsAccessRestriction must be a string');
		if (!['E-UTRAN-DENIED','NON-3GPP-ACC-DENIED','ALL-DENIED'].includes(v)) throw new Error('epsAccessRestriction enum');
		this._epsAccessRestriction = v;
	}

	get epsExtendedAccessRestriction() { return this._epsExtendedAccessRestriction; }
	set epsExtendedAccessRestriction(v) {
		for (const i of ['_epsAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'number') throw new Error('epsExtendedAccessRestriction must be a integer');
		if (v < 0) throw new Error('epsExtendedAccessRestriction minIclusive');
		if (v > 127) throw new Error('epsExtendedAccessRestriction maxIclusive');
		this._epsExtendedAccessRestriction = v;
	}

	get epsCommonMsisdn() { return this._epsCommonMsisdn; }
	set epsCommonMsisdn(v) {
		for (const i of ['_commonMsisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('epsCommonMsisdn must be a string');
		if (v.length < 5) throw new Error('epsCommonMsisdn minLength');
		if (v.length > 15) throw new Error('epsCommonMsisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsCommonMsisdnType pattern');
		this._epsCommonMsisdn = v;
	}

	get commonMsisdn() { return this._commonMsisdn; }
	set commonMsisdn(v) {
		for (const i of ['_epsCommonMsisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('commonMsisdn must be a string');
		if (v.length < 5) throw new Error('commonMsisdn minLength');
		if (v.length > 15) throw new Error('commonMsisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsCommonMsisdnType pattern');
		this._commonMsisdn = v;
	}

	encode() {
		let xml = '<hss:CreateEPSMultiSC>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		if (this._associationId) xml += '<hss:associationId>' + this._associationId + '</hss:associationId>';
		if (this._epsProfileId) xml += '<hss:epsProfileId>' + this._epsProfileId + '</hss:epsProfileId>';
		if (this._epsTechnology) xml += '<hss:epsTechnology>' + this._epsTechnology + '</hss:epsTechnology>';
		if (this._nodeType) xml += '<hss:nodeType>' + this._nodeType + '</hss:nodeType>';
		if (this._akaType) xml += '<hss:akaType>' + this._akaType + '</hss:akaType>';
		if (this._epsOdb) xml += '<hss:epsOdb>' + this._epsOdb + '</hss:epsOdb>';
		if (this._epsRoamingAllowed) xml += '<hss:epsRoamingAllowed>' + this._epsRoamingAllowed + '</hss:epsRoamingAllowed>';
		if (this._epsIndividualApnOperatorIdentifierReplacement) xml += '<hss:epsIndividualApnOperatorIdentifierReplacement>' + this._epsIndividualApnOperatorIdentifierReplacement + '</hss:epsIndividualApnOperatorIdentifierReplacement>';
		if (this._epsIndividualDefaultContextId) xml += '<hss:epsIndividualDefaultContextId>' + this._epsIndividualDefaultContextId + '</hss:epsIndividualDefaultContextId>';
		if (this._epsIndividualContextId) xml += '<hss:epsIndividualContextId>' + this._epsIndividualContextId + '</hss:epsIndividualContextId>';
		if (this._epsIndividualSubscribedChargingCharacteristic) xml += '<hss:epsIndividualSubscribedChargingCharacteristic>' + this._epsIndividualSubscribedChargingCharacteristic + '</hss:epsIndividualSubscribedChargingCharacteristic>';
		if (this._epsIndividualAmbrMaximalUplinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalUplinkIpFlow>' + this._epsIndividualAmbrMaximalUplinkIpFlow + '</hss:epsIndividualAmbrMaximalUplinkIpFlow>';
		if (this._epsIndividualAmbrMaximalDownlinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalDownlinkIpFlow>' + this._epsIndividualAmbrMaximalDownlinkIpFlow + '</hss:epsIndividualAmbrMaximalDownlinkIpFlow>';
		if (this._epsIndividualRatFrequencyPriorityId) xml += '<hss:epsIndividualRatFrequencyPriorityId>' + this._epsIndividualRatFrequencyPriorityId + '</hss:epsIndividualRatFrequencyPriorityId>';
		if (this._epsIndividualMappingContextId) xml += '<hss:epsIndividualMappingContextId>' + this._epsIndividualMappingContextId + '</hss:epsIndividualMappingContextId>';
		if (this._epsRoamingRestriction) xml += '<hss:epsRoamingRestriction>' + this._epsRoamingRestriction + '</hss:epsRoamingRestriction>';
		if (this._epsRegionalRoamingServiceAreaId) xml += '<hss:epsRegionalRoamingServiceAreaId>' + this._epsRegionalRoamingServiceAreaId + '</hss:epsRegionalRoamingServiceAreaId>';
		if (this._epsUserIpV4Address) xml += '<hss:epsUserIpV4Address>' + this._epsUserIpV4Address + '</hss:epsUserIpV4Address>';
		if (this._epsUserIpV6Address) xml += '<hss:epsUserIpV6Address>' + this._epsUserIpV6Address + '</hss:epsUserIpV6Address>';
		if (this._epsTenantId) xml += '<hss:epsTenantId>' + this._epsTenantId + '</hss:epsTenantId>';
		if (this._epsSessionTransferNumber) xml += '<hss:epsSessionTransferNumber>' + this._epsSessionTransferNumber + '</hss:epsSessionTransferNumber>';
		if (this._epsMultimediaPriorityService) xml += '<hss:epsMultimediaPriorityService>' + this._epsMultimediaPriorityService + '</hss:epsMultimediaPriorityService>';
		if (this._epsZoneCodeSetId) xml += '<hss:epsZoneCodeSetId>' + this._epsZoneCodeSetId + '</hss:epsZoneCodeSetId>';
		if (this._epsAaaOdb) xml += '<hss:epsAaaOdb>' + this._epsAaaOdb + '</hss:epsAaaOdb>';
		if (this._epsAaaIndividualDefaultContextId) xml += '<hss:epsAaaIndividualDefaultContextId>' + this._epsAaaIndividualDefaultContextId + '</hss:epsAaaIndividualDefaultContextId>';
		if (this._epsAaaIndividualContextId) xml += '<hss:epsAaaIndividualContextId>' + this._epsAaaIndividualContextId + '</hss:epsAaaIndividualContextId>';
		if (this._epsAaaIndividualMappingContextId) xml += '<hss:epsAaaIndividualMappingContextId>' + this._epsAaaIndividualMappingContextId + '</hss:epsAaaIndividualMappingContextId>';
		if (this._zoneid) xml += '<hss:zoneid>' + this._zoneid + '</hss:zoneid>';
		if (this._epsAdminDisable) xml += '<epsAdminDisable>' + this._epsAdminDisable + '</epsAdminDisable>';
		if (this._epsNam) xml += '<hss:epsNam>' + this._epsNam + '</hss:epsNam>';
		if (this._epsAaaMIP6FeatureVector) xml += '<hss:epsAaaMIP6FeatureVector>' + this._epsAaaMIP6FeatureVector + '</hss:epsAaaMIP6FeatureVector>';
		if (this._epsAccessRestriction) xml += '<hss:epsAccessRestriction>' + this._epsAccessRestriction + '</hss:epsAccessRestriction>';
		if (this._epsExtendedAccessRestriction) xml += '<hss:epsExtendedAccessRestriction>' + this._epsExtendedAccessRestriction + '</hss:epsExtendedAccessRestriction>';
		if (this._epsCommonMsisdn) xml += '<hss:epsCommonMsisdn>' + this._epsCommonMsisdn + '</hss:epsCommonMsisdn>';
		if (this._commonMsisdn) xml += '<hss:commonMsisdn>' + this._commonMsisdn + '</hss:commonMsisdn>';
		xml += '</hss:CreateEPSMultiSC>';
		return xml;
	}
}

class SetEPSMultiSC {
	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	get epsProfileId() { return this._epsProfileId; }
	set epsProfileId(v) {
		if (typeof v != 'string') throw new Error('epsProfileId must be a string');
		if (v.length < 1) throw new Error('epsProfileId minLength');
		if (v.length > 255) throw new Error('epsProfileId maxLength');
		this._epsProfileId = v;
	}

	get epsTechnology() { return this._epsTechnology; }
	set epsTechnology(v) {
		if (typeof v != 'string') throw new Error('epsTechnology must be a string');
		if (v.length < 1) throw new Error('epsTechnology minLength');
		if (v.length > 32) throw new Error('epsTechnology maxLength');
		this._epsTechnology = v;
	}

	get nodeType() { return this._nodeType; }
	set nodeType(v) {
		if (typeof v != 'string') throw new Error('nodeType must be a string');
		if (v.length < 3) throw new Error('nodeType minLength');
		if (v.length > 12) throw new Error('nodeType maxLength');
		if (v.match(/^[A-Z]*$/)) {}
		else throw new Error('nodeTypeType pattern');
		this._nodeType = v;
	}

	get akaType() { return this._akaType; }
	set akaType(v) {
		if (typeof v != 'string') throw new Error('akaType must be a string');
		if (v.length < 1) throw new Error('akaType minLength');
		if (v.length > 1) throw new Error('akaType maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('akaTypeType pattern');
		this._akaType = v;
	}

	get epsOdb() { return this._epsOdb; }
	set epsOdb(v) {
		if (typeof v != 'string') throw new Error('epsOdb must be a string');
		if (!['NONE','ODB-ALL','ODB-HPLMN-APN','ODB-VPLMN-APN'].includes(v)) throw new Error('epsOdb enum');
		this._epsOdb = v;
	}

	get epsRoamingAllowed() { return this._epsRoamingAllowed; }
	set epsRoamingAllowed(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingAllowed must be a boolean');
		this._epsRoamingAllowed = v;
	}

	get epsLocationState() { return this._epsLocationState; }
	set epsLocationState(v) {
		if (typeof v != 'string') throw new Error('epsLocationState must be a string');
		if (!['PURGED','LOCATED','UNKNOWN'].includes(v)) throw new Error('epsLocationState enum');
		this._epsLocationState = v;
	}

	get epsIndividualApnOperatorIdentifierReplacement() { return this._epsIndividualApnOperatorIdentifierReplacement; }
	set epsIndividualApnOperatorIdentifierReplacement(v) {
		if (typeof v != 'string') throw new Error('epsIndividualApnOperatorIdentifierReplacement must be a string');
		if (v.length > 255) throw new Error('epsIndividualApnOperatorIdentifierReplacement maxLength');
		this._epsIndividualApnOperatorIdentifierReplacement = v;
	}

	get epsIndividualDefaultContextId() { return this._epsIndividualDefaultContextId; }
	set epsIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualDefaultContextId must be a integer');
		this._epsIndividualDefaultContextId = v;
	}

	get epsIndividualContextId() { return this._epsIndividualContextId; }
	set epsIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualContextId must be a integer');
		this._epsIndividualContextId = v;
	}

	get epsIndividualSubscribedChargingCharacteristic() { return this._epsIndividualSubscribedChargingCharacteristic; }
	set epsIndividualSubscribedChargingCharacteristic(v) {
		if (typeof v != 'number') throw new Error('epsIndividualSubscribedChargingCharacteristic must be a integer');
		this._epsIndividualSubscribedChargingCharacteristic = v;
	}

	get epsIndividualAmbrMaximalUplinkIpFlow() { return this._epsIndividualAmbrMaximalUplinkIpFlow; }
	set epsIndividualAmbrMaximalUplinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalUplinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalUplinkIpFlow = v;
	}

	get epsIndividualAmbrMaximalDownlinkIpFlow() { return this._epsIndividualAmbrMaximalDownlinkIpFlow; }
	set epsIndividualAmbrMaximalDownlinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalDownlinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalDownlinkIpFlow = v;
	}

	get epsIndividualRatFrequencyPriorityId() { return this._epsIndividualRatFrequencyPriorityId; }
	set epsIndividualRatFrequencyPriorityId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualRatFrequencyPriorityId must be a integer');
		this._epsIndividualRatFrequencyPriorityId = v;
	}

	get epsIndividualMappingContextId() { return this._epsIndividualMappingContextId; }
	set epsIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsIndividualMappingContextId must be a string');
		this._epsIndividualMappingContextId = v;
	}

	get epsRoamingRestriction() { return this._epsRoamingRestriction; }
	set epsRoamingRestriction(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingRestriction must be a boolean');
		this._epsRoamingRestriction = v;
	}

	get epsRegionalRoamingServiceAreaId() { return this._epsRegionalRoamingServiceAreaId; }
	set epsRegionalRoamingServiceAreaId(v) {
		if (typeof v != 'number') throw new Error('epsRegionalRoamingServiceAreaId must be a integer');
		if (v < 1) throw new Error('epsRegionalRoamingServiceAreaId minIclusive');
		if (v > 65535) throw new Error('epsRegionalRoamingServiceAreaId maxIclusive');
		this._epsRegionalRoamingServiceAreaId = v;
	}

	get epsAaaRegistrationState() { return this._epsAaaRegistrationState; }
	set epsAaaRegistrationState(v) {
		if (typeof v != 'string') throw new Error('epsAaaRegistrationState must be a string');
		if (!['NOT_REGISTERED'].includes(v)) throw new Error('epsAaaRegistrationState enum');
		this._epsAaaRegistrationState = v;
	}

	get epsUserIpV4Address() { return this._epsUserIpV4Address; }
	set epsUserIpV4Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV4Address must be a string');
		if (v.match(/^.*[$]((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/)) {}
		else throw new Error('epsUserIpV4AddressType pattern');
		this._epsUserIpV4Address = v;
	}

	get epsUserIpV6Address() { return this._epsUserIpV6Address; }
	set epsUserIpV6Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV6Address must be a string');
		if (v.match(/^.*[$][0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]:(:[0-9A-Fa-f]{1,4}){1,7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,6}(:[0-9A-Fa-f]{1,4}){1}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,5}(:[0-9A-Fa-f]{1,4}){1,2}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1,3}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,4}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,5}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,6}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,7}:([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){6,6}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]::([0-9a-fA-F]{1,4}:){0,5}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,2}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,3}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,4}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){1,5}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else throw new Error('epsUserIpV6AddressType pattern');
		this._epsUserIpV6Address = v;
	}

	get epsTenantId() { return this._epsTenantId; }
	set epsTenantId(v) {
		if (typeof v != 'number') throw new Error('epsTenantId must be a integer');
		if (v < 1) throw new Error('epsTenantId minIclusive');
		if (v > 500) throw new Error('epsTenantId maxIclusive');
		this._epsTenantId = v;
	}

	get epsSessionTransferNumber() { return this._epsSessionTransferNumber; }
	set epsSessionTransferNumber(v) {
		if (typeof v != 'string') throw new Error('epsSessionTransferNumber must be a string');
		if (v.length < 5) throw new Error('epsSessionTransferNumber minLength');
		if (v.length > 15) throw new Error('epsSessionTransferNumber maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsSessionTransferNumberType pattern');
		this._epsSessionTransferNumber = v;
	}

	get epsMultimediaPriorityService() { return this._epsMultimediaPriorityService; }
	set epsMultimediaPriorityService(v) {
		if (typeof v != 'number') throw new Error('epsMultimediaPriorityService must be a integer');
		this._epsMultimediaPriorityService = v;
	}

	get epsZoneCodeSetId() { return this._epsZoneCodeSetId; }
	set epsZoneCodeSetId(v) {
		if (typeof v != 'number') throw new Error('epsZoneCodeSetId must be a integer');
		this._epsZoneCodeSetId = v;
	}

	get epsAaaOdb() { return this._epsAaaOdb; }
	set epsAaaOdb(v) {
		if (typeof v != 'string') throw new Error('epsAaaOdb must be a string');
		if (!['NONE','ODB-ALL'].includes(v)) throw new Error('epsAaaOdb enum');
		this._epsAaaOdb = v;
	}

	get epsAaaIndividualDefaultContextId() { return this._epsAaaIndividualDefaultContextId; }
	set epsAaaIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualDefaultContextId must be a integer');
		this._epsAaaIndividualDefaultContextId = v;
	}

	get epsAaaIndividualContextId() { return this._epsAaaIndividualContextId; }
	set epsAaaIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualContextId must be a integer');
		this._epsAaaIndividualContextId = v;
	}

	get epsAaaIndividualMappingContextId() { return this._epsAaaIndividualMappingContextId; }
	set epsAaaIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsAaaIndividualMappingContextId must be a string');
		this._epsAaaIndividualMappingContextId = v;
	}

	get epsAdminDisable() { return this._epsAdminDisable; }
	set epsAdminDisable(v) {
		if (typeof v != 'boolean') throw new Error('epsAdminDisable must be a boolean');
		this._epsAdminDisable = v;
	}

	get epsNam() { return this._epsNam; }
	set epsNam(v) {
		if (typeof v != 'number') throw new Error('epsNam must be a integer');
		if (v < 0) throw new Error('epsNam minIclusive');
		if (v > 2) throw new Error('epsNam maxIclusive');
		this._epsNam = v;
	}

	get epsAaaMIP6FeatureVector() { return this._epsAaaMIP6FeatureVector; }
	set epsAaaMIP6FeatureVector(v) {
		if (typeof v != 'string') throw new Error('epsAaaMIP6FeatureVector must be a string');
		if (!['NO_CAPABILITY','MIP6_INTEGRATED','LOCAL_HOME_AGENT_ASSIGNMENT','PMIP6_SUPPORTED','IP4_HOA_SUPPORTED','LOCAL_MAG_ROUTING_SUPPORTED','ASSIGN_LOCAL_IP','MIP4_SUPPORTED','OPTIMIZED_IDLE_MODE_MOBILITY','GTPv2_SUPPORTED'].includes(v)) throw new Error('epsAaaMIP6FeatureVector enum');
		this._epsAaaMIP6FeatureVector = v;
	}

	get epsAccessRestriction() { return this._epsAccessRestriction; }
	set epsAccessRestriction(v) {
		for (const i of ['_epsExtendedAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('epsAccessRestriction must be a string');
		if (!['E-UTRAN-DENIED','NON-3GPP-ACC-DENIED','ALL-DENIED'].includes(v)) throw new Error('epsAccessRestriction enum');
		this._epsAccessRestriction = v;
	}

	get epsExtendedAccessRestriction() { return this._epsExtendedAccessRestriction; }
	set epsExtendedAccessRestriction(v) {
		for (const i of ['_epsAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'number') throw new Error('epsExtendedAccessRestriction must be a integer');
		if (v < 0) throw new Error('epsExtendedAccessRestriction minIclusive');
		if (v > 127) throw new Error('epsExtendedAccessRestriction maxIclusive');
		this._epsExtendedAccessRestriction = v;
	}

	get epsCommonMsisdn() { return this._epsCommonMsisdn; }
	set epsCommonMsisdn(v) {
		for (const i of ['_commonMsisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('epsCommonMsisdn must be a string');
		if (v.length < 5) throw new Error('epsCommonMsisdn minLength');
		if (v.length > 15) throw new Error('epsCommonMsisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsCommonMsisdnType pattern');
		this._epsCommonMsisdn = v;
	}

	get commonMsisdn() { return this._commonMsisdn; }
	set commonMsisdn(v) {
		for (const i of ['_epsCommonMsisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('commonMsisdn must be a string');
		if (v.length < 5) throw new Error('commonMsisdn minLength');
		if (v.length > 15) throw new Error('commonMsisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsCommonMsisdnType pattern');
		this._commonMsisdn = v;
	}

	encode() {
		let xml = '<hss:SetEPSMultiSC>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		if (this._epsProfileId) xml += '<hss:epsProfileId>' + this._epsProfileId + '</hss:epsProfileId>';
		if (this._epsTechnology) xml += '<hss:epsTechnology>' + this._epsTechnology + '</hss:epsTechnology>';
		if (this._nodeType) xml += '<hss:nodeType>' + this._nodeType + '</hss:nodeType>';
		if (this._akaType) xml += '<hss:akaType>' + this._akaType + '</hss:akaType>';
		if (this._epsOdb) xml += '<hss:epsOdb>' + this._epsOdb + '</hss:epsOdb>';
		if (this._epsRoamingAllowed) xml += '<hss:epsRoamingAllowed>' + this._epsRoamingAllowed + '</hss:epsRoamingAllowed>';
		if (this._epsLocationState) xml += '<hss:epsLocationState>' + this._epsLocationState + '</hss:epsLocationState>';
		if (this._epsIndividualApnOperatorIdentifierReplacement) xml += '<hss:epsIndividualApnOperatorIdentifierReplacement>' + this._epsIndividualApnOperatorIdentifierReplacement + '</hss:epsIndividualApnOperatorIdentifierReplacement>';
		if (this._epsIndividualDefaultContextId) xml += '<hss:epsIndividualDefaultContextId>' + this._epsIndividualDefaultContextId + '</hss:epsIndividualDefaultContextId>';
		if (this._epsIndividualContextId) xml += '<hss:epsIndividualContextId>' + this._epsIndividualContextId + '</hss:epsIndividualContextId>';
		if (this._epsIndividualSubscribedChargingCharacteristic) xml += '<hss:epsIndividualSubscribedChargingCharacteristic>' + this._epsIndividualSubscribedChargingCharacteristic + '</hss:epsIndividualSubscribedChargingCharacteristic>';
		if (this._epsIndividualAmbrMaximalUplinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalUplinkIpFlow>' + this._epsIndividualAmbrMaximalUplinkIpFlow + '</hss:epsIndividualAmbrMaximalUplinkIpFlow>';
		if (this._epsIndividualAmbrMaximalDownlinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalDownlinkIpFlow>' + this._epsIndividualAmbrMaximalDownlinkIpFlow + '</hss:epsIndividualAmbrMaximalDownlinkIpFlow>';
		if (this._epsIndividualRatFrequencyPriorityId) xml += '<hss:epsIndividualRatFrequencyPriorityId>' + this._epsIndividualRatFrequencyPriorityId + '</hss:epsIndividualRatFrequencyPriorityId>';
		if (this._epsIndividualMappingContextId) xml += '<hss:epsIndividualMappingContextId>' + this._epsIndividualMappingContextId + '</hss:epsIndividualMappingContextId>';
		if (this._epsRoamingRestriction) xml += '<hss:epsRoamingRestriction>' + this._epsRoamingRestriction + '</hss:epsRoamingRestriction>';
		if (this._epsRegionalRoamingServiceAreaId) xml += '<hss:epsRegionalRoamingServiceAreaId>' + this._epsRegionalRoamingServiceAreaId + '</hss:epsRegionalRoamingServiceAreaId>';
		if (this._epsAaaRegistrationState) xml += '<hss:epsAaaRegistrationState>' + this._epsAaaRegistrationState + '</hss:epsAaaRegistrationState>';
		if (this._epsUserIpV4Address) xml += '<hss:epsUserIpV4Address>' + this._epsUserIpV4Address + '</hss:epsUserIpV4Address>';
		if (this._epsUserIpV6Address) xml += '<hss:epsUserIpV6Address>' + this._epsUserIpV6Address + '</hss:epsUserIpV6Address>';
		if (this._epsTenantId) xml += '<hss:epsTenantId>' + this._epsTenantId + '</hss:epsTenantId>';
		if (this._epsSessionTransferNumber) xml += '<hss:epsSessionTransferNumber>' + this._epsSessionTransferNumber + '</hss:epsSessionTransferNumber>';
		if (this._epsMultimediaPriorityService) xml += '<hss:epsMultimediaPriorityService>' + this._epsMultimediaPriorityService + '</hss:epsMultimediaPriorityService>';
		if (this._epsZoneCodeSetId) xml += '<hss:epsZoneCodeSetId>' + this._epsZoneCodeSetId + '</hss:epsZoneCodeSetId>';
		if (this._epsAaaOdb) xml += '<hss:epsAaaOdb>' + this._epsAaaOdb + '</hss:epsAaaOdb>';
		if (this._epsAaaIndividualDefaultContextId) xml += '<hss:epsAaaIndividualDefaultContextId>' + this._epsAaaIndividualDefaultContextId + '</hss:epsAaaIndividualDefaultContextId>';
		if (this._epsAaaIndividualContextId) xml += '<hss:epsAaaIndividualContextId>' + this._epsAaaIndividualContextId + '</hss:epsAaaIndividualContextId>';
		if (this._epsAaaIndividualMappingContextId) xml += '<hss:epsAaaIndividualMappingContextId>' + this._epsAaaIndividualMappingContextId + '</hss:epsAaaIndividualMappingContextId>';
		if (this._epsAdminDisable) xml += '<epsAdminDisable>' + this._epsAdminDisable + '</epsAdminDisable>';
		if (this._epsNam) xml += '<hss:epsNam>' + this._epsNam + '</hss:epsNam>';
		if (this._epsAaaMIP6FeatureVector) xml += '<hss:epsAaaMIP6FeatureVector>' + this._epsAaaMIP6FeatureVector + '</hss:epsAaaMIP6FeatureVector>';
		if (this._epsAccessRestriction) xml += '<hss:epsAccessRestriction>' + this._epsAccessRestriction + '</hss:epsAccessRestriction>';
		if (this._epsExtendedAccessRestriction) xml += '<hss:epsExtendedAccessRestriction>' + this._epsExtendedAccessRestriction + '</hss:epsExtendedAccessRestriction>';
		if (this._epsCommonMsisdn) xml += '<hss:epsCommonMsisdn>' + this._epsCommonMsisdn + '</hss:epsCommonMsisdn>';
		if (this._commonMsisdn) xml += '<hss:commonMsisdn>' + this._commonMsisdn + '</hss:commonMsisdn>';
		xml += '</hss:SetEPSMultiSC>';
		return xml;
	}
}

class GetEPSMultiSC {
	get imsi() { return this._imsi; }
	set imsi(v) {
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	get nodeType() { return this._nodeType; }
	set nodeType(v) {
		if (typeof v != 'string') throw new Error('nodeType must be a string');
		if (v.length < 3) throw new Error('nodeType minLength');
		if (v.length > 12) throw new Error('nodeType maxLength');
		if (v.match(/^[A-Z]*$/)) {}
		else throw new Error('nodeTypeType pattern');
		this._nodeType = v;
	}

	get akaType() { return this._akaType; }
	set akaType(v) {
		if (typeof v != 'string') throw new Error('akaType must be a string');
		if (v.length < 1) throw new Error('akaType minLength');
		if (v.length > 1) throw new Error('akaType maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('akaTypeType pattern');
		this._akaType = v;
	}

	encode() {
		let xml = '<hss:GetEPSMultiSC>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		if (this._nodeType) xml += '<hss:nodeType>' + this._nodeType + '</hss:nodeType>';
		if (this._akaType) xml += '<hss:akaType>' + this._akaType + '</hss:akaType>';
		xml += '</hss:GetEPSMultiSC>';
		return xml;
	}
}

class GetResponseEPSMultiSC {
	get imsi() { return this._imsi; }
	set imsi(v) {
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	get associationId() { return this._associationId; }
	set associationId(v) {
		if (typeof v != 'string') throw new Error('associationId must be a string');
		if (v.length > 72) throw new Error('associationId maxLength');
		this._associationId = v;
	}

	get epsProfileId() { return this._epsProfileId; }
	set epsProfileId(v) {
		if (typeof v != 'string') throw new Error('epsProfileId must be a string');
		if (v.length < 1) throw new Error('epsProfileId minLength');
		if (v.length > 255) throw new Error('epsProfileId maxLength');
		this._epsProfileId = v;
	}

	get epsTechnology() { return this._epsTechnology; }
	set epsTechnology(v) {
		if (typeof v != 'string') throw new Error('epsTechnology must be a string');
		if (v.length < 1) throw new Error('epsTechnology minLength');
		if (v.length > 32) throw new Error('epsTechnology maxLength');
		this._epsTechnology = v;
	}

	get nodeType() { return this._nodeType; }
	set nodeType(v) {
		if (typeof v != 'string') throw new Error('nodeType must be a string');
		if (v.length < 3) throw new Error('nodeType minLength');
		if (v.length > 12) throw new Error('nodeType maxLength');
		if (v.match(/^[A-Z]*$/)) {}
		else throw new Error('nodeTypeType pattern');
		this._nodeType = v;
	}

	get akaType() { return this._akaType; }
	set akaType(v) {
		if (typeof v != 'string') throw new Error('akaType must be a string');
		if (v.length < 1) throw new Error('akaType minLength');
		if (v.length > 1) throw new Error('akaType maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('akaTypeType pattern');
		this._akaType = v;
	}

	get epsOdb() { return this._epsOdb; }
	set epsOdb(v) {
		if (typeof v != 'string') throw new Error('epsOdb must be a string');
		if (!['NONE','ODB-ALL','ODB-HPLMN-APN','ODB-VPLMN-APN'].includes(v)) throw new Error('epsOdb enum');
		this._epsOdb = v;
	}

	get epsRoamingAllowed() { return this._epsRoamingAllowed; }
	set epsRoamingAllowed(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingAllowed must be a boolean');
		this._epsRoamingAllowed = v;
	}

	get epsIndividualApnOperatorIdentifierReplacement() { return this._epsIndividualApnOperatorIdentifierReplacement; }
	set epsIndividualApnOperatorIdentifierReplacement(v) {
		if (typeof v != 'string') throw new Error('epsIndividualApnOperatorIdentifierReplacement must be a string');
		if (v.length > 255) throw new Error('epsIndividualApnOperatorIdentifierReplacement maxLength');
		this._epsIndividualApnOperatorIdentifierReplacement = v;
	}

	get epsIndividualDefaultContextId() { return this._epsIndividualDefaultContextId; }
	set epsIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualDefaultContextId must be a integer');
		this._epsIndividualDefaultContextId = v;
	}

	get epsIndividualContextId() { return this._epsIndividualContextId; }
	set epsIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualContextId must be a integer');
		this._epsIndividualContextId = v;
	}

	get epsIndividualSubscribedChargingCharacteristic() { return this._epsIndividualSubscribedChargingCharacteristic; }
	set epsIndividualSubscribedChargingCharacteristic(v) {
		if (typeof v != 'number') throw new Error('epsIndividualSubscribedChargingCharacteristic must be a integer');
		this._epsIndividualSubscribedChargingCharacteristic = v;
	}

	get epsIndividualAmbrMaximalUplinkIpFlow() { return this._epsIndividualAmbrMaximalUplinkIpFlow; }
	set epsIndividualAmbrMaximalUplinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalUplinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalUplinkIpFlow = v;
	}

	get epsIndividualAmbrMaximalDownlinkIpFlow() { return this._epsIndividualAmbrMaximalDownlinkIpFlow; }
	set epsIndividualAmbrMaximalDownlinkIpFlow(v) {
		if (typeof v != 'number') throw new Error('epsIndividualAmbrMaximalDownlinkIpFlow must be a integer');
		this._epsIndividualAmbrMaximalDownlinkIpFlow = v;
	}

	get epsIndividualRatFrequencyPriorityId() { return this._epsIndividualRatFrequencyPriorityId; }
	set epsIndividualRatFrequencyPriorityId(v) {
		if (typeof v != 'number') throw new Error('epsIndividualRatFrequencyPriorityId must be a integer');
		this._epsIndividualRatFrequencyPriorityId = v;
	}

	get epsIndividualMappingContextId() { return this._epsIndividualMappingContextId; }
	set epsIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsIndividualMappingContextId must be a string');
		this._epsIndividualMappingContextId = v;
	}

	get mmeAddress() { return this._mmeAddress; }
	set mmeAddress(v) {
		if (typeof v != 'string') throw new Error('mmeAddress must be a string');
		if (v.length > 255) throw new Error('mmeAddress maxLength');
		this._mmeAddress = v;
	}

	get epsLocationState() { return this._epsLocationState; }
	set epsLocationState(v) {
		if (typeof v != 'string') throw new Error('epsLocationState must be a string');
		if (!['PURGED','LOCATED','UNKNOWN'].includes(v)) throw new Error('epsLocationState enum');
		this._epsLocationState = v;
	}

	get epsRoamingRestriction() { return this._epsRoamingRestriction; }
	set epsRoamingRestriction(v) {
		if (typeof v != 'boolean') throw new Error('epsRoamingRestriction must be a boolean');
		this._epsRoamingRestriction = v;
	}

	get epsRegionalRoamingServiceAreaId() { return this._epsRegionalRoamingServiceAreaId; }
	set epsRegionalRoamingServiceAreaId(v) {
		if (typeof v != 'number') throw new Error('epsRegionalRoamingServiceAreaId must be a integer');
		if (v < 1) throw new Error('epsRegionalRoamingServiceAreaId minIclusive');
		if (v > 65535) throw new Error('epsRegionalRoamingServiceAreaId maxIclusive');
		this._epsRegionalRoamingServiceAreaId = v;
	}

	get epsAaaRegistrationState() { return this._epsAaaRegistrationState; }
	set epsAaaRegistrationState(v) {
		if (typeof v != 'string') throw new Error('epsAaaRegistrationState must be a string');
		if (!['REGISTERED','NOT_REGISTERED','AAA_ASSIGNED'].includes(v)) throw new Error('epsAaaRegistrationState enum');
		this._epsAaaRegistrationState = v;
	}

	get epsAaaAddress() { return this._epsAaaAddress; }
	set epsAaaAddress(v) {
		if (typeof v != 'string') throw new Error('epsAaaAddress must be a string');
		if (v.length > 255) throw new Error('epsAaaAddress maxLength');
		this._epsAaaAddress = v;
	}

	get epsAaaRealm() { return this._epsAaaRealm; }
	set epsAaaRealm(v) {
		if (typeof v != 'string') throw new Error('epsAaaRealm must be a string');
		if (v.length > 255) throw new Error('epsAaaRealm maxLength');
		this._epsAaaRealm = v;
	}

	get epsDynamicPdnInformation() { return this._epsDynamicPdnInformation; }
	set epsDynamicPdnInformation(v) {
		if (typeof v != 'string') throw new Error('epsDynamicPdnInformation must be a string');
		this._epsDynamicPdnInformation = v;
	}

	get epsUeSrVccCap() { return this._epsUeSrVccCap; }
	set epsUeSrVccCap(v) {
		if (typeof v != 'string') throw new Error('epsUeSrVccCap must be a string');
		this._epsUeSrVccCap = v;
	}

	get epsUserIpV4Address() { return this._epsUserIpV4Address; }
	set epsUserIpV4Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV4Address must be a string');
		if (v.match(/^.*[$]((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])[.]){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/)) {}
		else throw new Error('epsUserIpV4AddressType pattern');
		this._epsUserIpV4Address = v;
	}

	get epsUserIpV6Address() { return this._epsUserIpV6Address; }
	set epsUserIpV6Address(v) {
		if (typeof v != 'string') throw new Error('epsUserIpV6Address must be a string');
		if (v.match(/^.*[$][0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4}){7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]:(:[0-9A-Fa-f]{1,4}){1,7}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,6}(:[0-9A-Fa-f]{1,4}){1}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,5}(:[0-9A-Fa-f]{1,4}){1,2}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1,3}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,4}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,5}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,6}([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,7}:([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){6,6}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]::([0-9a-fA-F]{1,4}:){0,5}((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,4}(:[0-9A-Fa-f]{1,4}){1}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,3}(:[0-9A-Fa-f]{1,4}){1,2}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1,2}(:[0-9A-Fa-f]{1,4}){1,3}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9A-Fa-f]{1,4}:){1}(:[0-9A-Fa-f]{1,4}){1,4}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else if (v.match(/^.*[$]([0-9a-fA-F]{1,4}:){1,5}:((25[0-5]|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9])[.]){3,3}(((25[0-5])|(2[0-4][0-9])|(1[0-9][0-9])|[1-9]?[0-9]))([/]((12[0-8]|(1[0-1][0-9])|([1-9][0-9]|[0-9]))))?$/)) {}
		else throw new Error('epsUserIpV6AddressType pattern');
		this._epsUserIpV6Address = v;
	}

	get epsTenantId() { return this._epsTenantId; }
	set epsTenantId(v) {
		if (typeof v != 'number') throw new Error('epsTenantId must be a integer');
		if (v < 1) throw new Error('epsTenantId minIclusive');
		if (v > 500) throw new Error('epsTenantId maxIclusive');
		this._epsTenantId = v;
	}

	get epsSessionTransferNumber() { return this._epsSessionTransferNumber; }
	set epsSessionTransferNumber(v) {
		if (typeof v != 'string') throw new Error('epsSessionTransferNumber must be a string');
		if (v.length < 5) throw new Error('epsSessionTransferNumber minLength');
		if (v.length > 15) throw new Error('epsSessionTransferNumber maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsSessionTransferNumberType pattern');
		this._epsSessionTransferNumber = v;
	}

	get epsMultimediaPriorityService() { return this._epsMultimediaPriorityService; }
	set epsMultimediaPriorityService(v) {
		if (typeof v != 'number') throw new Error('epsMultimediaPriorityService must be a integer');
		this._epsMultimediaPriorityService = v;
	}

	get epsZoneCodeSetId() { return this._epsZoneCodeSetId; }
	set epsZoneCodeSetId(v) {
		if (typeof v != 'number') throw new Error('epsZoneCodeSetId must be a integer');
		this._epsZoneCodeSetId = v;
	}

	get epsAutomaticProvisioned() { return this._epsAutomaticProvisioned; }
	set epsAutomaticProvisioned(v) {
		if (typeof v != 'boolean') throw new Error('epsAutomaticProvisioned must be a boolean');
		this._epsAutomaticProvisioned = v;
	}

	get epsImeiSv() { return this._epsImeiSv; }
	set epsImeiSv(v) {
		if (typeof v != 'string') throw new Error('epsImeiSv must be a string');
		if (v.length < 0) throw new Error('epsImeiSv minLength');
		if (v.length > 16) throw new Error('epsImeiSv maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsImeiSvType pattern');
		this._epsImeiSv = v;
	}

	get epsLastUpdateLocationDate() { return this._epsLastUpdateLocationDate; }
	set epsLastUpdateLocationDate(v) {
		if (typeof v != 'string') throw new Error('epsLastUpdateLocationDate must be a string');
		if (v.length > 255) throw new Error('epsLastUpdateLocationDate maxLength');
		this._epsLastUpdateLocationDate = v;
	}

	get commonMsisdn() { return this._commonMsisdn; }
	set commonMsisdn(v) {
		if (typeof v != 'string') throw new Error('commonMsisdn must be a string');
		if (v.length < 5) throw new Error('commonMsisdn minLength');
		if (v.length > 15) throw new Error('commonMsisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('epsCommonMsisdnType pattern');
		this._commonMsisdn = v;
	}

	get epsAaaOdb() { return this._epsAaaOdb; }
	set epsAaaOdb(v) {
		if (typeof v != 'string') throw new Error('epsAaaOdb must be a string');
		if (!['NONE','ODB-ALL'].includes(v)) throw new Error('epsAaaOdb enum');
		this._epsAaaOdb = v;
	}

	get epsAaaIndividualDefaultContextId() { return this._epsAaaIndividualDefaultContextId; }
	set epsAaaIndividualDefaultContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualDefaultContextId must be a integer');
		this._epsAaaIndividualDefaultContextId = v;
	}

	get epsAaaIndividualContextId() { return this._epsAaaIndividualContextId; }
	set epsAaaIndividualContextId(v) {
		if (typeof v != 'number') throw new Error('epsAaaIndividualContextId must be a integer');
		this._epsAaaIndividualContextId = v;
	}

	get epsAaaIndividualMappingContextId() { return this._epsAaaIndividualMappingContextId; }
	set epsAaaIndividualMappingContextId(v) {
		if (typeof v != 'string') throw new Error('epsAaaIndividualMappingContextId must be a string');
		this._epsAaaIndividualMappingContextId = v;
	}

	get zoneid() { return this._zoneid; }
	set zoneid(v) {
		if (typeof v != 'number') throw new Error('zoneid must be a integer');
		if (v < 0) throw new Error('zoneid minIclusive');
		if (v > 65535) throw new Error('zoneid maxIclusive');
		this._zoneid = v;
	}

	get epsAdminDisable() { return this._epsAdminDisable; }
	set epsAdminDisable(v) {
		if (typeof v != 'boolean') throw new Error('epsAdminDisable must be a boolean');
		this._epsAdminDisable = v;
	}

	get epsNam() { return this._epsNam; }
	set epsNam(v) {
		if (typeof v != 'number') throw new Error('epsNam must be a integer');
		if (v < 0) throw new Error('epsNam minIclusive');
		if (v > 2) throw new Error('epsNam maxIclusive');
		this._epsNam = v;
	}

	get epsAaaMIP6FeatureVector() { return this._epsAaaMIP6FeatureVector; }
	set epsAaaMIP6FeatureVector(v) {
		if (typeof v != 'string') throw new Error('epsAaaMIP6FeatureVector must be a string');
		if (!['NO_CAPABILITY','MIP6_INTEGRATED','LOCAL_HOME_AGENT_ASSIGNMENT','PMIP6_SUPPORTED','IP4_HOA_SUPPORTED','LOCAL_MAG_ROUTING_SUPPORTED','ASSIGN_LOCAL_IP','MIP4_SUPPORTED','OPTIMIZED_IDLE_MODE_MOBILITY','GTPv2_SUPPORTED'].includes(v)) throw new Error('epsAaaMIP6FeatureVector enum');
		this._epsAaaMIP6FeatureVector = v;
	}

	get epsAccessRestriction() { return this._epsAccessRestriction; }
	set epsAccessRestriction(v) {
		for (const i of ['_epsExtendedAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('epsAccessRestriction must be a string');
		if (!['E-UTRAN-DENIED','NON-3GPP-ACC-DENIED','ALL-DENIED'].includes(v)) throw new Error('epsAccessRestriction enum');
		this._epsAccessRestriction = v;
	}

	get epsExtendedAccessRestriction() { return this._epsExtendedAccessRestriction; }
	set epsExtendedAccessRestriction(v) {
		for (const i of ['_epsAccessRestriction']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'number') throw new Error('epsExtendedAccessRestriction must be a integer');
		if (v < 0) throw new Error('epsExtendedAccessRestriction minIclusive');
		if (v > 127) throw new Error('epsExtendedAccessRestriction maxIclusive');
		this._epsExtendedAccessRestriction = v;
	}

	encode() {
		let xml = '<hss:GetResponseEPSMultiSC>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		if (this._associationId) xml += '<hss:associationId>' + this._associationId + '</hss:associationId>';
		if (this._epsProfileId) xml += '<hss:epsProfileId>' + this._epsProfileId + '</hss:epsProfileId>';
		if (this._epsTechnology) xml += '<hss:epsTechnology>' + this._epsTechnology + '</hss:epsTechnology>';
		if (this._nodeType) xml += '<hss:nodeType>' + this._nodeType + '</hss:nodeType>';
		if (this._akaType) xml += '<hss:akaType>' + this._akaType + '</hss:akaType>';
		if (this._epsOdb) xml += '<hss:epsOdb>' + this._epsOdb + '</hss:epsOdb>';
		if (this._epsRoamingAllowed) xml += '<hss:epsRoamingAllowed>' + this._epsRoamingAllowed + '</hss:epsRoamingAllowed>';
		if (this._epsIndividualApnOperatorIdentifierReplacement) xml += '<hss:epsIndividualApnOperatorIdentifierReplacement>' + this._epsIndividualApnOperatorIdentifierReplacement + '</hss:epsIndividualApnOperatorIdentifierReplacement>';
		if (this._epsIndividualDefaultContextId) xml += '<hss:epsIndividualDefaultContextId>' + this._epsIndividualDefaultContextId + '</hss:epsIndividualDefaultContextId>';
		if (this._epsIndividualContextId) xml += '<hss:epsIndividualContextId>' + this._epsIndividualContextId + '</hss:epsIndividualContextId>';
		if (this._epsIndividualSubscribedChargingCharacteristic) xml += '<hss:epsIndividualSubscribedChargingCharacteristic>' + this._epsIndividualSubscribedChargingCharacteristic + '</hss:epsIndividualSubscribedChargingCharacteristic>';
		if (this._epsIndividualAmbrMaximalUplinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalUplinkIpFlow>' + this._epsIndividualAmbrMaximalUplinkIpFlow + '</hss:epsIndividualAmbrMaximalUplinkIpFlow>';
		if (this._epsIndividualAmbrMaximalDownlinkIpFlow) xml += '<hss:epsIndividualAmbrMaximalDownlinkIpFlow>' + this._epsIndividualAmbrMaximalDownlinkIpFlow + '</hss:epsIndividualAmbrMaximalDownlinkIpFlow>';
		if (this._epsIndividualRatFrequencyPriorityId) xml += '<hss:epsIndividualRatFrequencyPriorityId>' + this._epsIndividualRatFrequencyPriorityId + '</hss:epsIndividualRatFrequencyPriorityId>';
		if (this._epsIndividualMappingContextId) xml += '<hss:epsIndividualMappingContextId>' + this._epsIndividualMappingContextId + '</hss:epsIndividualMappingContextId>';
		if (this._mmeAddress) xml += '<hss:mmeAddress>' + this._mmeAddress + '</hss:mmeAddress>';
		if (this._epsLocationState) xml += '<hss:epsLocationState>' + this._epsLocationState + '</hss:epsLocationState>';
		if (this._epsRoamingRestriction) xml += '<hss:epsRoamingRestriction>' + this._epsRoamingRestriction + '</hss:epsRoamingRestriction>';
		if (this._epsRegionalRoamingServiceAreaId) xml += '<hss:epsRegionalRoamingServiceAreaId>' + this._epsRegionalRoamingServiceAreaId + '</hss:epsRegionalRoamingServiceAreaId>';
		if (this._epsAaaRegistrationState) xml += '<hss:epsAaaRegistrationState>' + this._epsAaaRegistrationState + '</hss:epsAaaRegistrationState>';
		if (this._epsAaaAddress) xml += '<hss:epsAaaAddress>' + this._epsAaaAddress + '</hss:epsAaaAddress>';
		if (this._epsAaaRealm) xml += '<hss:epsAaaRealm>' + this._epsAaaRealm + '</hss:epsAaaRealm>';
		if (this._epsDynamicPdnInformation) xml += '<hss:epsDynamicPdnInformation>' + this._epsDynamicPdnInformation + '</hss:epsDynamicPdnInformation>';
		if (this._epsUeSrVccCap) xml += '<hss:epsUeSrVccCap>' + this._epsUeSrVccCap + '</hss:epsUeSrVccCap>';
		if (this._epsUserIpV4Address) xml += '<hss:epsUserIpV4Address>' + this._epsUserIpV4Address + '</hss:epsUserIpV4Address>';
		if (this._epsUserIpV6Address) xml += '<hss:epsUserIpV6Address>' + this._epsUserIpV6Address + '</hss:epsUserIpV6Address>';
		if (this._epsTenantId) xml += '<hss:epsTenantId>' + this._epsTenantId + '</hss:epsTenantId>';
		if (this._epsSessionTransferNumber) xml += '<hss:epsSessionTransferNumber>' + this._epsSessionTransferNumber + '</hss:epsSessionTransferNumber>';
		if (this._epsMultimediaPriorityService) xml += '<hss:epsMultimediaPriorityService>' + this._epsMultimediaPriorityService + '</hss:epsMultimediaPriorityService>';
		if (this._epsZoneCodeSetId) xml += '<hss:epsZoneCodeSetId>' + this._epsZoneCodeSetId + '</hss:epsZoneCodeSetId>';
		if (this._epsAutomaticProvisioned) xml += '<hss:epsAutomaticProvisioned>' + this._epsAutomaticProvisioned + '</hss:epsAutomaticProvisioned>';
		if (this._epsImeiSv) xml += '<hss:epsImeiSv>' + this._epsImeiSv + '</hss:epsImeiSv>';
		if (this._epsLastUpdateLocationDate) xml += '<hss:epsLastUpdateLocationDate>' + this._epsLastUpdateLocationDate + '</hss:epsLastUpdateLocationDate>';
		if (this._commonMsisdn) xml += '<hss:commonMsisdn>' + this._commonMsisdn + '</hss:commonMsisdn>';
		if (this._epsAaaOdb) xml += '<hss:epsAaaOdb>' + this._epsAaaOdb + '</hss:epsAaaOdb>';
		if (this._epsAaaIndividualDefaultContextId) xml += '<hss:epsAaaIndividualDefaultContextId>' + this._epsAaaIndividualDefaultContextId + '</hss:epsAaaIndividualDefaultContextId>';
		if (this._epsAaaIndividualContextId) xml += '<hss:epsAaaIndividualContextId>' + this._epsAaaIndividualContextId + '</hss:epsAaaIndividualContextId>';
		if (this._epsAaaIndividualMappingContextId) xml += '<hss:epsAaaIndividualMappingContextId>' + this._epsAaaIndividualMappingContextId + '</hss:epsAaaIndividualMappingContextId>';
		if (this._zoneid) xml += '<hss:zoneid>' + this._zoneid + '</hss:zoneid>';
		if (this._epsAdminDisable) xml += '<epsAdminDisable>' + this._epsAdminDisable + '</epsAdminDisable>';
		if (this._epsNam) xml += '<hss:epsNam>' + this._epsNam + '</hss:epsNam>';
		if (this._epsAaaMIP6FeatureVector) xml += '<hss:epsAaaMIP6FeatureVector>' + this._epsAaaMIP6FeatureVector + '</hss:epsAaaMIP6FeatureVector>';
		if (this._epsAccessRestriction) xml += '<hss:epsAccessRestriction>' + this._epsAccessRestriction + '</hss:epsAccessRestriction>';
		if (this._epsExtendedAccessRestriction) xml += '<hss:epsExtendedAccessRestriction>' + this._epsExtendedAccessRestriction + '</hss:epsExtendedAccessRestriction>';
		xml += '</hss:GetResponseEPSMultiSC>';
		return xml;
	}
}

class PGFault {
	get errorcode() { return this._errorcode; }
	set errorcode(v) {
		if (typeof v != 'number') throw new Error('errorcode must be a integer');
		this._errorcode = v;
	}

	get errormessage() { return this._errormessage; }
	set errormessage(v) {
		if (typeof v != 'string') throw new Error('errormessage must be a string');
		this._errormessage = v;
	}

	get errordetails() { return this._errordetails; }
	set errordetails(v) {
		if (typeof v != 'string') throw new Error('errordetails must be a string');
		this._errordetails = v;
	}

	encode() {
		let xml = '<pg:PGFault>';
		if (this._errorcode) xml += '<errorcode>' + this._errorcode + '</errorcode>';
		if (this._errormessage) xml += '<errormessage>' + this._errormessage + '</errormessage>';
		if (this._errordetails) xml += '<errordetails>' + this._errordetails + '</errordetails>';
		xml += '</pg:PGFault>';
		return xml;
	}
}

class EPSFault {
	get errorcode() { return this._errorcode; }
	set errorcode(v) {
		if (typeof v != 'number') throw new Error('errorcode must be a integer');
		this._errorcode = v;
	}

	get errormessage() { return this._errormessage; }
	set errormessage(v) {
		if (typeof v != 'string') throw new Error('errormessage must be a string');
		this._errormessage = v;
	}

	get errordetails() { return this._errordetails; }
	set errordetails(v) {
		if (typeof v != 'string') throw new Error('errordetails must be a string');
		this._errordetails = v;
	}

	encode() {
		let xml = '<pg:EPSFault>';
		if (this._errorcode) xml += '<errorcode>' + this._errorcode + '</errorcode>';
		if (this._errormessage) xml += '<errormessage>' + this._errormessage + '</errormessage>';
		if (this._errordetails) xml += '<errordetails>' + this._errordetails + '</errordetails>';
		xml += '</pg:EPSFault>';
		return xml;
	}
}

class AVGFault {
	get errorcode() { return this._errorcode; }
	set errorcode(v) {
		if (typeof v != 'number') throw new Error('errorcode must be a integer');
		this._errorcode = v;
	}

	get errormessage() { return this._errormessage; }
	set errormessage(v) {
		if (typeof v != 'string') throw new Error('errormessage must be a string');
		this._errormessage = v;
	}

	get errordetails() { return this._errordetails; }
	set errordetails(v) {
		if (typeof v != 'string') throw new Error('errordetails must be a string');
		this._errordetails = v;
	}

	encode() {
		let xml = '<pg:AVGFault>';
		if (this._errorcode) xml += '<errorcode>' + this._errorcode + '</errorcode>';
		if (this._errormessage) xml += '<errormessage>' + this._errormessage + '</errormessage>';
		if (this._errordetails) xml += '<errordetails>' + this._errordetails + '</errordetails>';
		xml += '</pg:AVGFault>';
		return xml;
	}
}

class Create {
	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:Create>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += this._MOId.encode();
		if (this._MOAttributes) xml += this._MOAttributes.encode();
		xml += '</cai3g:Create>';
		return xml;
	}
}

Create.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		for (const i of ['_msisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		for (const i of ['_imsi']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

Create.MOAttributes = class {
	get CreateEPSMultiSC() { return this._CreateEPSMultiSC; }
	set CreateEPSMultiSC(v) {
		this._CreateEPSMultiSC = v;
	}

	encode() {
		let xml = '<cai3g:MOAttributes>';
		if (this._CreateEPSMultiSC) xml += this._CreateEPSMultiSC.encode();
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

class CreateResponse {
	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	encode() {
		let xml = '<cai3g:CreateResponse>';
		if (this._MOId) xml += this._MOId.encode();
		xml += '</cai3g:CreateResponse>';
		return xml;
	}
}

CreateResponse.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

class Delete {
	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	encode() {
		let xml = '<cai3g:Delete>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += this._MOId.encode();
		xml += '</cai3g:Delete>';
		return xml;
	}
}

Delete.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		for (const i of ['_msisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		for (const i of ['_imsi']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

class DeleteResponse {
	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	encode() {
		let xml = '<cai3g:DeleteResponse>';
		if (this._MOId) xml += this._MOId.encode();
		xml += '</cai3g:DeleteResponse>';
		return xml;
	}
}

DeleteResponse.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

class Set {
	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:Set>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += this._MOId.encode();
		if (this._MOAttributes) xml += this._MOAttributes.encode();
		xml += '</cai3g:Set>';
		return xml;
	}
}

Set.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		for (const i of ['_msisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		for (const i of ['_imsi']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

Set.MOAttributes = class {
	get SetEPSMultiSC() { return this._SetEPSMultiSC; }
	set SetEPSMultiSC(v) {
		this._SetEPSMultiSC = v;
	}

	encode() {
		let xml = '<cai3g:MOAttributes>';
		if (this._SetEPSMultiSC) xml += this._SetEPSMultiSC.encode();
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

class SetResponse {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:SetResponse>';
		if (this._MOAttributes) xml += this._MOAttributes.encode();
		xml += '</cai3g:SetResponse>';
		return xml;
	}
}

SetResponse.MOAttributes = class {
	get SetEPSMultiSC() { return this._SetEPSMultiSC; }
	set SetEPSMultiSC(v) {
		this._SetEPSMultiSC = v;
	}

	encode() {
		let xml = '<cai3g:MOAttributes>';
		if (this._SetEPSMultiSC) xml += this._SetEPSMultiSC.encode();
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

class Get {
	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:Get>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += this._MOId.encode();
		if (this._MOAttributes) xml += this._MOAttributes.encode();
		xml += '</cai3g:Get>';
		return xml;
	}
}

Get.MOId = class {
	get imsi() { return this._imsi; }
	set imsi(v) {
		for (const i of ['_msisdn']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('imsi must be a string');
		if (v.length < 6) throw new Error('imsi minLength');
		if (v.length > 15) throw new Error('imsi maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('imsiType pattern');
		this._imsi = v;
	}

	get msisdn() { return this._msisdn; }
	set msisdn(v) {
		for (const i of ['_imsi']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		if (typeof v != 'string') throw new Error('msisdn must be a string');
		if (v.length < 5) throw new Error('msisdn minLength');
		if (v.length > 15) throw new Error('msisdn maxLength');
		if (v.match(/^[0-9]*$/)) {}
		else throw new Error('msisdnType pattern');
		this._msisdn = v;
	}

	encode() {
		let xml = '<cai3g:MOId>';
		if (this._imsi) xml += '<hss:imsi>' + this._imsi + '</hss:imsi>';
		if (this._msisdn) xml += '<hss:msisdn>' + this._msisdn + '</hss:msisdn>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

Get.MOAttributes = class {
	get GetEPSMultiSC() { return this._GetEPSMultiSC; }
	set GetEPSMultiSC(v) {
		this._GetEPSMultiSC = v;
	}

	encode() {
		let xml = '<cai3g:MOAttributes>';
		if (this._GetEPSMultiSC) xml += this._GetEPSMultiSC.encode();
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

class GetResponse {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:GetResponse>';
		if (this._MOAttributes) xml += this._MOAttributes.encode();
		xml += '</cai3g:GetResponse>';
		return xml;
	}
}

GetResponse.MOAttributes = class {
	get GetResponseEPSMultiSC() { return this._GetResponseEPSMultiSC; }
	set GetResponseEPSMultiSC(v) {
		this._GetResponseEPSMultiSC = v;
	}

	encode() {
		let xml = '<cai3g:MOAttributes>';
		if (this._GetResponseEPSMultiSC) xml += this._GetResponseEPSMultiSC.encode();
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

class Cai3gFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'number') throw new Error('faultcode must be a integer');
		this._faultcode = v;
	}

	get faultreason() { return this._faultreason; }
	set faultreason(v) {
		this._faultreason = v;
	}

	get faultrole() { return this._faultrole; }
	set faultrole(v) {
		if (typeof v != 'string') throw new Error('faultrole must be a string');
		this._faultrole = v;
	}

	get details() { return this._details; }
	set details(v) {
		this._details = v;
	}

	encode() {
		let xml = '<cai3g:Cai3gFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		if (this._faultreason) xml += this._faultreason.encode();
		if (this._faultrole) xml += '<cai3g:faultrole>' + this._faultrole + '</cai3g:faultrole>';
		if (this._details) xml += this._details.encode();
		xml += '</cai3g:Cai3gFault>';
		return xml;
	}
}

Cai3gFault.faultreason = class {
	get reasonText() { return this._reasonText; }
	set reasonText(v) {
		if (typeof v != 'string') throw new Error('reasonText must be a string');
		this._reasonText = v;
	}

	encode() {
		let xml = '<cai3g:faultreason>';
		if (this._reasonText) xml += '<cai3g:reasonText>' + this._reasonText + '</cai3g:reasonText>';
		xml += '</cai3g:faultreason>';
		return xml;
	}
}

Cai3gFault.details = class {
	get PGFault() { return this._PGFault; }
	set PGFault(v) {
		for (const i of ['_EPSFault']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		this._PGFault = v;
	}

	get EPSFault() { return this._EPSFault; }
	set EPSFault(v) {
		for (const i of ['_PGFault']) if (this.hasOwnProperty(i)) throw new Error('Setting multiple elements in choice');
		this._EPSFault = v;
	}

	encode() {
		let xml = '<cai3g:details>';
		if (this._PGFault) xml += this._PGFault.encode();
		if (this._EPSFault) xml += this._EPSFault.encode();
		xml += '</cai3g:details>';
		return xml;
	}
}

class SessionIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid SessionId','Session Timeout','SessionId Syntax Error'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:SessionIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:SessionIdFault>';
		return xml;
	}
}

class SequenceIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid SequenceId'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:SequenceIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:SequenceIdFault>';
		return xml;
	}
}

class TransactionIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid TransactionId'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:TransactionIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:TransactionIdFault>';
		return xml;
	}
}

class HeaderFaultType {
	get faultactor() { return this._faultactor; }
	set faultactor(v) {
		if (typeof v != 'string') throw new Error('faultactor must be a string');
		this._faultactor = v;
	}

	get description() { return this._description; }
	set description(v) {
		if (typeof v != 'string') throw new Error('description must be a string');
		this._description = v;
	}

	encode() {
		let xml = '<cai3g:HeaderFaultType>';
		if (this._faultactor) xml += '<cai3g:faultactor>' + this._faultactor + '</cai3g:faultactor>';
		if (this._description) xml += '<cai3g:description>' + this._description + '</cai3g:description>';
		xml += '</cai3g:HeaderFaultType>';
		return xml;
	}
}

async function sendRequest(action, req, headers) {
	let xml = '<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hss="http://schemas.ericsson.com/ma/HSS/" xmlns:pg="http://schemas.ericsson.com/pg/1.0" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/">';
	if (headers) xml += headers.encode();
	else xml += '<S:Header/>';
	xml += '<S:Body>' + req.encode() + '</S:Body></S:Envelope>';

	return new Promise((resolve, reject) => {
		axios.post(req.url, xml, {
			headers: { "SOAPAction": action, "content-type": "text/xml" }
		}).then((response) => {
			resolve(response);
		}).catch((error) => {
			reject(error);
		});
	});
}

Create.Header = class {
	get SessionId() { return this._SessionId; }
	set SessionId(v) {
		if (typeof v != 'string') throw new Error('SessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._SessionId = v;
	}

	get TransactionId() { return this._TransactionId; }
	set TransactionId(v) {
		if (typeof v != 'number') throw new Error('TransactionId must be a integer');
		this._TransactionId = v;
	}

	get SequenceId() { return this._SequenceId; }
	set SequenceId(v) {
		if (typeof v != 'number') throw new Error('SequenceId must be a integer');
		this._SequenceId = v;
	}

	encode() {
		let xml = '<S:Header>';
		if (this._SessionId) xml += '<cai3g:SessionId>' + this._SessionId + '</cai3g:SessionId>';
		if (this._TransactionId) xml += '<cai3g:TransactionId>' + this._TransactionId + '</cai3g:TransactionId>';
		if (this._SequenceId) xml += '<cai3g:SequenceId>' + this._SequenceId + '</cai3g:SequenceId>';
		xml += '</S:Header>';
		return xml;
	}
}

Delete.Header = class {
	get SessionId() { return this._SessionId; }
	set SessionId(v) {
		if (typeof v != 'string') throw new Error('SessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._SessionId = v;
	}

	get TransactionId() { return this._TransactionId; }
	set TransactionId(v) {
		if (typeof v != 'number') throw new Error('TransactionId must be a integer');
		this._TransactionId = v;
	}

	get SequenceId() { return this._SequenceId; }
	set SequenceId(v) {
		if (typeof v != 'number') throw new Error('SequenceId must be a integer');
		this._SequenceId = v;
	}

	encode() {
		let xml = '<S:Header>';
		if (this._SessionId) xml += '<cai3g:SessionId>' + this._SessionId + '</cai3g:SessionId>';
		if (this._TransactionId) xml += '<cai3g:TransactionId>' + this._TransactionId + '</cai3g:TransactionId>';
		if (this._SequenceId) xml += '<cai3g:SequenceId>' + this._SequenceId + '</cai3g:SequenceId>';
		xml += '</S:Header>';
		return xml;
	}
}

Get.Header = class {
	get SessionId() { return this._SessionId; }
	set SessionId(v) {
		if (typeof v != 'string') throw new Error('SessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._SessionId = v;
	}

	get TransactionId() { return this._TransactionId; }
	set TransactionId(v) {
		if (typeof v != 'number') throw new Error('TransactionId must be a integer');
		this._TransactionId = v;
	}

	get SequenceId() { return this._SequenceId; }
	set SequenceId(v) {
		if (typeof v != 'number') throw new Error('SequenceId must be a integer');
		this._SequenceId = v;
	}

	encode() {
		let xml = '<S:Header>';
		if (this._SessionId) xml += '<cai3g:SessionId>' + this._SessionId + '</cai3g:SessionId>';
		if (this._TransactionId) xml += '<cai3g:TransactionId>' + this._TransactionId + '</cai3g:TransactionId>';
		if (this._SequenceId) xml += '<cai3g:SequenceId>' + this._SequenceId + '</cai3g:SequenceId>';
		xml += '</S:Header>';
		return xml;
	}
}

Set.Header = class {
	get SessionId() { return this._SessionId; }
	set SessionId(v) {
		if (typeof v != 'string') throw new Error('SessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._SessionId = v;
	}

	get TransactionId() { return this._TransactionId; }
	set TransactionId(v) {
		if (typeof v != 'number') throw new Error('TransactionId must be a integer');
		this._TransactionId = v;
	}

	get SequenceId() { return this._SequenceId; }
	set SequenceId(v) {
		if (typeof v != 'number') throw new Error('SequenceId must be a integer');
		this._SequenceId = v;
	}

	encode() {
		let xml = '<S:Header>';
		if (this._SessionId) xml += '<cai3g:SessionId>' + this._SessionId + '</cai3g:SessionId>';
		if (this._TransactionId) xml += '<cai3g:TransactionId>' + this._TransactionId + '</cai3g:TransactionId>';
		if (this._SequenceId) xml += '<cai3g:SequenceId>' + this._SequenceId + '</cai3g:SequenceId>';
		xml += '</S:Header>';
		return xml;
	}
}

module.exports.EPSMultiSC = {
	CreateEPSMultiSC,
	SetEPSMultiSC,
	GetEPSMultiSC,
	GetResponseEPSMultiSC,
	PGFault,
	EPSFault,
	AVGFault,
	Create,
	CreateResponse,
	Delete,
	DeleteResponse,
	Set,
	SetResponse,
	Get,
	GetResponse,
	Cai3gFault,
	SessionIdFault,
	SequenceIdFault,
	TransactionIdFault,
	HeaderFaultType,
	sendCreate: function (req, headers) {
		if (!(req instanceof Create)) throw new Error('Invalid request type')
		if (!(headers instanceof Create.Header)) throw new Error('Invalid headers type')
		return sendRequest('CAI3G#Create', req, headers);
	},
	sendDelete: function (req, headers) {
		if (!(req instanceof Delete)) throw new Error('Invalid request type')
		if (!(headers instanceof Delete.Header)) throw new Error('Invalid headers type')
		return sendRequest('CAI3G#Delete', req, headers);
	},
	sendGet: function (req, headers) {
		if (!(req instanceof Get)) throw new Error('Invalid request type')
		if (!(headers instanceof Get.Header)) throw new Error('Invalid headers type')
		return sendRequest('CAI3G#Get', req, headers);
	},
	sendSet: function (req, headers) {
		if (!(req instanceof Set)) throw new Error('Invalid request type')
		if (!(headers instanceof Set.Header)) throw new Error('Invalid headers type')
		return sendRequest('CAI3G#Set', req, headers);
	},
}
