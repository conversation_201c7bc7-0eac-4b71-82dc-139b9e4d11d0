const parser = require("xml2json");
const genericPool = require("generic-pool");

const cai3gSessionLib = require("./cai3g-session-ctl.lib").SessionControl;
const cai3gLib = require("./cai3g.lib").EPSMultiSC;
const CoalesceError = require("@coalesce/coalesceError");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");

const cai3gEndpointManager = new EndpointManager("cai3g");

class Cai3gError extends CoalesceError {
  constructor(...params) {
    super(Cai3gError, ...params);
  }
}

// Will be overwritten by CONNECTORS!!
let cai3gOptions = {
  url: "http://127.0.0.1:8088/",
};

const DEFAULT_ENDPOINT_NAME = "cai3g";
// Overwritten by default endpoint .... OR by setEndpoint() method inside a GUI component
let currentEndpointName = DEFAULT_ENDPOINT_NAME;

// Will be overwritten by CONNECTORS!!
let cai3gSessionLogin = {
  username: "userN",
  password: "passw0r_d",
};

let poolOptions = {
  max: 3, // maximum size of the pool
  min: 1, // minimum size of the pool
  acquireTimeoutMillis: 1500,
};

const MAX_TIMEOUT = 5000;
const MIN_TIMEOUT = 1000;

const factory = {
  create: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const request = new cai3gSessionLib.Login();

        request.pwd = cai3gSessionLogin.password;
        request.userId = cai3gSessionLogin.username;

        const res = new cai3gSessionLib.LoginResponse();

        request.MOAttributes = res;
        request.url = cai3gOptions.url;

        const response = await cai3gSessionLib.sendLogin(request);

        const json = parser.toJson(response.data, { object: true });

        console.info("==== Logging in to Cai3g ====");
        console.trace(" Login Response \n", JSON.stringify(json));

        const sessionId = json["S:Envelope"]["S:Body"].LoginResponse.sessionId;
        resolve(sessionId);
      } catch (err) {
        let factoryError = new Cai3gError(err, "Failed to login to cai3g server");
        console.error(factoryError);
        reject(factoryError);
      }
    });
  },
  destroy: async (sessionId) => {
    try {
      const request = new cai3gSessionLib.Logout();
      const headers = new cai3gSessionLib.Logout.Header();

      headers.SessionId = sessionId;
      request.sessionId = sessionId;

      request.url = cai3gOptions.url;

      await cai3gSessionLib.sendLogout(request, headers).then((response) => {
        const json = parser.toJson(response.data, { object: true });

        console.info("==== Logged Out of Cai3g ====");
        console.trace(" Logout Response \n", JSON.stringify(json));
      });
    } catch (err) {
      const factoryError = new Cai3gError(err, "An error occurred during logout");
      console.error(factoryError);
      throw factoryError;
    }
  },
};

let activePool = false;

async function getActivePool(connectionInfo) {
  let { hostname, port, url, timeout, auth } = connectionInfo;

  timeout = parseInt(timeout);

  if (isNaN(timeout) || timeout < MIN_TIMEOUT || timeout > MAX_TIMEOUT) {
    console.warn(`Invalid timeout for CAI3G endpoint, using default: ${poolOptions.acquireTimeoutMillis}`);
    // make them equal so the 'new' check explicitly has no change for timeout
    timeout = poolOptions.acquireTimeoutMillis;
  }

  const newAuthInfo =
    typeof auth === "object" &&
    (auth.username !== cai3gSessionLogin.username || auth.password !== cai3gSessionLogin.password);

  if (newAuthInfo) {
    console.debug("==== Setting Connection Pool Authentication");
    cai3gSessionLogin.username = auth.username;
    cai3gSessionLogin.password = auth.password;
  }

  const encodedUrl = encodeURIPath(url.replace(/^\//, ""));

  let fullUrl = `http://${String(hostname)}:${String(port)}/${encodedUrl}`;

  const newConnectInfo = fullUrl !== cai3gOptions.url || timeout !== poolOptions.acquireTimeoutMillis;

  if (newConnectInfo) {
    console.debug("==== Setting Connection Pool URL : ", fullUrl);
    console.debug("==== Setting Connection Pool Timeout : ", timeout);
    cai3gOptions.url = fullUrl;
    poolOptions.acquireTimeoutMillis = timeout;
  }

  if (activePool === false || newConnectInfo || newAuthInfo) {
    if (activePool !== false) {
      // This means a NEW URL has been generated ... let's drain and clear the current pool first before creating a new one
      console.debug("==== Received NEW Connection or Auth details. Draining Pool, and Re-creating ====");
      await activePool.drain().then(() => {
        activePool.clear();
      });
    }

    timeout = parseInt(timeout);

    console.debug(
      `==== ${
        activePool !== false ? "Updated Connector" : "First Run"
      }: Activating connection pool with respective connector information ====`
    );

    console.debug("==== Connection Pool Options : ", JSON.stringify(poolOptions));
    console.debug("==== CAI3G Connection settings : ", JSON.stringify(cai3gOptions));

    activePool = genericPool.createPool(factory, poolOptions);
    activePool.on("factoryCreateError", (error) => {
      // Workaround for known issue releasing failed create() errors
      // -- see https://github.com/coopernurse/node-pool/issues/175#issuecomment-501826583
      const dequeued = activePool._waitingClientsQueue.dequeue();
      if (dequeued) dequeued.reject(error);
    });
    console.debug("==== Pool Promise Created ====");
  } else {
    console.debug("==== No changes to connection settings or auth settings - Using Active Connection Pool ====");
  }

  return activePool;
}

const getEndpointConnectionInfo = (ctx) => {
  let connectionInfo;

  if (!currentEndpointName) console.warn("Setting to default endpoint: ", DEFAULT_ENDPOINT_NAME);

  cai3gEndpointManager.setEndpointInContext(currentEndpointName || DEFAULT_ENDPOINT_NAME, ctx);
  connectionInfo = cai3gEndpointManager.getEndpointFromContext(ctx);

  return connectionInfo;
};

const setEndpoint = (replacementEndpointName) => {
  currentEndpointName = replacementEndpointName;
};

const setDefaultEndpoint = () => {
  currentEndpointName = DEFAULT_ENDPOINT_NAME;
};

function cai3gGet_AUCRequest(sessionId, imsi) {
  return cai3gGet(sessionId, imsi, "AUC");
}

function cai3gGet_HLRRequest(sessionId, msisdn) {
  return cai3gGet(sessionId, msisdn, "HLR");
}

function cai3gGet(sessionId, identifier, nodeType) {
  return new Promise(async (resolve, reject) => {
    let responseData = { ok: false };
    try {
      const request = new cai3gLib.Get();
      const headers = new cai3gLib.Get.Header();
      headers.SessionId = sessionId;

      request.MOId = new cai3gLib.Get.MOId();

      if (nodeType === "HLR") request.MOId.msisdn = identifier;
      else request.MOId.imsi = identifier;

      const eps = new cai3gLib.GetEPSMultiSC();

      if (nodeType === "HLR") eps.msisdn = identifier;
      else eps.imsi = identifier;

      eps.nodeType = nodeType;
      eps.akaType = "1";

      request.MOAttributes = new cai3gLib.Get.MOAttributes();
      request.MOAttributes.GetEPSMultiSC = eps;
      request.url = cai3gOptions.url;

      await cai3gLib.sendGet(request, headers).then((result) => {
        const response = parser.toJson(result.data, { object: true });

        responseData.result = response;

        if (response["S:Envelope"]["S:Body"]["cai3g:Fault"]) {
          responseData.reason = response["S:Envelope"]["S:Body"]["cai3g:Fault"]["cai3g:faultstring"];
          resolve(responseData);
          return;
        }

        responseData.ok = true;

        if (nodeType === "HLR") {
          if (
            response["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"]["hss:epsProfileId"]
          ) {
            responseData.epsProfileId =
              response["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"][
                "hss:epsProfileId"
              ];
          }
          responseData.imsi =
            response["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"]["hss:imsi"];
        } else responseData.result = result;

        console.debug(`Request type was ${nodeType} ... `, result);

        resolve(responseData);
      });
    } catch (error) {
      // The only instance that we need this is to evaluate the response status for is4GCompatible(...)
      //  So we need to ensure the error is returned as an object in these cases -- not an Error Class
      if (error.isAxiosError === true && error.response) return reject(error.response);

      console.error(`GET ${nodeType}Request failed: `, error.response || error);
      const exception = new Cai3gError(error.response || error, `GET ${nodeType}Request failed`);
      reject(exception);
    }
  });
}

function cai3gSet_HLRRequest(sessionId, msisdn, attributes) {
  return new Promise(async (resolve, reject) => {
    let responseData = { ok: false };
    try {
      const headers = new cai3gLib.Set.Header();
      const request = new cai3gLib.Set();
      headers.SessionId = sessionId;

      request.MOId = new cai3gLib.Set.MOId();
      request.MOId.msisdn = msisdn;

      request.MOAttributes = new cai3gLib.Set.MOAttributes();
      request.MOAttributes.SetEPSMultiSC = new cai3gLib.SetEPSMultiSC();
      Object.keys(attributes).forEach((key) => {
        request.MOAttributes.SetEPSMultiSC[key] = attributes[key];
      });

      request.url = cai3gOptions.url;

      await cai3gLib.sendSet(request, headers).then((result) => {
        const response = parser.toJson(result.data, { object: true });
        responseData.result = response;

        if (response["S:Envelope"]["S:Body"]["cai3g:Fault"]) {
          responseData.reason = response["S:Envelope"]["S:Body"]["cai3g:Fault"]["cai3g:faultstring"];
          resolve(responseData);
          return;
        }

        console.debug("SET Request result: ", result);

        responseData.ok = true;
        resolve(responseData);
      });
    } catch (error) {
      console.error(`SET HLRRequest failed: `, error.response || error);
      const exception = new Cai3gError(error.response || error, "SET HLRRequest failed");
      reject(exception);
    }
  });
}

function provision4G(ctx, subscriberNumber) {
  return new Promise(async (resolve, reject) => {
    try {
      const connectionInfo = getEndpointConnectionInfo(ctx);
      if (!subscriberNumber) throw new Cai3gError(null, 'Missing required field "subscriberNumber"');

      const activePool = await getActivePool(connectionInfo);
      const sessionId = await activePool.acquire();
      activePool.release(sessionId);

      let provisioned = false;

      let result = await is4GProvisioned(ctx, subscriberNumber);
      if (result === true) {
        provisioned = true;
        resolve(provisioned);
        return;
      }

      await cai3gSet_HLRRequest(sessionId, subscriberNumber, { epsProfileId: "1" }).then((result) => {
        if (!result.ok) return Promise.reject(result.reason);

        provisioned = true;
        console.info("Successful HLR SET Request");
        console.trace("HLR SET Result:", JSON.stringify(result));
      });

      resolve(provisioned);
    } catch (err) {
      const exception = new Cai3gError(err, "Failed provision4G(...) request");
      reject(exception);
    }
  });
}

function is4GProvisioned(ctx, subscriberNumber) {
  return new Promise(async (resolve, reject) => {
    try {
      const connectionInfo = getEndpointConnectionInfo(ctx);
      if (!subscriberNumber) throw new Cai3gError(null, 'Missing required field "subscriberNumber"');

      const activePool = await getActivePool(connectionInfo);
      const sessionId = await activePool.acquire();
      activePool.release(sessionId);

      let provisioned = false;

      await cai3gGet_HLRRequest(sessionId, subscriberNumber).then((result) => {
        if (!result.ok) return Promise.reject(result.reason);

        provisioned = result.epsProfileId === "1";
        console.info("Successful HLR GET Request");
        console.trace("HLR GET Result:", result);
      });

      resolve(provisioned);
    } catch (e) {
      const exception = new Cai3gError(e, "Failed is4GProvisioned(...) request");
      reject(exception);
    }
  });
}

function is4GCompatible(ctx, subscriberNumber) {
  return new Promise(async (resolve, reject) => {
    try {
      const connectionInfo = getEndpointConnectionInfo(ctx);
      if (!subscriberNumber) throw new Cai3gError(null, 'Missing required field "subscriberNumber"');

      const activePool = await getActivePool(connectionInfo);
      const sessionId = await activePool.acquire();
      activePool.release(sessionId);

      let is_4g_compatible = false;

      // used to throw AFTER connection is released
      let imsi = "";

      await cai3gGet_HLRRequest(sessionId, subscriberNumber).then((result) => {
        if (!result.ok) return Promise.reject(result.reason);

        imsi = result.imsi;
        console.info("Successful HLR GET Request");
        console.trace("HLR GET Result:", result);
      });

      await cai3gGet_AUCRequest(sessionId, imsi)
        .then((result) => {
          if (!result.ok) return Promise.reject(result.reason);

          is_4g_compatible = true;
          console.info("Successful AUC GET Request");
          console.trace("AUC GET Result:", result);
        })
        .catch((err) => {
          if (err.status != 302) {
            console.warn("Did not get 302 response status code, failing AUCRequest", err);
            return Promise.reject(err.reason);
          }

          // NOTE: this is an error code from the AUC request
          //  However it is a business logic error, so does not constitute a error in Coalesce
          console.info("AUC GET Request responded with 302 (i.e. Not 4G Compatible):", err);
        });

      resolve(is_4g_compatible);
    } catch (e) {
      const exception = new Cai3gError(e, "Failed is4GCompatible(...) request");
      reject(exception);
    }
  });
}

module.exports = {
  is4GCompatible,
  is4GProvisioned,
  provision4G,
  setEndpoint,
  setDefaultEndpoint,
};
