// This file is generated by a tool. Don't try to edit it manually!


const axios = require('axios'), parser = require('xml2json');
class CreateResponse {
	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	get extension() { return this._extension; }
	set extension(v) {
		this._extension = v;
	}

	encode() {
		let xml = '<cai3g:CreateResponse>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		if (this._extension) xml += '<cai3g:extension>' + this._extension + '</cai3g:extension>';
		xml += '</cai3g:CreateResponse>';
		return xml;
	}
}

CreateResponse.MOId = class {
	encode() {
		let xml = '<cai3g:MOId>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

CreateResponse.MOAttributes = class {
	encode() {
		let xml = '<cai3g:MOAttributes>';
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

CreateResponse.extension = class {
	encode() {
		let xml = '<cai3g:extension>';
		xml += '</cai3g:extension>';
		return xml;
	}
}

class SetResponse {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	get extension() { return this._extension; }
	set extension(v) {
		this._extension = v;
	}

	encode() {
		let xml = '<cai3g:SetResponse>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		if (this._extension) xml += '<cai3g:extension>' + this._extension + '</cai3g:extension>';
		xml += '</cai3g:SetResponse>';
		return xml;
	}
}

SetResponse.MOAttributes = class {
	encode() {
		let xml = '<cai3g:MOAttributes>';
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

SetResponse.extension = class {
	encode() {
		let xml = '<cai3g:extension>';
		xml += '</cai3g:extension>';
		return xml;
	}
}

class DeleteResponse {
	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		this._MOAttributes = v;
	}

	get extension() { return this._extension; }
	set extension(v) {
		this._extension = v;
	}

	encode() {
		let xml = '<cai3g:DeleteResponse>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		if (this._extension) xml += '<cai3g:extension>' + this._extension + '</cai3g:extension>';
		xml += '</cai3g:DeleteResponse>';
		return xml;
	}
}

DeleteResponse.MOId = class {
	encode() {
		let xml = '<cai3g:MOId>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

DeleteResponse.MOAttributes = class {
	encode() {
		let xml = '<cai3g:MOAttributes>';
		xml += '</cai3g:MOAttributes>';
		return xml;
	}
}

DeleteResponse.extension = class {
	encode() {
		let xml = '<cai3g:extension>';
		xml += '</cai3g:extension>';
		return xml;
	}
}

class Search {
	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		if (v.match(/^[A-Za-z][_A-Za-z0-9]*@.*$/)) {}
		else throw new Error('MoType pattern');
		this._MOType = v;
	}

	get filters() { return this._filters; }
	set filters(v) {
		this._filters = v;
	}

	get extension() { return this._extension; }
	set extension(v) {
		this._extension = v;
	}

	encode() {
		let xml = '<cai3g:Search>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._filters) xml += '<cai3g:filters>' + this._filters + '</cai3g:filters>';
		if (this._extension) xml += '<cai3g:extension>' + this._extension + '</cai3g:extension>';
		xml += '</cai3g:Search>';
		return xml;
	}
}

Search.filters = class {
	get filter() { return this._filter; }
	set filter(v) {
		this._filter = v;
	}

	encode() {
		let xml = '<cai3g:filters>';
		if (this._filter) xml += '<cai3g:filter>' + this._filter + '</cai3g:filter>';
		xml += '</cai3g:filters>';
		return xml;
	}
}

Search.filter = class {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:filter>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:filter>';
		return xml;
	}
}

Search.extension = class {
	encode() {
		let xml = '<cai3g:extension>';
		xml += '</cai3g:extension>';
		return xml;
	}
}

class SearchResponse {
	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get extension() { return this._extension; }
	set extension(v) {
		this._extension = v;
	}

	encode() {
		let xml = '<cai3g:SearchResponse>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._extension) xml += '<cai3g:extension>' + this._extension + '</cai3g:extension>';
		xml += '</cai3g:SearchResponse>';
		return xml;
	}
}

SearchResponse.MOId = class {
	encode() {
		let xml = '<cai3g:MOId>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

SearchResponse.extension = class {
	encode() {
		let xml = '<cai3g:extension>';
		xml += '</cai3g:extension>';
		return xml;
	}
}

class Login {
	get userId() { return this._userId; }
	set userId(v) {
		if (typeof v != 'string') throw new Error('userId must be a string');
		this._userId = v;
	}

	get pwd() { return this._pwd; }
	set pwd(v) {
		if (typeof v != 'string') throw new Error('pwd must be a string');
		this._pwd = v;
	}

	encode() {
		let xml = '<cai3g:Login>';
		if (this._userId) xml += '<cai3g:userId>' + this._userId + '</cai3g:userId>';
		if (this._pwd) xml += '<cai3g:pwd>' + this._pwd + '</cai3g:pwd>';
		xml += '</cai3g:Login>';
		return xml;
	}
}

class LoginResponse {
	get sessionId() { return this._sessionId; }
	set sessionId(v) {
		if (typeof v != 'string') throw new Error('sessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._sessionId = v;
	}

	get baseSequenceId() { return this._baseSequenceId; }
	set baseSequenceId(v) {
		if (typeof v != 'number') throw new Error('baseSequenceId must be a integer');
		this._baseSequenceId = v;
	}

	encode() {
		let xml = '<cai3g:LoginResponse>';
		if (this._sessionId) xml += '<cai3g:sessionId>' + this._sessionId + '</cai3g:sessionId>';
		if (this._baseSequenceId) xml += '<cai3g:baseSequenceId>' + this._baseSequenceId + '</cai3g:baseSequenceId>';
		xml += '</cai3g:LoginResponse>';
		return xml;
	}
}

class Logout {
	get sessionId() { return this._sessionId; }
	set sessionId(v) {
		if (typeof v != 'string') throw new Error('sessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._sessionId = v;
	}

	encode() {
		let xml = '<cai3g:Logout>';
		if (this._sessionId) xml += '<cai3g:sessionId>' + this._sessionId + '</cai3g:sessionId>';
		xml += '</cai3g:Logout>';
		return xml;
	}
}

class Subscribe {
	get managerRef() { return this._managerRef; }
	set managerRef(v) {
		this._managerRef = v;
	}

	get filters() { return this._filters; }
	set filters(v) {
		this._filters = v;
	}

	encode() {
		let xml = '<cai3g:Subscribe>';
		if (this._filters) xml += '<cai3g:filters>' + this._filters + '</cai3g:filters>';
		xml += '</cai3g:Subscribe>';
		return xml;
	}
}

Subscribe.filters = class {
	get filter() { return this._filter; }
	set filter(v) {
		this._filter = v;
	}

	encode() {
		let xml = '<cai3g:filters>';
		if (this._filter) xml += '<cai3g:filter>' + this._filter + '</cai3g:filter>';
		xml += '</cai3g:filters>';
		return xml;
	}
}

Subscribe.filter = class {
	get cai3gUser() { return this._cai3gUser; }
	set cai3gUser(v) {
		if (typeof v != 'string') throw new Error('cai3gUser must be a string');
		this._cai3gUser = v;
	}

	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get operation() { return this._operation; }
	set operation(v) {
		if (typeof v != 'string') throw new Error('operation must be a string');
		if (!['Create','Delete','Set'].includes(v)) throw new Error('operation enum');
		this._operation = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		if (typeof v != 'string') throw new Error('MOId must be a string');
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:filter>';
		if (this._cai3gUser) xml += '<cai3g:cai3gUser>' + this._cai3gUser + '</cai3g:cai3gUser>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._operation) xml += '<cai3g:operation>' + this._operation + '</cai3g:operation>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:filter>';
		return xml;
	}
}

class SubscribeResponse {
	get subscriptionId() { return this._subscriptionId; }
	set subscriptionId(v) {
		if (typeof v != 'string') throw new Error('subscriptionId must be a string');
		this._subscriptionId = v;
	}

	encode() {
		let xml = '<cai3g:SubscribeResponse>';
		if (this._subscriptionId) xml += '<cai3g:subscriptionId>' + this._subscriptionId + '</cai3g:subscriptionId>';
		xml += '</cai3g:SubscribeResponse>';
		return xml;
	}
}

class Unsubscribe {
	get managerRef() { return this._managerRef; }
	set managerRef(v) {
		this._managerRef = v;
	}

	get subscriptionId() { return this._subscriptionId; }
	set subscriptionId(v) {
		if (typeof v != 'string') throw new Error('subscriptionId must be a string');
		this._subscriptionId = v;
	}

	encode() {
		let xml = '<cai3g:Unsubscribe>';
		if (this._subscriptionId) xml += '<cai3g:subscriptionId>' + this._subscriptionId + '</cai3g:subscriptionId>';
		xml += '</cai3g:Unsubscribe>';
		return xml;
	}
}

class Notify {
	get notificationHeader() { return this._notificationHeader; }
	set notificationHeader(v) {
		this._notificationHeader = v;
	}

	get correlatedNotifications() { return this._correlatedNotifications; }
	set correlatedNotifications(v) {
		if (typeof v != 'string') throw new Error('correlatedNotifications must be a string');
		this._correlatedNotifications = v;
	}

	get additionalText() { return this._additionalText; }
	set additionalText(v) {
		if (typeof v != 'string') throw new Error('additionalText must be a string');
		this._additionalText = v;
	}

	get sourceIndicator() { return this._sourceIndicator; }
	set sourceIndicator(v) {
		this._sourceIndicator = v;
	}

	get notificationData() { return this._notificationData; }
	set notificationData(v) {
		this._notificationData = v;
	}

	encode() {
		let xml = '<cai3g:Notify>';
		if (this._notificationHeader) xml += '<cai3g:notificationHeader>' + this._notificationHeader + '</cai3g:notificationHeader>';
		if (this._correlatedNotifications) xml += '<cai3g:correlatedNotifications>' + this._correlatedNotifications + '</cai3g:correlatedNotifications>';
		if (this._additionalText) xml += '<cai3g:additionalText>' + this._additionalText + '</cai3g:additionalText>';
		if (this._notificationData) xml += '<cai3g:notificationData>' + this._notificationData.encode() + '</cai3g:notificationData>';
		xml += '</cai3g:Notify>';
		return xml;
	}
}

Notify.notificationHeader = class {
	get cai3gUser() { return this._cai3gUser; }
	set cai3gUser(v) {
		if (typeof v != 'string') throw new Error('cai3gUser must be a string');
		this._cai3gUser = v;
	}

	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		if (v.match(/^[A-Za-z][_A-Za-z0-9]*@.*$/)) {}
		else throw new Error('MoType pattern');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get notificationId() { return this._notificationId; }
	set notificationId(v) {
		if (typeof v != 'string') throw new Error('notificationId must be a string');
		this._notificationId = v;
	}

	get eventTime() { return this._eventTime; }
	set eventTime(v) {
		this._eventTime = v;
	}

	get notificationActor() { return this._notificationActor; }
	set notificationActor(v) {
		this._notificationActor = v;
	}

	get operation() { return this._operation; }
	set operation(v) {
		if (typeof v != 'string') throw new Error('operation must be a string');
		if (!['Create','Delete','Set'].includes(v)) throw new Error('operation enum');
		this._operation = v;
	}

	get subscriptionId() { return this._subscriptionId; }
	set subscriptionId(v) {
		if (typeof v != 'string') throw new Error('subscriptionId must be a string');
		this._subscriptionId = v;
	}

	encode() {
		let xml = '<cai3g:notificationHeader>';
		if (this._cai3gUser) xml += '<cai3g:cai3gUser>' + this._cai3gUser + '</cai3g:cai3gUser>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._notificationId) xml += '<cai3g:notificationId>' + this._notificationId + '</cai3g:notificationId>';
		if (this._eventTime) xml += '<cai3g:eventTime>' + this._eventTime + '</cai3g:eventTime>';
		if (this._operation) xml += '<cai3g:operation>' + this._operation + '</cai3g:operation>';
		if (this._subscriptionId) xml += '<cai3g:subscriptionId>' + this._subscriptionId + '</cai3g:subscriptionId>';
		xml += '</cai3g:notificationHeader>';
		return xml;
	}
}

Notify.MOId = class {
	encode() {
		let xml = '<cai3g:MOId>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

Notify.notificationData = class {
	encode() {
		let xml = '<cai3g:notificationData>';
		xml += '</cai3g:notificationData>';
		return xml;
	}
}

class Cai3gFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'number') throw new Error('faultcode must be a integer');
		this._faultcode = v;
	}

	get faultreason() { return this._faultreason; }
	set faultreason(v) {
		this._faultreason = v;
	}

	get faultrole() { return this._faultrole; }
	set faultrole(v) {
		if (typeof v != 'string') throw new Error('faultrole must be a string');
		this._faultrole = v;
	}

	get details() { return this._details; }
	set details(v) {
		this._details = v;
	}

	encode() {
		let xml = '<cai3g:Cai3gFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		if (this._faultreason) xml += '<cai3g:faultreason>' + this._faultreason.encode() + '</cai3g:faultreason>';
		if (this._faultrole) xml += '<cai3g:faultrole>' + this._faultrole + '</cai3g:faultrole>';
		if (this._details) xml += '<cai3g:details>' + this._details.encode() + '</cai3g:details>';
		xml += '</cai3g:Cai3gFault>';
		return xml;
	}
}

Cai3gFault.faultreason = class {
	get reasonText() { return this._reasonText; }
	set reasonText(v) {
		if (typeof v != 'string') throw new Error('reasonText must be a string');
		this._reasonText = v;
	}

	encode() {
		let xml = '<cai3g:faultreason>';
		if (this._reasonText) xml += '<cai3g:reasonText>' + this._reasonText + '</cai3g:reasonText>';
		xml += '</cai3g:faultreason>';
		return xml;
	}
}

Cai3gFault.details = class {
	encode() {
		let xml = '<cai3g:details>';
		xml += '</cai3g:details>';
		return xml;
	}
}

class SessionIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid SessionId','Session Timeout','SessionId Syntax Error'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:SessionIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:SessionIdFault>';
		return xml;
	}
}

class SequenceIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid SequenceId'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:SequenceIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:SequenceIdFault>';
		return xml;
	}
}

class TransactionIdFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid TransactionId'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:TransactionIdFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:TransactionIdFault>';
		return xml;
	}
}

class ContextFault {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'string') throw new Error('faultcode must be a string');
		if (!['Invalid Context'].includes(v)) throw new Error('faultcode enum');
		this._faultcode = v;
	}

	encode() {
		let xml = '<cai3g:ContextFault>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		xml += '</cai3g:ContextFault>';
		return xml;
	}
}

class ResponseMOAttributesType {
	encode() {
		let xml = '<cai3g:ResponseMOAttributesType>';
		xml += '</cai3g:ResponseMOAttributesType>';
		return xml;
	}
}

class AnyMOIdType {
	encode() {
		let xml = '<cai3g:AnyMOIdType>';
		xml += '</cai3g:AnyMOIdType>';
		return xml;
	}
}

class AnySequenceType {
	encode() {
		let xml = '<cai3g:AnySequenceType>';
		xml += '</cai3g:AnySequenceType>';
		return xml;
	}
}

class SearchFiltersType {
	get filter() { return this._filter; }
	set filter(v) {
		this._filter = v;
	}

	encode() {
		let xml = '<cai3g:SearchFiltersType>';
		if (this._filter) xml += '<cai3g:filter>' + this._filter + '</cai3g:filter>';
		xml += '</cai3g:SearchFiltersType>';
		return xml;
	}
}

SearchFiltersType.filter = class {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:filter>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:filter>';
		return xml;
	}
}

class SearchFilterType {
	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:SearchFilterType>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:SearchFilterType>';
		return xml;
	}
}

class NotificationHeaderType {
	get cai3gUser() { return this._cai3gUser; }
	set cai3gUser(v) {
		if (typeof v != 'string') throw new Error('cai3gUser must be a string');
		this._cai3gUser = v;
	}

	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		if (v.match(/^[A-Za-z][_A-Za-z0-9]*@.*$/)) {}
		else throw new Error('MoType pattern');
		this._MOType = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		this._MOId = v;
	}

	get notificationId() { return this._notificationId; }
	set notificationId(v) {
		if (typeof v != 'string') throw new Error('notificationId must be a string');
		this._notificationId = v;
	}

	get eventTime() { return this._eventTime; }
	set eventTime(v) {
		this._eventTime = v;
	}

	get notificationActor() { return this._notificationActor; }
	set notificationActor(v) {
		this._notificationActor = v;
	}

	get operation() { return this._operation; }
	set operation(v) {
		if (typeof v != 'string') throw new Error('operation must be a string');
		if (!['Create','Delete','Set'].includes(v)) throw new Error('operation enum');
		this._operation = v;
	}

	get subscriptionId() { return this._subscriptionId; }
	set subscriptionId(v) {
		if (typeof v != 'string') throw new Error('subscriptionId must be a string');
		this._subscriptionId = v;
	}

	encode() {
		let xml = '<cai3g:NotificationHeaderType>';
		if (this._cai3gUser) xml += '<cai3g:cai3gUser>' + this._cai3gUser + '</cai3g:cai3gUser>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._notificationId) xml += '<cai3g:notificationId>' + this._notificationId + '</cai3g:notificationId>';
		if (this._eventTime) xml += '<cai3g:eventTime>' + this._eventTime + '</cai3g:eventTime>';
		if (this._operation) xml += '<cai3g:operation>' + this._operation + '</cai3g:operation>';
		if (this._subscriptionId) xml += '<cai3g:subscriptionId>' + this._subscriptionId + '</cai3g:subscriptionId>';
		xml += '</cai3g:NotificationHeaderType>';
		return xml;
	}
}

NotificationHeaderType.MOId = class {
	encode() {
		let xml = '<cai3g:MOId>';
		xml += '</cai3g:MOId>';
		return xml;
	}
}

class NotificationFilterType {
	get cai3gUser() { return this._cai3gUser; }
	set cai3gUser(v) {
		if (typeof v != 'string') throw new Error('cai3gUser must be a string');
		this._cai3gUser = v;
	}

	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get operation() { return this._operation; }
	set operation(v) {
		if (typeof v != 'string') throw new Error('operation must be a string');
		if (!['Create','Delete','Set'].includes(v)) throw new Error('operation enum');
		this._operation = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		if (typeof v != 'string') throw new Error('MOId must be a string');
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:NotificationFilterType>';
		if (this._cai3gUser) xml += '<cai3g:cai3gUser>' + this._cai3gUser + '</cai3g:cai3gUser>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._operation) xml += '<cai3g:operation>' + this._operation + '</cai3g:operation>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:NotificationFilterType>';
		return xml;
	}
}

class NotificationFiltersType {
	get filter() { return this._filter; }
	set filter(v) {
		this._filter = v;
	}

	encode() {
		let xml = '<cai3g:NotificationFiltersType>';
		if (this._filter) xml += '<cai3g:filter>' + this._filter + '</cai3g:filter>';
		xml += '</cai3g:NotificationFiltersType>';
		return xml;
	}
}

NotificationFiltersType.filter = class {
	get cai3gUser() { return this._cai3gUser; }
	set cai3gUser(v) {
		if (typeof v != 'string') throw new Error('cai3gUser must be a string');
		this._cai3gUser = v;
	}

	get MOType() { return this._MOType; }
	set MOType(v) {
		if (typeof v != 'string') throw new Error('MOType must be a string');
		this._MOType = v;
	}

	get operation() { return this._operation; }
	set operation(v) {
		if (typeof v != 'string') throw new Error('operation must be a string');
		if (!['Create','Delete','Set'].includes(v)) throw new Error('operation enum');
		this._operation = v;
	}

	get MOId() { return this._MOId; }
	set MOId(v) {
		if (typeof v != 'string') throw new Error('MOId must be a string');
		this._MOId = v;
	}

	get MOAttributes() { return this._MOAttributes; }
	set MOAttributes(v) {
		if (typeof v != 'string') throw new Error('MOAttributes must be a string');
		this._MOAttributes = v;
	}

	encode() {
		let xml = '<cai3g:filter>';
		if (this._cai3gUser) xml += '<cai3g:cai3gUser>' + this._cai3gUser + '</cai3g:cai3gUser>';
		if (this._MOType) xml += '<cai3g:MOType>' + this._MOType + '</cai3g:MOType>';
		if (this._operation) xml += '<cai3g:operation>' + this._operation + '</cai3g:operation>';
		if (this._MOId) xml += '<cai3g:MOId>' + this._MOId + '</cai3g:MOId>';
		if (this._MOAttributes) xml += '<cai3g:MOAttributes>' + this._MOAttributes + '</cai3g:MOAttributes>';
		xml += '</cai3g:filter>';
		return xml;
	}
}

class Cai3gFaultType {
	get faultcode() { return this._faultcode; }
	set faultcode(v) {
		if (typeof v != 'number') throw new Error('faultcode must be a integer');
		this._faultcode = v;
	}

	get faultreason() { return this._faultreason; }
	set faultreason(v) {
		this._faultreason = v;
	}

	get faultrole() { return this._faultrole; }
	set faultrole(v) {
		if (typeof v != 'string') throw new Error('faultrole must be a string');
		this._faultrole = v;
	}

	get details() { return this._details; }
	set details(v) {
		this._details = v;
	}

	encode() {
		let xml = '<cai3g:Cai3gFaultType>';
		if (this._faultcode) xml += '<cai3g:faultcode>' + this._faultcode + '</cai3g:faultcode>';
		if (this._faultreason) xml += '<cai3g:faultreason>' + this._faultreason.encode() + '</cai3g:faultreason>';
		if (this._faultrole) xml += '<cai3g:faultrole>' + this._faultrole + '</cai3g:faultrole>';
		if (this._details) xml += '<cai3g:details>' + this._details.encode() + '</cai3g:details>';
		xml += '</cai3g:Cai3gFaultType>';
		return xml;
	}
}

Cai3gFaultType.faultreason = class {
	get reasonText() { return this._reasonText; }
	set reasonText(v) {
		if (typeof v != 'string') throw new Error('reasonText must be a string');
		this._reasonText = v;
	}

	encode() {
		let xml = '<cai3g:faultreason>';
		if (this._reasonText) xml += '<cai3g:reasonText>' + this._reasonText + '</cai3g:reasonText>';
		xml += '</cai3g:faultreason>';
		return xml;
	}
}

Cai3gFaultType.details = class {
	encode() {
		let xml = '<cai3g:details>';
		xml += '</cai3g:details>';
		return xml;
	}
}

class HeaderFaultType {
	get faultactor() { return this._faultactor; }
	set faultactor(v) {
		if (typeof v != 'string') throw new Error('faultactor must be a string');
		this._faultactor = v;
	}

	get description() { return this._description; }
	set description(v) {
		if (typeof v != 'string') throw new Error('description must be a string');
		this._description = v;
	}

	encode() {
		let xml = '<cai3g:HeaderFaultType>';
		if (this._faultactor) xml += '<cai3g:faultactor>' + this._faultactor + '</cai3g:faultactor>';
		if (this._description) xml += '<cai3g:description>' + this._description + '</cai3g:description>';
		xml += '</cai3g:HeaderFaultType>';
		return xml;
	}
}

async function sendRequest(action, req, headers) {
	let xml = '<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/" xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/">';
	if (headers) xml += headers.encode();
	else xml += '<S:Header/>';
	xml += '<S:Body>' + req.encode() + '</S:Body></S:Envelope>';

	return new Promise((resolve, reject) => {
		axios.post(req.url, xml, {
			headers: { "SOAPAction": action, "content-type": "text/xml" }
		}).then((response) => {
			resolve(response);
		}).catch((error) => {
			reject(error);
		});
	});
}

Logout.Header = class {
	get SessionId() { return this._SessionId; }
	set SessionId(v) {
		if (typeof v != 'string') throw new Error('SessionId must be a string');
		if (v.match(/^[\d\w]{1,}$/)) {}
		else throw new Error('SessionIdType pattern');
		this._SessionId = v;
	}

	encode() {
		let xml = '<S:Header>';
		if (this._SessionId) xml += '<cai3g:SessionId>' + this._SessionId + '</cai3g:SessionId>';
		xml += '</S:Header>';
		return xml;
	}
}

module.exports.SessionControl = {
	CreateResponse,
	SetResponse,
	DeleteResponse,
	Search,
	SearchResponse,
	Login,
	LoginResponse,
	Logout,
	Subscribe,
	SubscribeResponse,
	Unsubscribe,
	Notify,
	SessionIdFault,
	SequenceIdFault,
	TransactionIdFault,
	ContextFault,
	ResponseMOAttributesType,
	AnyMOIdType,
	AnySequenceType,
	SearchFiltersType,
	SearchFilterType,
	NotificationHeaderType,
	NotificationFilterType,
	NotificationFiltersType,
	Cai3gFaultType,
	HeaderFaultType,
	sendLogin: function (req) {
		if (!(req instanceof Login)) throw new Error('Invalid request type')
		return sendRequest('CAI3G#Login', req);
	},
	sendLogout: function (req, headers) {
		if (!(req instanceof Logout)) throw new Error('Invalid request type')
		if (!(headers instanceof Logout.Header)) throw new Error('Invalid headers type')
		return sendRequest('CAI3G#Logout', req, headers);
	},
}
