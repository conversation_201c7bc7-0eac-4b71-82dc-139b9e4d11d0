const { callComzaAcs, setDefaultEndpoint, setEndpoint } = require('./comzaAcsUtils');

const advance = async (ctx, requestParams) => {
  const params = {
    method: 'advance',
    mandatoryParams: ['TransactionId', `ApplicationId/ShortCode`, 'Msisdn', 'ServiceId'],
    requestParams,
  };
  return callComzaAcs(ctx, params);
};
const statusCheck = async (ctx, requestParams) => {
  const params = {
    method: 'statusCheck',
    mandatoryParams: ['TransactionId', `ApplicationId/ShortCode`, 'Msisdn'],
    requestParams,
  };
  return callComzaAcs(ctx, params);
};

module.exports = {
  setEndpoint,
  setDefaultEndpoint,
  advance,
  statusCheck,
};
