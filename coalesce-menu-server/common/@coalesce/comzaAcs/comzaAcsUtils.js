const axios = require("axios");
const CoalesceError = require("@coalesce/coalesceError");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");
const xmlrpc = require("@coalesce/xmlrpc");

const comzaAcsEndpointManager = new EndpointManager("comzaAcs");

class ComzaAcsError extends CoalesceError {
  constructor(...params) {
    super(ComzaAcsError, ...params);
  }
}

const DEFAULT_ENDPOINT_NAME = "acs";

/*
 * Overwritten by default endpoint .... OR by setEndpoint() method inside a GUI component
 *
 */
let currentEndpointName = DEFAULT_ENDPOINT_NAME;

const setEndpoint = (replacementEndpointName) => {
  currentEndpointName = replacementEndpointName;
};

const setDefaultEndpoint = () => {
  currentEndpointName = DEFAULT_ENDPOINT_NAME;
};

const getEndpointConnectionInfo = (ctx) => {
  let connectionInfo = {};

  if (!currentEndpointName) console.warn("Setting to default endpoint: ", DEFAULT_ENDPOINT_NAME);

  comzaAcsEndpointManager.setEndpointInContext(currentEndpointName || DEFAULT_ENDPOINT_NAME, ctx);
  connectionInfo = comzaAcsEndpointManager.getEndpointFromContext(ctx);

  return connectionInfo;
};
const checkRequestParams = (mandatoryParams, requestParams) => {
  try {
    for (let p of mandatoryParams) {
      let paramFound = false;
      for (let param in requestParams) {
        if (param === p) {
          paramFound = true;
          break;
        } else continue;
      }
      if (!paramFound) {
        throw new ComzaAcsError(null, "Missing Mandatory comzaAcs Parameter");
      }
    }
    return;
  } catch (error) {
    throw new ComzaAcsError(null, error.message);
  }
};
const sendRequest = (ctx, requestType, requestParams) => {
  return new Promise(async (resolve, reject) => {
    try {
      console.info(`Making comzAfrica '${requestType}' Request`);
      const connectionInfo = getEndpointConnectionInfo(ctx);
      if (!connectionInfo) throw new ComzaAcsError(null, "Connector details missing");

      const { hostname, port, url, authType, authDetails, protocol, timeout } = connectionInfo;

      if (!hostname || !port || !url || !protocol)
        throw new ComzaAcsError(null, "Connector hostname/port/url/protocol missing");

      // check connector auth
      if (authType === "basic" && (!authDetails.username || !authDetails.password)) {
        throw new ComzaAcsError(null, "Unauthorized Error");
      }

      const encodedUriPath = encodeURIPath(String(url).replace(/^\//, ""));

      const fullUrl = `${protocol}://${String(hostname)}:${String(port)}/${encodedUriPath}`;

      const { username, password } = authDetails;
      const base64Header = Buffer.from(`${username}:${password}`).toString("base64"); // convert username:password to base64

      const headers = {
        "Content-Type": "text/xml",
        Authorization: `Basic ${base64Header}`,
      };

      const requestBody = `<?xml version="1.0"?>` + xmlrpc.encodeRequest(requestType, requestParams);

      console.trace(`comzAfrica Request: `, JSON.stringify({ fullUrl, headers, requestBody }));

      const response = await axios.post(fullUrl, requestBody, {
        headers,
        timeout: parseInt(timeout),
      });

      resolve(response.data);
    } catch (e) {
      let error = new ComzaAcsError(e, `ComzaAcs sendRequest method for (${requestType} request) failed`);
      console.error(error);
      reject(error);
    }
  });
};

const callComzaAcs = (ctx, { method, mandatoryParams, requestParams }) => {
  return new Promise(async (resolve, reject) => {
    try {
      checkRequestParams(mandatoryParams, requestParams);

      const xmlResponse = await sendRequest(ctx, method, requestParams);

      const parsedResponse = xmlrpc.parseResponse(xmlResponse);
      console.trace("comzAfrica JSON Response:", JSON.stringify(parsedResponse));

      if (parsedResponse.faultCode || parsedResponse.faultString) {
        throw new ComzaAcsError(null, JSON.stringify(parsedResponse));
      }

      return resolve(parsedResponse);
    } catch (e) {
      let error = new ComzaAcsError(e, `ComzaAcs 'callComzaAcs' method for (${method} request) failed`);
      console.error("error/fault in comzAfrica callComzaAcs method:", error);
      reject(error);
    }
  });
};

module.exports = {
  callComzaAcs,
  setEndpoint,
  setDefaultEndpoint,
};
