const componentManager = require('../../server/componentManager'),
  { isPreviewer } = require('@coalesce/env');

const CTX_UID = '_menuCtx';

// Menu.js
class Menu {
  constructor() {
    this.items = [];
    this.output = '';
  }

  static hasNestedContext(rootCtx) {
    return !!rootCtx[CTX_UID];
  }

  static getNestedContext(rootCtx) {
    return rootCtx[CTX_UID];
  }

  static forNestedContextIn(rootCtx) {
    return {
      write: (key, value) => {
        if (rootCtx[CTX_UID]) rootCtx[CTX_UID][key] = value;
        else throw new Error('Did you forget to create a nested context for this menu?');
      },
    };
  }

  addItem(text, cb) {
    this.items.push(cb);
    this.output += `${this.items.length} ${text}\n`;
  }

  addText(text) {
    this.output += text + '\n';
  }

  static _update(rootCtx) {
    return {
      with: (localCtx) => {
        rootCtx[CTX_UID] = localCtx;
      },
    };
  }

  async execute(rootCtx) {
    const localCtx = rootCtx[CTX_UID];

    const nestedMenus = localCtx.menus.pop();
    if (nestedMenus) {
      await nestedMenus.execute(rootCtx);
      if (localCtx.waiting) {
        localCtx.menus.push(nestedMenus);
      }

      Menu._update(rootCtx).with(localCtx);
      return;
    }

    if (localCtx.waiting) {
      localCtx.waiting = false;
      localCtx.output = '';

      const cb = localCtx.cb.pop();
      Menu._update(rootCtx).with(localCtx);
      if (cb) return await cb(rootCtx);

      const i = Number(localCtx.input);
      if (isNaN(i) || i > this.items.length) {
        //console.error('Invaid user iput');
      } else {
        Menu._update(rootCtx).with(localCtx);
        await this.items[i - 1](rootCtx);
        return;
      }
    } else {
      localCtx.menus.pop();
    }

    localCtx.waiting = true;
    localCtx.output = this.output;

    Menu._update(rootCtx).with(localCtx);
  }

  static ask(rootCtx, text, cb) {
    const localCtx = rootCtx[CTX_UID];

    localCtx.cb.push(cb);

    localCtx.waiting = true;
    localCtx.output = text + '\n';

    Menu._update(rootCtx).with(localCtx);
  }

  static display(rootCtx, text) {
    rootCtx[CTX_UID].output += text;
  }

  static callMenu(rootCtx, m) {
    rootCtx[CTX_UID].menus.push(m);
    m.execute(rootCtx);
  }

  /*

	:::::::::::::::::
	:::: WARNING ::::
	:::::::::::::::::


	When we enable this again ... be sure to test it thoroughly.
	
	We have moved the context around dramatically (segregating root from local contexts) - and this code was explicitly disabled by stakeholders at the time of the change.
	As such although the context moving was done in this code ..... it is untested


	:::::::::::::::::
	:::: WARNING ::::
	:::::::::::::::::



  static callComponent(rootCtx, params, cb) {
    return this.callComponentInternal(componentManager, rootCtx, params, cb);
  }

  static callComponentDebug(rootCtx, params, cb) {
    return this.callComponentInternal(ctx.vars.componentManager || componentManager, rootCtx, params, cb);
  }

  static async callComponentInternal(theComponentManager, rootCtx, { name }, cb) {
    const localCtx = rootCtx[CTX_UID];

    const dummyMenu = {
      execute: async () => {
        await theComponentManager.execute(rootCtx, name);

        const componentManagerCtx = rootCtx.components.get(name);

        ctx.waiting = !componentManagerCtx.done;
        //ctx.response = rootCtx.variables.response;
        ctx.response = componentManagerCtx.output;

        if (cb) cb(rootCtx);
      },
    };

    await dummyMenu.execute();

    if (localCtx.waiting) localCtx.menus.push(dummyMenu);

    this._update(rootCtx).with(localCtx);
	}
	*/

  // static back(ctx) {
  // 	const m = ctx.menus.pop();
  // 	if (m) {
  // 		ctx.waiting = false;
  // 		return m.execute(ctx);
  // 	}
  // }

  static createNestedContext(rootCtx) {
    rootCtx[CTX_UID] = { cb: [], menus: [], output: '' };
  }
}

/*
if (isPreviewer) {
	Menu.callComponent = Menu.callComponentDebug;
}
*/

module.exports = Menu;
