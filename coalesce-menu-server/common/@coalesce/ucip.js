const { performance } = require('perf_hooks');
const xmlrpc = require("@coalesce/xmlrpc");
const { ComponentRequestError, sendRequest, getOptions } = require("@coalesce/request");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");
const endpointConnectionIndexer = require('@coalesce/EndpointConnectionIndexer');

const ENDPOINT_TYPE = "ucip";

const ucipEndpointManager = new EndpointManager(ENDPOINT_TYPE);

const DEFAULT_ENDPOINT_NAME = "air";

const LIBRARY_NAME = "@coalesce/ucip"

/**
 * For description around code 9999, see @coalesce/xmlrpc.js 'parseResponse(...)' method
 */
const UCIP_CODES_FOR_CONNECTION_CYCLE = [100, 999, 9999];

const UCIP_ERROR_MAP = {
    0: 'Successful',
    100: 'Other Error',
    102: 'Susbcriber not found',
    104: 'Temporary blocked',
    115: 'Refill not accepted',
    999: 'Other Error No Retry',
}

class XMLRPCFaultException extends CoalesceError {
    constructor(airurl, faultJson, ...params) {
        super(XMLRPCFaultException, null, ...params);
        this.airUrl = airurl;
        this.xmlAsJsonString = JSON.stringify(faultJson, null, 2);
        this.faultCode = faultJson?.faultCode || '?no-faultCode?';
        this.faultString = faultJson?.faultString || '?no-faultString?';
    }

    getFaultDescription() {
        return this.faultCode + ':' + this.faultString;
    }
}

class UCIPProtocolException extends CoalesceError {
    constructor(airurl, code, ...params) {
        super(UCIPProtocolException, null, ...params);
        this.airUrl = airurl;
        this.responseCode = code;
    }

    static getCodeDescription(code) {
        const description = UCIP_ERROR_MAP[code] || '-see-ucip-doc-for-responseCode-description-';
        return `UCIP:${code}:${description}`;
    }
}

class UCIPLibraryException extends CoalesceError {
    constructor(...params) {
        super(UCIPLibraryException, ...params);
    }
}

const check = (item) => {
    return {
        containsAny: (params) => {
            if ((!Array.isArray(item) && typeof item !== "object") || !Array.isArray(params)) {
                console.error(
                    "Cannot check for contained parameters - one of the options for comparison is invalid: ",
                    item,
                    params
                );
                return true;
            }

            const rootParams = !Array.isArray(item) ? Object.keys(item) : item;

            let containsAny = false;
            params.findIndex((param) => {
                if (rootParams.includes(param)) containsAny = true;

                return containsAny;
            });

            return containsAny;
        },
        isMissingMandatoryParams: (mandatoryParams) => {
            if ((!Array.isArray(item) && typeof item !== "object") || !Array.isArray(mandatoryParams)) {
                console.error(
                    "Cannot check for missing params - one of the options for comparison is invalid: ",
                    item,
                    mandatoryParams
                );
                return true;
            }

            const params = !Array.isArray(item) ? Object.keys(item) : item;

            const indexOfMissing = mandatoryParams.findIndex((param) => {
                //
                return !params.includes(param);
            });

            if (indexOfMissing !== -1) {
                console.warn("Missing mandatory UCIP param: " + mandatoryParams[indexOfMissing]);
                return true;
            }

            return false;
        },
    };
};

/**
 * Performs a deep merge of objects and returns new object. Does not modify
 * objects (immutable) and merges arrays via concatenation.
 *
 * @param {...object} objects - Objects to merge
 * @returns {object} New object with merged key/values
 */
function mergeDeep(...objects) {
    const isObject = (obj) => obj && typeof obj === "object";

    return objects.reduce((prev, obj) => {
        Object.keys(obj).forEach((key) => {
            const pVal = prev[key];
            const oVal = obj[key];

            if (Array.isArray(pVal) && Array.isArray(oVal)) {
                prev[key] = pVal.concat(...oVal);
            } else if (isObject(pVal) && isObject(oVal)) {
                prev[key] = mergeDeep(pVal, oVal);
            } else {
                prev[key] = oVal;
            }
        });

        return prev;
    }, {});
}

function addCommonParams(params, ctx) {
    const endpoint = ucipEndpointManager.getEndpointFromContext(ctx);

    console.debug("Setting originTransactionID to ", ctx.request.TransactionId);

    const mandatory = {
        originNodeType: endpoint.hostIdentity.originNodeType,
        originHostName: endpoint.hostIdentity.originHostName,

        originTransactionID: String(ctx.request.TransactionId),

        originTimeStamp: new Date(),
        subscriberNumber: String(ctx.request.MSISDN),
    };

    const callParams = mergeDeep(mandatory, params);
    return callParams;
}

const getEndpoint = (ctx) => {
    if (typeof ctx !== "object" || typeof ctx.menuState !== "object") {
        throw new Error("Invalid context provided in getEndpoint(...) method");
    }

    return ctx.menuState.ucipEndpointName || DEFAULT_ENDPOINT_NAME;
};

const setEndpoint = (newName, ctx) => {
    ucipEndpointManager.setEndpointInContext(newName || DEFAULT_ENDPOINT_NAME, ctx);
};

const setEndpointIfNotSet = (ctx) => {
    const endpointName = ctx?.menuState?.connectors?.ucip?.endpointName;
    setEndpoint(endpointName, ctx);
};

async function callAir(params, ctx, options) {
    let ucipCallSubscriberNumber = 'n/a';

    /**
     * FIXME -- Handle validation separately to the actual AIR PORT request
     */
    const startTime = performance.now()
    return new Promise(async (resolve, reject) => {
        let airurl = "n/a";
        try {
            const { ucipCallName } = options || {};

            console.info(`Making UCIP '${ucipCallName}' Request`);

            validateParams(params, ucipCallName);

            setEndpointIfNotSet(ctx);

            const endpointConfig = ucipEndpointManager.getEndpointFromContext(ctx);

            const callParams = addCommonParams(params, ctx);

            callParams.subscriberNumber = formatSubscriberNumber(endpointConfig, callParams.subscriberNumber);
            ucipCallSubscriberNumber = callParams.subscriberNumber;

            const { hostname, port, url, auth, subscriberNumberNAI } = endpointConfig;

            if (subscriberNumberNAI || subscriberNumberNAI === 0) callParams.subscriberNumberNAI = Number(subscriberNumberNAI);

            const encodedUrl = encodeURIPath(String(url).replace(/^\//, ""));

            console.debug("EncodedUrl", encodedUrl);
            airurl = `http://${hostname}:${port}/${encodedUrl}`;

            // Force basic - in case it is empty (default)
            if (auth && auth.username && auth.password) auth.type = "basic";
            endpointConfig.auth = auth;

            let { headers, timeout } = getOptions(endpointConfig);
            let headers_text = JSON.stringify(headers);
            const body = xmlrpc.encodeRequest(ucipCallName, callParams);

            console.trace("= Sending request to AIR");
            console.trace(`= URL ${airurl}`);
            console.trace(`= Request Headers ${headers_text}`);
            console.trace(`= Request ${body}`);

            const endpointConnectionIndexKey =
                `${ENDPOINT_TYPE}.` + ucipEndpointManager.getEndpointNameFromContext(ctx);

            let xml = "";

            let airResponse;

            console.info(`LIB: ${LIBRARY_NAME} | START EXT Endpoint [${airurl}] ${options.ucipCallName} subject [${ucipCallSubscriberNumber}]`);

            try {
                airResponse = await sendRequest({ url: airurl, body, headers, timeout });
            } catch (e) {
                endpointConnectionIndexer.cycleConnectionIndex(endpointConnectionIndexKey);
                throw e;
            }

            xml = airResponse.data;
            console.trace(`= Response headers ${JSON.stringify(airResponse.headers)}\n`);

            const response = xmlrpc.parseResponse(xml, airResponse.headers);
            console.trace("= AIR response: ", JSON.stringify(response));

            const { responseCode, isXMLRPCFault } = response

            if (isXMLRPCFault || isNaN(responseCode)) {
                endpointConnectionIndexer.cycleConnectionIndex(endpointConnectionIndexKey);
                throw new XMLRPCFaultException(airurl, response);
            }
    
            if (UCIP_CODES_FOR_CONNECTION_CYCLE.includes(responseCode) ) {
                endpointConnectionIndexer.cycleConnectionIndex(endpointConnectionIndexKey);
                let responseString = JSON.stringify(response);
                throw new UCIPProtocolException(airurl, responseCode, responseString)
            }

            if (responseCode >= 100) {
                console.warn("UCIP Response not successful: ", JSON.stringify(response, null, 2));
            }

            const ucipResponseCodeDescription = UCIPProtocolException.getCodeDescription(responseCode);
            const endTime = performance.now()

            console.info(
                `LIB: ${LIBRARY_NAME} | ` +
                `END EXT Endpoint [${airurl}] ${options.ucipCallName} ` +
                `subject [${ucipCallSubscriberNumber}] ` +
                `response [${ucipResponseCodeDescription}] ` +
                `duration [${(endTime - startTime).toFixed(3)}ms]`
            );
            return resolve(response);
        } catch (error) {
            const endTime = performance.now()

            let errorResponseString = 'Exception-Thrown';

            if (error instanceof XMLRPCFaultException) {
                errorResponseString = `XMLRPCFault:${error.getFaultDescription()}`;
            } else if (error instanceof UCIPProtocolException) {
                errorResponseString = UCIPProtocolException.getCodeDescription(error.responseCode);
            } else if (error instanceof ComponentRequestError) {
                errorResponseString = `HTTP_NETWORK_ERROR:${error.getErrorDescription()}`
            }

            console.error(
                `LIB: ${LIBRARY_NAME} | EXCEPTION | ` +
                `END EXT Endpoint [${airurl}] ${options.ucipCallName} ` +
                `subject [${ucipCallSubscriberNumber}] ` +
                `response [${errorResponseString}] ` +
                `duration [${(endTime - startTime).toFixed(3)}ms]`
            );
            if (error instanceof UCIPLibraryException || error instanceof XMLRPCFaultException) {
                reject(error);
            } else {
                reject(new UCIPLibraryException(error, "Problem making callAir(...) request"));
            }
        }
    });
}

const validateParams = (params, ucipCallName) => {
    const errorMessages = [];

    switch (ucipCallName) {
        case "Refill":
            const isValidVoucherless = isValidVoucherlessRefill(params);
            const isValidVoucher = isValidVoucherRefill(params);
            if (!isValidVoucher && !isValidVoucherless) {
                errorMessages =
                    "Cannot Refill. Missing, Invalid, or conflicting params given for Voucher OR Voucherless refill.";
            }
            break;
        case "GetRefillOptions":
            if (check(params).isMissingMandatoryParams(["voucherActivationCode"])) {
                errorMessages = "Cannot GetRefillOptions. Missing mandatory param 'voucherActivatioCode'.";
            }
            break;
        case "UpdateOffer":
            if (check(params).isMissingMandatoryParams(["offerID"])) {
                errorMessages = "Cannot UpdateOffer. Missing mandatory param 'offerID'.";
            }
            break;
    }

    if (errorMessages.length !== 0) throw new UCIPLibraryException(null, errorMessages.join(", "));
};

const isValidVoucherlessRefill = (params) => {
    const mandatoryVoucherless = ["transactionAmount", "transactionCurrency", "refillProfileID"];
    const invalidForVoucherless = [
        "locationNumber",
        "locationNumberNAI",
        "validateSubscriberLocation",
        "voucherActivationCode",
    ];

    const hasMandatory = !check(params).isMissingMandatoryParams(mandatoryVoucherless);
    const hasInvalidParams = check(params).containsAny(invalidForVoucherless);

    return hasMandatory && !hasInvalidParams;
};

const isValidVoucherRefill = (params) => {
    const mandatoryVoucher = ["voucherActivationCode"];
    const invalidForVoucher = ["transactionAmount", "transactionCurrency", "refillProfileID"];

    const hasMandatory = !check(params).isMissingMandatoryParams(mandatoryVoucher);
    const hasInvalidParams = check(params).containsAny(invalidForVoucher);

    return hasMandatory && !hasInvalidParams;
};

/**
 * Performs an UpdateBalanceAndDate call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function updateBalanceAndDate(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "UpdateBalanceAndDate" });
}

/**
 * Performs an UpdateBalanceAndDate call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function getBalanceAndDate(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "GetBalanceAndDate" });
}

/**
 * Performs an UpdateBalanceAndDate call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function getAccountDetails(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "GetAccountDetails" });
}

/**
 * Performs an UpdateSubscriberSegmentation call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function updateSubscriberSegmentation(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "UpdateSubscriberSegmentation" });
}

/**
 * Performs an GetRefillOptions call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function getRefillOptions(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "GetRefillOptions" });
}

/**
 * Performs an Refill call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
function refill(params, ctx) {
    return callAir(params, ctx, { ucipCallName: "Refill" });
}

/**
 * Performs an GetUsageThresholdsAndCounters call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
const getUsageThresholdsAndCounters = (params, ctx) => {
    return callAir(params, ctx, { ucipCallName: "GetUsageThresholdsAndCounters" });
};

/**
 * Performs an UpdateUsageThresholdsAndCounters call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
const updateUsageThresholdsAndCounters = (params, ctx) => {
    return callAir(params, ctx, { ucipCallName: "UpdateUsageThresholdsAndCounters" });
};

/**
 * Performs an GetOffers call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
const getOffers = (params, ctx) => {
    return callAir(params, ctx, { ucipCallName: "GetOffers" });
};

/**
 * Performs an UpdateOffer call to Air
 *
 * @param {object} params - Call Parameters
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Air
 */
const updateOffer = (params, ctx) => {
    return callAir(params, ctx, { ucipCallName: "UpdateOffer" });
}

/**
 * Performs a subscriber number formatting based on endpoint configuration
 *
 * @param {object} endpoint - Component Endpoint
 * @param {string} subscriberNumber - Subscriber number
 * @throws {object} Invalid number exception
 */
const formatSubscriberNumber = (endpoint, subscriberNumber) => {
    //const endpoint = ctx.connectors.ucip[ctx.menuState.ucipEndpointName || DEFAULT_ENDPOINT_NAME];

    const operatorFormats = endpoint.operatorFormats;
    const nai = "subscriberNumberNAI" in endpoint ? endpoint.subscriberNumberNAI : -1;

    if (operatorFormats && operatorFormats.length > 0) {
        for (const operatorFormat of operatorFormats) {
            const { national, country, international } = endpoint.prefixes;

            let prefixes = [
                // national + number
                {
                    prefixFormat: national,
                    combinedLength: national.length + operatorFormat.prefix.length + operatorFormat.length,
                },

                // country + number
                {
                    prefixFormat: country,
                    combinedLength: country.length + operatorFormat.prefix.length + operatorFormat.length,
                },

                // international + country + number
                {
                    prefixFormat: international + country,
                    combinedLength: international.length + country.length + operatorFormat.prefix.length + operatorFormat.length,
                },
            ];

            for (const prefix of prefixes) {
                const { prefixFormat, combinedLength } = prefix;
                if (
                    subscriberNumber.length == combinedLength && //
                    subscriberNumber.startsWith(prefixFormat + operatorFormat.prefix)
                ) {
                    const nationalWithoutPrefix_subscriberNumber = subscriberNumber.slice(prefixFormat.length);

                    switch (nai) {
                        case 0:
                            return national + nationalWithoutPrefix_subscriberNumber;

                        case 1:
                            return country + nationalWithoutPrefix_subscriberNumber;
                    }

                    return nationalWithoutPrefix_subscriberNumber;
                }
            }

            // No matches for 'prefixes'
            //  Check if NON-prefixed format
            const length = operatorFormat.prefix.length + operatorFormat.length;
            if (subscriberNumber.length == length && subscriberNumber.startsWith(operatorFormat.prefix)) {
                const nationalWithoutPrefix_subscriberNumber = subscriberNumber;

                switch (nai) {
                    case 0:
                        return national + nationalWithoutPrefix_subscriberNumber;

                    case 1:
                        return country + nationalWithoutPrefix_subscriberNumber;
                }

                return nationalWithoutPrefix_subscriberNumber;
            }
        }
    } else {
        return subscriberNumber;
    }

    console.error(
        "ucip.formatSubscriberNumber(...) :: The susbcriber number does not match the formatting provided by the connector endpoint"
    );
    throw new Error(`Invalid subscriber number: ${subscriberNumber}`);
};

module.exports = {
    updateBalanceAndDate,
    getBalanceAndDate,
    getAccountDetails,
    updateSubscriberSegmentation,
    getRefillOptions,
    refill,
    getUsageThresholdsAndCounters,
    updateUsageThresholdsAndCounters,
    getOffers,
    updateOffer,
    setEndpoint,
    getEndpoint,
    mergeDeep,
};
