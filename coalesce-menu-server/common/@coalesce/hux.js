const { performance } = require('perf_hooks');
const xmlrpc = require("@coalesce/xmlrpc");
const { ComponentRequestError, sendRequest, getOptions } = require("@coalesce/request");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");

// FIXME - this should likely use a better UUID than this
const randomTransactionId = () => {
  return String(Math.floor(100000000 + Math.random() * 900000000));
};

const DEFAULT_ENDPOINT_NAME = "smartshop";

const huxEndpointManager = new EndpointManager("hux");

const LIBRARY_NAME = "@coalesce/hux"

const setEndpoint = (name, ctx) => {

  if (!name) {
    console.warn("setEndpoint(...) :: No endpoint name secified, setting to default: ", DEFAULT_ENDPOINT_NAME);
  }
  huxEndpointManager.setEndpointInContext(name || DEFAULT_ENDPOINT_NAME, ctx);
};

class HuxError extends CoalesceError {
  constructor(...params) {
    super(HuxError, ...params);
  }
}

/**
 * This method will send a USSD request to the associated endpoint that is provided by the setEndpoint(...) method
 *
 * @param {String} ussdString The USSD String that will be sent as a HuX request to the external endpoint
 * @param {Object} ctx The Context provided by the Menu Server
 * @param {Object} params The options for this USSD request
 */
const sendUSSD = async (ussdString, ctx, additionalParams = {}) => {
  const MSISDN = ctx?.request?.MSISDN;
  const startTime = performance.now();
  return new Promise(async (resolve, reject) => {
    let requestUrl = 'n/a';
    try {
      const huxConfig = getHuxConfig(ussdString, ctx, additionalParams);
      requestUrl = huxConfig.requestUrl
      const { endpoint } = huxConfig;

      console.info(`LIB: ${LIBRARY_NAME} | START EXT USSD [${requestUrl}] USSD Request [${ussdString}] subject [${MSISDN}]`);

      let { params, huxCodeParts } = huxConfig;

      params.USSDServiceCode = huxCodeParts[0];

      if (ctx.new && huxCodeParts.length > 1) {
        huxCodeParts.shift();
        params.USSDRequestString = "*";
        params.USSDRequestString += huxCodeParts.join("*");
        params.USSDRequestString += "#";
      }

      const body = xmlrpc.encodeRequest("handleUSSDRequest", params);

      let { headers, timeout } = getOptions(endpoint);
      console.trace("HuX Library :: Endpoint being used for request:", endpoint);

      let xml = {};
      console.debug("outgoing XML: ", body);
      const callResponse = await sendRequest({ url: requestUrl, body, headers, timeout });
      xml = callResponse.data;
      console.trace("HuX response content", xml);

      const parsedResponse = xmlrpc.parseResponse(xml);
      console.trace("Hux Library parsed response", parsedResponse);

      const ongoingHuxRequest = parsedResponse.action === "request";

      ctx.done = !ongoingHuxRequest;
      ctx.respond = true; // Mark to send parsedResponse to user
      ctx.output = parsedResponse.USSDResponseString || "";
      ctx.raw = xml;

      const containsOutput = ctx.output.length !== 0;

      // XML parser trims the new line at the end of message
      ctx.output += containsOutput ? "\n" : "";

      ctx.variables.response = parsedResponse.USSDResponseString;

      const endTime = performance.now()
      console.info(
        `LIB: ${LIBRARY_NAME} | ` +
        `END EXT USSD [${requestUrl}] USSD Request [${ussdString}] ` +
        `subject [${MSISDN}] ` +
        `USSDResponseString [${parsedResponse.USSDResponseString}] ` +
        `duration [${(endTime - startTime).toFixed(3)}ms]`
      );

      return resolve({
        huxRequestCompleted: !!ctx.done,
        ...parsedResponse,
      });
    } catch (e) {
      const endTime = performance.now()

      let errorResponseString = 'Exception-Thrown';
      if (e instanceof ComponentRequestError) {
        errorResponseString = `HTTP_NETWORK_ERROR:${e.getErrorDescription()}`;
      }

      console.error(
        `LIB: ${LIBRARY_NAME} | EXCEPTION | ` +
        `END EXT USSD [${requestUrl}] USSD Request [${ussdString}] ` +
        `subject [${MSISDN}] ` +
        `response [${errorResponseString}]` +
        `duration [${(endTime - startTime).toFixed(3)}ms]`
      );

      let huxError = new HuxError(e, "hux.sendUSSD(...) Failed");
      console.error(huxError);
      return reject(huxError);
    }
  });
};

const getValidatedUSSDStringParts = (ussdString) => {
  const INVALID_USSD_STRING = "Invalid USSD String for HuX Request";
  const EMPTY_USSD_STRING = "Empty USSD String for HuX Request";

  if (!ussdString) throw new HuxError(null, EMPTY_USSD_STRING);

  // Remove suffix # and ensure nothing comes after it
  let ussdStringParts = ussdString.split("#");
  if (ussdStringParts.length !== 2 || ussdStringParts[ussdStringParts.length - 1] !== "") {
    throw new HuxError(null, INVALID_USSD_STRING);
  }
  ussdStringParts.pop();

  // Remove prefix * and ensure nothing comes before it
  ussdStringParts = ussdStringParts[0].split("*");
  if (ussdStringParts[0] !== "") throw new HuxError(null, INVALID_USSD_STRING);
  ussdStringParts.shift();

  ussdStringParts.forEach((part) => {
    if (!part.match(/^[0-9]+$/)) throw new HuxError(null, INVALID_USSD_STRING);
  });

  return ussdStringParts;
};

const getRequestParamsFromContext = (ctx) => {
  const { request } = ctx || {};
  /**
   * FIXME ... this 'gotolabel' should not be in the ctx.request ... however it is DEEPLY embeded.
   *           To remove it would take significant work which we didn't have time for during this update
   */
  delete request.gotolabel;
  return request;
}

const getHuxConfig = (ussdString, ctx, additionalParams = {}) => {
  const huxCodeParts = getValidatedUSSDStringParts(ussdString);

  const endpoint = huxEndpointManager.getEndpointFromContext(ctx);

  let params = getRequestParamsFromContext(ctx);

  const { hostname, port, url } = endpoint;

  const encodedPath = encodeURIPath(url.replace(/^\/+/, ""));

  const requestUrl = `http://${hostname}:${port}/${encodedPath}`;

  params = {
    ...params,
    TransactionId: params.TransactionId || randomTransactionId(),
    TransactionTime: new Date(),
    MSISDN: ctx.request.MSISDN,
    USSDRequestString: ctx.new ? "#" : ctx.request.USSDRequestString,
    response: ctx.new ? false : ctx.request.response,
    ...additionalParams // allows overriding existing params if they are set to 'undefined'
  };

  // ensures 'undefined' params are removed - so they don't create empty params in the XML request
  Object.entries(params).forEach((entry) => {
    const [key, value] = entry;
    if (!value) delete params[key];
  })

  return { requestUrl, params, huxCodeParts, endpoint };
};

module.exports = {
  sendUSSD,
  setEndpoint,
  getValidatedUSSDStringParts,
};
