class CoalesceError extends Error {
  constructor(errorType, innerError, ...params) {
    super(...params);
    this.name = errorType.name;
    this.errorType = errorType;
    this.innerError = innerError;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.errorType);
    }
  }

  toString() {
    let errorName = this.name ? this.name : "Error";
    let completeDescription = errorName + ":" + this.message + "\n";

    if (this.innerError) {
      completeDescription += " Caused by " + this.innerError.toString();
    }

    return completeDescription;
  }
}

module.exports = CoalesceError;
