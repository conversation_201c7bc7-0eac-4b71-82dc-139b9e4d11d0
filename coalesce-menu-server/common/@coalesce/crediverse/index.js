const { performance } = require('perf_hooks');
const { sendGetRequest, getOptions, ComponentRequestError } = require("@coalesce/request");
const { encodeURIPath, EndpointManager } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");
const { getActiveConnection, CrediverseConnectionPoolException } = require("./poolController");

const crediverseEndpointManager = new EndpointManager("crediverse");

const LIBRARY_NAME = "@coalesce/crediverse"

class CrediverseLibraryException extends CoalesceError {
  constructor(...params) {
    super(CrediverseLibraryException, ...params);
  }
}

const DEFAULT_ENDPOINT_NAME = "ecds";

const HTTP_401_UNAUTHORIZED = 401;
const HTTP_404_NOT_FOUND = 404;

const setEndpoint = (name, ctx) => {
  if (!name) {
    console.warn("setEndpoint(...) :: No endpoint name secified, setting to default: ", DEFAULT_ENDPOINT_NAME);
  }
  crediverseEndpointManager.setEndpointInContext(name || DEFAULT_ENDPOINT_NAME, ctx);
};

function callApi(path, ctx) {
  const startTime = performance.now();
  return new Promise(async (resolve, reject) => {
    let url = "n/a"
    try {
      const config = crediverseEndpointManager.getEndpointFromContext(ctx);

      const { headers, timeout } = getOptions(config);

      const encodedUrl = encodeURIPath(config.url.replace(/^\/+/, ""));

      url = `http://${config.hostname}:${config.port}/${encodedUrl}/${path.replace(/^\//, "")}`;
      console.info(`LIB: ${LIBRARY_NAME} | START EXT Endpoint [${url}]`);

      let response;

      const connectionManager = await getActiveConnection(ctx);

      const tokenData = await connectionManager.acquire({ immediateRelease: true });

      headers["Authorization"] = `Bearer ${tokenData.access_token}`;

      console.info(`Making request to ${path}`);
      console.debug({ url, headers, timeout });

      response = await sendGetRequest({ url, headers, timeout });

      console.info("Crediverse GetAgentDetails request completed.");
      console.debug("Crediverse GetAgentDetails response:", response);

      const responseCode = response.code || response.status;
      const responseMessage = response.statusText;
      const endTime = performance.now()
      console.info(
        `LIB: ${LIBRARY_NAME} | ` +
        `END EXT Endpoint [${url}] ` +
        `response [HTTP ${responseCode}:${responseMessage}] ` +
        `duration [${(endTime - startTime).toFixed(3)}ms]`
      );

      resolve(response);
    } catch (error) {
      const endTime = performance.now()

      let errorResponseString = 'Exception-Thrown'
      if (error.innerError instanceof ComponentRequestError) {
        errorResponseString = `HTTP_NETWORK_ERROR:${error.innerError.getErrorDescription()}`;
      } else if (error instanceof ComponentRequestError) {
        errorResponseString = `HTTP_NETWORK_ERROR:${error.getErrorDescription()}`;
      }

      console.error(
        `LIB: ${LIBRARY_NAME} | EXCEPTION | ` +
        `END EXT Endpoint [${url}] ` +
        `response [${errorResponseString}] ` +
        `duration [${(endTime - startTime).toFixed(3)}ms]`
      );

      if (error instanceof CrediverseLibraryException || error instanceof CrediverseConnectionPoolException) {
        console.error(error);
        reject(error);
      } else if (error instanceof ComponentRequestError) {
        if (error.status === HTTP_404_NOT_FOUND) {
          // NOTE: This intentionally 'resolves' successfully so that a CAD can handle a 404
          resolve({ code: error.status });
        } else {
          const unknownError = new CrediverseLibraryException(error, "Failed to Get Agent details");
          console.error(unknownError);
          reject(unknownError);
        }
      } else {
        const unknownError = new CrediverseLibraryException(error, "Failed to Get Agent details");
        console.error(unknownError);
        reject(unknownError);
      }
    }
  });
}

/**
 * Performs an GetAccount call to Crediverse API
 *
 * @param {object} ctx - Component Context
 * @returns {object} New object with response from Crediverse
 */
function getAgentDetails(ctx) {
  validateContext(ctx);

  const path = `service/agent/${encodeURIComponent(ctx.request.MSISDN)}/details`;
  return callApi(path, ctx);
}

const validateContext = (ctx) => {
  if (!ctx || !ctx.request) {
    throw new CrediverseLibraryException(null, "Missing request data in the session ctx");
  }
};

module.exports = {
  setEndpoint,
  getAgentDetails,
};
