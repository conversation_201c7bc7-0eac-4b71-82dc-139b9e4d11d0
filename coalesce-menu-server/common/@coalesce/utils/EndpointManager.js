const componentManager = require("../../../server/componentManager");
const CoalesceError = require("@coalesce/coalesceError");

class EndpointException extends CoalesceError {
  constructor(...params) {
    super(EndpointException, ...params);
  }
}

class EndpointManager {
  constructor(connectorType) {
    const invalidConnectorType = !connectorType || typeof connectorType !== "string";

    if (invalidConnectorType) {
      const exception = new EndpointException(
        null,
        "Cannot create EndpointManager without valid connectorType, e.g crediverse, ucip, etc"
      );
      throw exception;
    }

    this._type = connectorType;
  }

  getComponentManagerEndpoint(endpointName) {
    const endpoint = componentManager.getEndpointsFor(this._type)[endpointName];

    if (!endpoint) {
      const exception = new EndpointException(null, "No Endpoint with that name in componentManager");

      throw exception
    }

    return endpoint;
  }

  getComponentManagerConnectorData(connectorId) {
    const connectorData = componentManager.getConnectorData(connectorId);
    if (!connectorData) {
      const exception = new EndpointException(
        null,
        `No Connector Data found for for connectorId provided '${String(connectorId)}'`
      );
      throw exception
    }
    return connectorData;
  }

  setEndpointInContext(name, ctx) {
    this._prepareMenuState(ctx);
    if (!name || typeof name !== "string") {
      const exception = new EndpointException(null, "Invalid endpoint name given, must be non empty string");
      throw exception;
    }
    ctx.menuState.connectors[this._type].endpointName = name;

  }

  getEndpointNameFromContext(ctx) {
    this._prepareMenuState(ctx);
    this._validateEndpointInContext(ctx);
    return ctx.menuState.connectors[this._type].endpointName;
  }

  getEndpointFromContext(ctx) {
    this._prepareMenuState(ctx);
    this._validateEndpointInContext(ctx);
    const { endpointName } = ctx.menuState.connectors[this._type];
    return ctx.connectors[this._type][endpointName];
  }

  _prepareMenuState(ctx) {
    if (typeof ctx !== "object" || !ctx || !ctx.connectors) {
      const exception = new EndpointException(null, "Invalid context provided");
      throw exception;
    }
    // set defaults when empty
    ctx.menuState = ctx.menuState || {};
    ctx.menuState.connectors = ctx.menuState.connectors || {};
    ctx.menuState.connectors[this._type] = ctx.menuState.connectors[this._type] || {};
  }

  _validateEndpointInContext(ctx) {
    let throws = false;

    if (!ctx.menuState.connectors[this._type] || !ctx.menuState.connectors[this._type].endpointName) throws = true;

    const { endpointName } = ctx.menuState.connectors[this._type];

    if (!ctx.connectors[this._type] || !ctx.connectors[this._type][endpointName]) throws = true;

    if (throws) {
      const exception = new EndpointException(
        null,
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
      throw exception;
    }
  }
}

module.exports = { EndpointManager };
