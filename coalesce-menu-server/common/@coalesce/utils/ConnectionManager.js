const genericPool = require("generic-pool");
const CoalesceError = require("@coalesce/coalesceError");

class ConnectionManagerException extends CoalesceError {
  constructor(...params) {
    super(ConnectionManagerException, ...params);
  }
}

const MIN_ACQUIRE_TIMEOUT = 500;
const MAX_ACQUIRE_TIMEOUT = 40000;

const MIN_POOL_SIZE = 1;
const MAX_POOL_SIZE = 100;

const DEFAULT_POOL_SIZE = 1;

let DEFAULT_POOL_OPTIONS = {
  max: DEFAULT_POOL_SIZE,
  min: DEFAULT_POOL_SIZE,
  acquireTimeoutMillis: MAX_ACQUIRE_TIMEOUT,
  testOnBorrow: true,
  autostart: false,
};

class ConnectionManager {
  constructor(poolOptions) {
    this.validateAndSetPoolOptions(poolOptions);
    this._pool = null;
    this._initialized = false;

    this._draining = false;
    this._drainPromise = Promise.resolve();
  }

  get initialized() {
    return this._initialized;
  }

  validateAndSetPoolOptions(poolOptions) {
    if (typeof poolOptions !== "undefined" && typeof poolOptions !== "object") {
      throw new ConnectionManagerException("Invalid poolOptions provided");
    }

    let { min, max, acquireTimeoutMillis, testOnBorrow } =
      // prefer PARAM first, then EXISTING, then DEFAULTS
      poolOptions || this._poolOptions || DEFAULT_POOL_OPTIONS;

    testOnBorrow = typeof testOnBorrow === "boolean" ? testOnBorrow : DEFAULT_POOL_OPTIONS.testOnBorrow;

    if (isNaN(min) || min < MIN_POOL_SIZE || min > MAX_POOL_SIZE) min = DEFAULT_POOL_SIZE;
    if (isNaN(max) || max < MIN_POOL_SIZE || max > MAX_POOL_SIZE) max = DEFAULT_POOL_SIZE;
    if (min > max) min = max;

    if (
      isNaN(acquireTimeoutMillis) ||
      acquireTimeoutMillis < MIN_ACQUIRE_TIMEOUT ||
      acquireTimeoutMillis > MAX_ACQUIRE_TIMEOUT
    ) {
      acquireTimeoutMillis = MAX_ACQUIRE_TIMEOUT;
    }

    this._poolOptions = {
      ...DEFAULT_POOL_OPTIONS,

      // IMPORTANT: This timeout is often the SAME timeout used inside the factory.create request
      //            Due to that, the POOL timeout must be slightly higher to prevent RACE conditions
      //            ... where the pool would timeout before the connection request
      acquireTimeoutMillis: acquireTimeoutMillis + 200,
      min,
      max,
      testOnBorrow,
    };
  }

  initializeFactory(/* Optional */ factory) {
    this._initialized = false;
    this._setAndValidateFactory(factory);

    this._pool = genericPool.createPool(this._factory, this._poolOptions);
    this._pool.on("factoryCreateError", (error) => {
      // Workaround for known issue releasing failed create() errors
      // -- see https://github.com/coopernurse/node-pool/issues/175#issuecomment-501826583
      const dequeued = this._pool._waitingClientsQueue.dequeue();
      if (dequeued) dequeued.reject(error);
    });
    this._initialized = true;
  }

  _setAndValidateFactory(/* Optional */ newFactory) {
    this._factory = newFactory || this._factory;

    const { _factory } = this;
    const invalidFactory =
      typeof _factory !== "object" ||
      typeof _factory.create !== "function" ||
      (this._poolOptions.testOnBorrow === true && typeof _factory.validate !== "function") ||
      typeof _factory.destroy !== "function";

    if (invalidFactory) {
      throw new ConnectionManagerException("Cannot validate connection pool factory, it is unset or invalid!");
    }
  }

  async _drainActivePool() {
    if (this._pool !== null) {
      this._drainPromise = new Promise(async (resolve) => {
        try {
          this._draining = true;
          await this._pool.drain();
          await this._pool.clear();
          this._draining = false;
        } catch (error) {
          console.warn(
            new ConnectionManagerException(
              error,
              "Encountered a problem draining the Connection Pool -- MIGHT be able to ignore this"
            )
          );
        }
        resolve();
      });
    }
  }

  async acquire(o) {
    return await this._drainPromise.then(async () => {
      this._validatePool();

      const options = o || {};

      const resource = await this._pool.acquire();

      if (options.immediateRelease) await this._pool.release(resource);

      return resource;
    });
  }

  _validatePool() {
    const errors = [];

    if (!this._initialized) errors.push("Connection pool not initialized");
    if (this._draining) errors.push("Connection pool busy draining");

    if (errors.length !== 0) {
      throw new ConnectionManagerException(null, errors.join(", "));
    }
  }

  async reinitializeFactory(/* Optional */ factory, /* Optional */ poolOptions) {
    this.validateAndSetPoolOptions(poolOptions);
    await this._drainActivePool();
    this.initializeFactory(factory);
  }
}

module.exports = { ConnectionManager };
