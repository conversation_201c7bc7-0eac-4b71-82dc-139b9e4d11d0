const CoalesceError = require("@coalesce/coalesceError");
const { LANG_MAP } = require("@coalesce/env");
const { ConnectionManager } = require("./ConnectionManager");
const { EndpointManager } = require("./EndpointManager");

class LanguageMapException extends CoalesceError {
  constructor(...params) {
    super(LanguageMapException, ...params);
  }
}

const getLanguageMapFromEnvironment = () => {
  const langmap = LANG_MAP;

  let langs = langmap.split("\n").filter(i => i !== "")
  if (langs.length <= 1) {
    langs = langmap.trim().replace(/;$/, "").split(";");
  }

  if (langs.length === 0) {
    const langMapException = new LanguageMapException(null, "Cannot retrieve language map, error with format");
    console.error(langMapException);
    throw langMapException;
  }

  const res = [];
  langs.forEach((item) => {
    const [i, lang] = item.split(": ");
    res[Number(i)] = lang.trim().toUpperCase();
  });
  res[0] = res[1];
  return res;
};

function deepEqual(object1, object2) {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = object1[key];
    const val2 = object2[key];
    const areObjects = isObject(val1) && isObject(val2);
    if ((areObjects && !deepEqual(val1, val2)) || (!areObjects && val1 !== val2)) {
      return false;
    }
  }

  return true;
}

function isObject(object) {
  return object != null && typeof object === "object";
}

const encodeURIPath = (url) => {
  try {
    return String(url)
      .split("/")
      .map((part) => encodeURI(part)) // replace all parts as encoded
      .join("/");
  } catch (e) {
    console.error("Failed to encodeURI for the following endpoint url (probably bad UTF-8 characters present): ", url);
    throw e;
  }
};

const queryablePromise = (promise) => {
  // Don't modify any promise that has been already modified.
  if (promise.isQueryable) return promise;

  // Set initial state
  var isPending = true;
  var isRejected = false;
  var isFulfilled = false;

  // Observe the promise, saving the fulfillment in a closure scope.
  var result = promise.then(
    (v) => {
      isFulfilled = true;
      isPending = false;
      return v;
    },
    (e) => {
      isRejected = true;
      isPending = false;
      throw e;
    }
  );

  result.isQueryable = true;

  result.isPending = () => isPending;
  result.isCompleted = () => !isPending;

  result.isFulfilled = () => isFulfilled;
  result.isRejected = () => isRejected;

  return result;
};

const epochNow = () => Math.floor(new Date().getTime() / 1000);

module.exports = {
  getLanguageMap: getLanguageMapFromEnvironment,
  deepEqual,
  encodeURIPath,
  queryablePromise,
  EndpointManager,
  ConnectionManager,
  epochNow,
};
