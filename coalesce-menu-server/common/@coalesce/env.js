require("dotenv").config();
// TODO: we should leave CS_MENU_ENVIRONMENT to say previewer
// Force to 'preprod' when environment is _previewer_, because previewer runs in 'preprod' by default
let csMenuEnv = process.env.CS_MENU_ENVIRONMENT || "preprod";
const CS_MENU_ENVIRONMENT = csMenuEnv === "previewer" ? "preprod" : csMenuEnv;

const CS_ENVIRONMENT_NAME = process.env.CS_ENVIRONMENT_NAME || "development";
const CS_CUSTOMER_NAME = process.env.CS_CUSTOMER_NAME || "cs";
const MODULE_NAME = process.env.MODULE_NAME || "previewer";

const isPreviewer = process.env.CS_MENU_ENVIRONMENT == "previewer";
const isProduction = process.env.NODE_ENV == "production";

const LANG_MAP = process.env.LANG_MAP || "1: ENG\n2: FRA\n"; // trailing NL is necessary

const LOG_MEMORY_DATA = process.env.LOG_MEMORY_DATA || false;

module.exports = {
  isPreviewer,
  isProduction,
  LANG_MAP,
  LOG_MEMORY_DATA,
  CS_MENU_ENVIRONMENT,
  CS_ENVIRONMENT_NAME,
  CS_CUSTOMER_NAME,
  MODULE_NAME,
  ENDPOINTS_ETCD_KEY: `coalesce/${CS_CUSTOMER_NAME}/${CS_ENVIRONMENT_NAME}/menu/${CS_MENU_ENVIRONMENT}/endpoints`,
  GLOBAL_MESSAGES_ETCD_KEY: `/coalesce/${CS_CUSTOMER_NAME}/globalsettings/messages`,
  LOG_LEVEL_ETCD_KEY: `coalesce/cs/${MODULE_NAME}/${CS_MENU_ENVIRONMENT}/log-level`,
  CREDIVERSE_MAX_POOL_SIZE_ETCD_KEY: `coalesce/cs/${MODULE_NAME}/${CS_MENU_ENVIRONMENT}/crediverse/max-pool-size`,
};