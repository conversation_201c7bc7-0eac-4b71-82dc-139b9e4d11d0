const request = require('request');
const http = require('http');

	// all in milliseconds
const DEFAULT_REQUEST_TIMEOUT = 2000;
const MIN_REQUEST_TIMEOUT = 500;
const MAX_REQUEST_TIMEOUT = 30000;

function sendRequest(args) {

	return new Promise((resolve, reject) => {

		request.post(args, (error, response, data) => {
			if (error) 
			{
							// ---- Different timeout types can be different root issues... we must correctly log the important types.
					if ( error.code === 'ESOCKETTIMEDOUT' )
							console.log("== 'Read Timeout' ==\n", error);
					else if ( error.code === 'ETIMEDOUT' && error.connect === true )
							console.log("== 'Connect Timeout' ==\n", error);
					else
							console.log("== Unknown request error ==\n", error);

					reject(error);
			}
			else {
				//console.log('DATA: ' + data);
				resolve(data);
			}
		})
	})
	.catch(err => {
		return Promise.reject(err)
	});
}

function getOptions(connector)
{
    console.log("== in getOptions for connector : ", connector);

    let headerArray    = Array.isArray(connector.headers) ? connector.headers : []
    headerArray        = JSON.parse( JSON.stringify(headerArray) ); // copy object (prevent passing by reference)

    connector.headers   = {};
	connector.auth		= typeof connector.auth != "undefined" ? connector.auth : {};
    connector.auth.type	= typeof connector.auth.type != "undefined" ? connector.auth.type : 'none';

    

	connector.headers['Content-Type'] = 'text/xml';
	//connector.headers['User-Agent'] = 'CsMenu Server/1.0';
	connector.headers['User-Agent'] = 'UGw Server/5.0/1.0';
	


		// if it is 'basic' - then it's already checked that username/password aren't empty, so we aren't double-checking
    if ( connector.auth.type == "basic" )
        connector.headers.Authorization = "Basic " + Buffer.from(connector.auth.username + ":" + connector.auth.password).toString("base64");

        // import header array into header object by key/value pairs -- duplicate keys will overwrite keys before
    headerArray.forEach(element => {
        if ( element.name && element.value )
            connector.headers[element.name] = element.value;
    });
    

        // explicit logging for timeout configuration issues
    if ( typeof connector.timeout == "undefined" )
        console.log("timeout undefined, defaulting to:", DEFAULT_REQUEST_TIMEOUT);

	connector.timeout		= typeof connector.timeout != "undefined" ? parseInt(connector.timeout) : DEFAULT_REQUEST_TIMEOUT;

    if ( connector.timeout < MIN_REQUEST_TIMEOUT || connector.timeout > MAX_REQUEST_TIMEOUT )
    {
        console.log("timeout out of bounds, defaulting to:", DEFAULT_REQUEST_TIMEOUT);
        connector.timeout = DEFAULT_REQUEST_TIMEOUT;
    }

        // copy to return as new object
    return JSON.parse(JSON.stringify(connector));
}

module.exports = {
	sendRequest,
	getOptions,
}