#!/usr/bin/env node

//const net = require('net');
const http = require('http')
	, URL = require('url')
	, hux = require('@coalesce/xmlrpc')
	;


const AIRSIM_SERVER_PORT = 10011;
const AIRSIM_HTTP_PORT = 10080;

const HXC_SERVER_PORT = 14100;
const RPC2_SERVER_PORT = 14000;
const ECDS_SERVER_PORT = 14400;

const headers = [
	'MSISDN',
	{ name: 'Lang', id: "languageIDCurrent" },
	'Service Class',
	'Service Class Orig.',
	'Acc. Activated Flag',
	'Acc. Activation Date',
	'Acc. Flags',
	'Master Subs. Flag',
	'Master Acc. Number',
	'Superv. Expirity Date',
	'Credit Clearance Bal. Date',
	'Serv. Removal Date',
	'Serv. Fee Expirity Date',
	'Currency 1',
	'Acc. Value 1',
	'Currency 2',
	'Acc. Value 2',
	'Acc. Flags Before'
];

class Server {
	constructor() {
		this.subscribers = new Map();
	}

	start() {
		console.info('Starting AIR server on: %d', AIRSIM_SERVER_PORT);

		const server = http.createServer((req, res) => this.handleRequest(req, res));
		server.listen(AIRSIM_SERVER_PORT);
	}

	handleRequest(req, res) {
		//console.log('AIRSIM request received:');
		const { headers, method, url } = req;
		//console.log('Method: %s', method);
		//console.log('URL: %s', url);
	
		let body = [];
		req.on('error', (err) => {
			console.error(err);
		}).on('data', (chunk) => {
			body.push(chunk);
		}).on('end', () => {
			body = Buffer.concat(body).toString();
	
			//console.log('Request received\n' + body);
			const route = URL.parse(url).path.split('/');
			route.splice(0, 1);
	
			if (method == 'POST') {
				if (route[0] == 'Air')
					this.handleHuxRequest(body, res);
				else if (route[0] == 'api')
					this.handleApiRequest(body, res);
			}
			else {
				res.setHeader('Content-Type', 'text/html');
	
				const html = this.renderSubscribers();
				res.end(html);
			}
		});
	}

	handleHuxRequest(body, res) {
		res.setHeader('Content-Type', 'text/xml');

		const request = hux.parse(body);
		const method = request.method;
		const req = request.members;
		const msisdn = req.subscriberNumber;
		//console.debug('USSD request received:\n', request);

		const s = this.subscribers.get(msisdn);
		if (!s) {
			console.log('Subsciber NOT found');
			res.status = 400;
			res.end();
			return;
		}
				
		//console.debug(body);
		let r;
		switch (method) {
			case 'GetAccountDetails':
			r = getAccountDetails(s, req);
			break;
	
			case 'UpdateAccountDetails':
			r = updateAccountDetails(s, req);
			break;
	
			case 'UpdateBalanceAndDate':
			r = updateBalanceAndDate(s, req);
			break;
	
			case 'GetBalanceAndDate':
			r = getBalanceAndDate(s, req);
			break;
	
			case 'UpdateOffer':
			r = updateOffer(s, req);
			break;

			case 'deleteOffer':
			r = deleteOffer(s, req);
			break;
	
			case 'GetUsageThresholdsAndCounters':
			r = getUsageThresholdsAndCounters(s, req);
			break;

			case 'UpdateUsageThresholdsAndCounters':
			r = updateUsageThresholdsAndCounters(s, req);
			break;
	
			case 'Refill':
			r = refill(s, req);
			break;

			case 'GetFaFList':
			r = getFaFList(s, req);
			break;
	
			case 'AddPeriodicAccountManagementData':
			r = addPeriodicAccountManagementData(s, req);
			break;
	
			case 'DeletePeriodicAccountManagementData':
	
	
			default:
			console.error('Unkown method: ', method);
			res.status = 400;
			res.end();
			return;
		}
	
		const xml = getXmlResponse(r);
		//console.debug('XML response:\n', xml);
		res.end(xml);
	}

	handleApiRequest(body, res) {
		res.setHeader('Content-Type', 'application/json');
	
		const req = JSON.parse(body);
		const msisdn = req.subscriberNumber;
		const s = Object.assign(defaultSubscriber(msisdn), req);
	
		this.subscribers.set(msisdn, s);
		const r = JSON.stringify(s);
		res.end(r);
	}

	addSubscriber(msisdn, s) {
		console.debug('Adding subscriber:\n', s);

		let o = {};
		if (s.serviceClassCurrent && !s.serviceClassOriginal)
			o.serviceClassOriginal = s.serviceClassCurrent;
	
		if (s.currency1 && !s.currency2)
			o.currency2 = s.currency1;
	
		if (s.accountValue1 && !s.accountValue2)
			o.accountValue2 = s.accountValue1;
	
		this.subscribers.set(msisdn, Object.assign(defaultSubscriber(msisdn), s, o));
	}

	addSubscribers(subscribers) {
		for (const s of subscribers)
			this.addSubscriber(s.subscriberNumber, s);
	} 

	renderSubscribers() {
		let html = `<!DOCTYPE html><html>
	<head>
	<style>
		table {
		  border-collapse: collapse;
		  width: 100%;
		}
		
		th, td {
		  text-align: left;
		  padding: 8px;
		}
		
		tr:nth-child(even) {background-color: #f2f2f2;}
	</style>
	</head><body>
	<h2>Subscribers</h2>
	<div style="overflow-x:auto;">
	<table><tr>`;
	
		headers.map((v, k) => {
			html += `<th>${v}</th>`;
		});
	
		html += '</tr>';
	
		//console.log(subscribers);
		for (const [msisdn, s] of this.subscribers) {
			html += `<tr><td>${msisdn}</td>`;
	
			for (const k in s) {
				const v = s[k];
				if (typeof v === 'object') {
					if (k === 'accountFlags' || k === 'accountFlagsBefore') 
						html += `<td>${flagsToString(v)}</td>`;
					else if (v instanceof Date)
						html += `<td>${v.toISOString()}</td>`;
					else
						html += `<td>${v}</td>`;
				}
				else
					html += `<td>${v}</td>`;
			}
			
			html += '</tr>';
		}
	
		html += '</table></div></body></html>';
	
		return html;
	}
};

function getXmlResponse(r) {
	return hux.encode(r);
}

function getAccountDetails(s, req) {
	console.debug('GetAccountDetails');

	const r = { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, languageIDCurrent: s.languageIDCurrent
		, serviceClassCurrent: s.serviceClassCurrent
		, serviceClassOriginal: s.serviceClassOriginal
		, serviceOfferings: s.serviceOfferings
		, accountActivatedFlag: s.activatedFlag
		, activationDate: s.activationDate
		, accountFlags: s.accountFlags
		, accountFlagsBefore: s.accountFlagsBefore
	};

	setAmount(s, r);

	const flags = req.requestedInformationFlags;
	if (flags) {
		if (flags.requestMasterAccountBalanceFlag) {
			setMasterAccountInfo(s, r);
		}
	}

	if (req.requestPamInformationFlag) {
		r.pamInformationList = s.pamInformationList;
	}

	return r;
}

function updateAccountDetails(s, req) {
	console.debug('UpdateAccountDetails');

	const r = { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, languageIDCurrent: s.languageIDCurrent
		, serviceClassCurrent: s.serviceClassCurrent
		, serviceClassOriginal: s.serviceClassOriginal
		, serviceOfferings: s.serviceOfferings
		, accountActivatedFlag: s.activatedFlag
		, activationDate: s.activationDate
	};

	if (req.originOperatorID)
		r.originOperatorID = req.originOperatorID;

	r.currency1 = s.currency1;
	r.currency2 = s.currency2;

	return r;
}

function updateBalanceAndDate(s, req) {
	console.debug('UpdateBalanceAndDate');

	const r = { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
	};

	if (req.adjustmentAmountRelative) {

		const value = Number(req.adjustmentAmountRelative); 
		console.log('\tCHARGED: %d %s', value, req.transactionCurrency);

		s.accountValue1 += value;
		s.accountValue2 = s.accountValue1;

		setAmount(s, r);
	}

	//console.debug(req);
	if (req.dedicatedAccountUpdateInformation) {
		//console.debug(req.dedicatedAccountUpdateInformation);
		const da = req.dedicatedAccountUpdateInformation[0];
		//console.debug('dedicatedAccountUpdateInformation\n', da);
		s.da = da;

		//r.currency1 = req.transactionCurrency;
		//r.currency2 = r.currency1;

		r.dedicatedAccountChangeInformation = [ 
			{
				dedicatedAccountID: da.dedicatedAccountID
				, dedicatedAccountValue1: da.dedicatedAccountValueNew
				, dedicatedAccountValue1: da.dedicatedAccountValueNew
				, expiryDate: da.expiryDate
				, dedicatedAccountUnitType: da.dedicatedAccountUnitType
			}
		];
	}

	return r;
}

function getBalanceAndDate(s, req) {
	console.debug('GetBalanceAndDate');

	const r = { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, serviceClassCurrent: s.serviceClassCurrent
		, supervisionExpiryDate: s.supervisionExpiryDate
		, creditClearanceDate: s.creditClearanceDate
		, serviceRemovalDate: s.serviceRemovalDate
		, serviceFeeExpiryDate: s.serviceFeeExpiryDate
		, languageIDCurrent: s.languageIDCurrent
		, accountFlagsAfter: s.accountFlags
		, accountFlagsBefore: s.accountFlags
		, offerInformationList: []
	};

	setAmount(s, r);

	return r;
}

function updateOffer(s, req) {
	console.debug('UpdateOffer');

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, currency1: s.currency1
		, currency2: s.currency2
		, offerID: req.offerID
		, expiryDate: req.expiryDate
		, offerType: req.offerType
		, offerState: 0
	};
}

function deleteOffer(s, req) {
	console.debug('DeleteOffer');

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, currency1: s.currency1
		, currency2: s.currency2
		, offerID: req.offerID
		, offerType: 0
	};
}

function getUsageThresholdsAndCounters(s, req) {
	console.debug('GetUsageThresholdsAndCounters');

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, currency1: s.currency1
		, currency2: s.currency2
		, usageCounterUsageThresholdInformation: s.usageInformation.usageThresholdInformation
	};
}

function updateUsageThresholdsAndCounters(s, req) {
	console.debug('UpdateUsageThresholdsAndCounters');
	//console.debug(req);

	const usage = s.usageInformation;
	if ('usageCounterUpdateInformation' in req && req.usageCounterUpdateInformation.length > 0) {
		const newinfo = req.usageCounterUpdateInformation[0];

		usage.usageCounterID = newinfo.usageCounterID;
		usage.usageCounterValue = newinfo.usageCounterValueNew;
	}

	if ('usageThresholdUpdateInformation' in req && req.usageThresholdUpdateInformation.length > 0) {
		const list = usage.usageThresholdInformation;
		if (list.length == 0)
			list.push({});

		const newinfo = req.usageThresholdUpdateInformation[0];
		const threshold = list[0];
		threshold.usageThresholdID = newinfo.usageThresholdID;
		threshold.usageThresholdValue = newinfo.usageThresholdValueNew;
		threshold.usageThresholdSource = 3;
	}

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, currency1: s.currency1
		, currency2: s.currency2
		, usageCounterUsageThresholdInformation: usage
	};
}

function refill(s, req) {
	console.debug('Refill');

	s.accountValue1 += Number(req.transactionAmount);
	s.accountValue2 = s.accountValue1;

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, masterAccountNumber: req.subscriberNumber
		, languageIDCurrent: s.languageIDCurrent
		, transactionCurrency: req.transactionCurrency
		, transactionAmount: req.transactionAmount
		, transactionAmountConverted: req.transactionAmount
		, currency1: s.currency1
		, currency2: s.currency2
	};
}

function getFaFList(s, req) {
	console.debug('GetFaFList');

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, fafInformationList: s.fafInformationList
	};
}

function addPeriodicAccountManagementData(s, req) {
	console.debug('AddPeriodicAccountManagementData');

	s.pamInformationList.push(...req.pamInformationList);

	return { 
		responseCode: 0
		, originTransactionID: String(req.originTransactionID)
		, originOperatorID: req.originOperatorID
		, pamInformationList: s.pamInformationList
	};
}

function defaultSubscriber(msisdn) {
	const accountFlags = {
		activationStatusFlag: true,
		negativeBarringStatusFlag: false,
		supervisionPeriodWarningActiveFlag: true,
		serviceFeePeriodWarningActiveFlag: false,
		supervisionPeriodExpiryFlag: false,
		serviceFeePeriodExpiryFlag: false,
	};

	const currency = 'EUR';
	const ammount = 1000000;

	return {
		languageIDCurrent: 1,
		serviceClassCurrent: 1,
		serviceClassOriginal: 1,
		serviceOfferings: [],
		activatedFlag: true,
		//activationDate: new Date(),
		activationDate: date(-1),
		accountFlags,
		accountFlagsBefore: accountFlags,
		masterSubscriberFlag: true,
		masterAccountNumber: String(msisdn),
		supervisionExpiryDate: date(7),
		creditClearanceDate: date(10),
		serviceRemovalDate: date(12),
		serviceFeeExpiryDate: date(8),
		currency1: currency,
		accountValue1: ammount,
		currency2: currency,
		accountValue2: ammount
		, offerInformationList: []
		, pamInformationList: []
		, serviceOfferings: []
		, usageInformation: {
				usageCounterID: 0
				, usageCounterValue: 0
				, usageThresholdInformation: []
			}
		, fafInformationList: []
	};
}

function setMasterAccountInfo(s, r) {
	r.masterSubscriberFlag = s.masterSubscriberFlag;
	r.masterAccountNumber = s.masterAccountNumber;
	r.supervisionExpiryDate = s.supervisionExpiryDate;
	r.creditClearanceDate = s.creditClearanceDate;
	r.serviceRemovalDate = s.serviceRemovalDate;
	r.serviceFeeExpiryDate = s.serviceFeeExpiryDate;
}

function setAmount(s, r) {
	console.log('\tBALANCE: %d %s', s.accountValue1, s.currency1);
	r.currency1 = s.currency1;
	r.accountValue1 = String(s.accountValue1);
	r.currency2 = s.currency2;
	r.accountValue2 =  String(s.accountValue2);
}

/*
addSubscriber(********, {
	currency1: 'EUR',
	accountValue1: 1000000,
});

addSubscriber(********, {
	languageIDCurrent: 2,
	serviceClassCurrent: 4,
	currency1: 'USD',
	accountValue1: 500000,
});

addSubscriber(********, {
	languageIDCurrent: 3,
	serviceClassCurrent: 2,
	currency1: 'ZAR',
	accountValue1: 2000000,
});

addSubscriber(********, {
	languageIDCurrent: 2,
	serviceClassCurrent: 5,
	serviceClassOriginal: 1,
	currency1: 'BGN',
	accountValue1: 500,
});

addSubscriber(********, {
	languageIDCurrent: 6,
	serviceClassCurrent: 7,
	currency1: 'RUB',
	accountValue1: 400000,
});

addSubscriber(*********, {
	serviceClassCurrent: 4
	, currency1: 'CFR'
});
*/

function date(days) {
	return new Date(Date.now() + (days * 60 * 60 * 24 * 1000));
}

function flagsToString(flags) {
	let s = '';
	for (const i in flags) 
		s += flags[i] ? '1' : '0';
	return s;
}

if (require.main === module) {
	const yargs = require('yargs')
		.usage('Usage: $0 <command> [options]')
		.option( 'c', { alias: 'config', demand: false, describe: 'config  file to be loaded', type: 'string' } )
		.help('h').alias('h', 'help')
		.epilog('\251 2019. Concurrent Systems');

	//console.log(yargs);

	const argv = yargs.argv;

	const server = new Server;
	server.addSubscriber(*********, {
		serviceClassCurrent: 4
		, currency1: 'CFR'
	});

	server.start();
}

module.exports = {
	Server
}