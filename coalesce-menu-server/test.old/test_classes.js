const config = require('./sample_menu.json')
const readlineSync = require('readline-sync')

const lang = 'eng'

const labelMap = new Map
const variableMap = new Map
const menuMap = new Map

class UserIO {
	respond(text, prompt) {}
	getInput() {}
}

class ConsoleIO extends UserIO {
	respond(text) {
		console.log(text)
	}

	getInput() {
		const userInput = readlineSync.question()
		return userInput
	}
}

class HuXIO extends UserIO {
	respond(text) {}

	async getInput() {
	}
}

const io = new ConsoleIO()

class Renderable {
	render() {
	}
}

class Executable {
	execute() {}
}

class Prompt {
	respond() {}
}

class Menu extends Prompt {
	constructor(config) {
		super()
		this.renderables = []
		for (const r of config.content) {
			if (r.display) {
				this.renderables.push(new Display(r.display))
			} else if (r.item) {
				this.renderables.push(new Item(r.item))
			}
		}
	}

	async respond() {
		let output = ''
		for (const r of this.renderables) {
			output += r.render()
		}

		await this.handleInput(output)
	}

	async handleInput(text) {
		io.respond(text)
		const userInput = await io.getInput()

		for (const i of this.renderables) {
			if (!(i instanceof Item))
				continue

			if (i.execute(userInput)) 
				break
		}
	}
}

class Ask extends Prompt {
	constructor(config) {
		super()
		this.msg = getMessageContent(config.message_id)
		this.variable = config.variable
	}

	async respond() {
		io.respond(this.msg)
		const userInput = await io.getInput()
		variableMap.set(this.variable, userInput)
	}
}

class Display extends Renderable {
	constructor(config) {
		super()
		this.msg = getMessageContent(config.message_id)
	}

	render() {
		return this.msg
	}
}

class Item extends Display {
	constructor(config) {
		super(config)
		this.selector = config.selector
		if (config.body) {
			this.body = new Body(config.body)
		}
	}

	render() {
		return this.selector + '. ' + this.msg
	}

	execute(selection) {
		if (this.selector != selection)
			return false

		if (this.body) {
			this.body.execute()
		}

		return true
	}
}

class Assign extends Executable {
	constructor(config) {
		super()
		this.value = config.value
		this.variable = config.variable
	}

	execute() {
		variableMap.set(this.variable, this.value)
		return true
	}
}

class AssignNull extends Executable {
	constructor(config) {
		super()
		this.variables = config.variables
	}

	execute() {
		for (const v of this.variables) {
			variableMap.set(v, undefined)
		}
		return true
	}
}

class Clear extends Executable {
	constructor(config) {
		super()
		this.variables = config.variables
	}

	execute() {
		for (const v of this.variables) {
			variableMap.delete(v)
		}
		return true
	}
}

class Goto extends Executable {
	constructor(config) {
		super()
		this.target = config.target
	}

	execute() {
		const menu = menuMap.get(this.target)
		if (menu) {
			try {
				menu.respond()
			} catch(e) {}

			return true
		}

		const b = labelMap.get(this.target)
		if (!b) {
			// TODO handle error properly
			return false
		}

		return b.body.execute(b.index)
	}
}

class If extends Executable {
	constructor(config) {
		super()

		this.operator = config.operator
		this.variable = config.variable
		this.rhsConstants = config.rhs_constants

		this.onTrue = new Body(config.body)
		this.onFalse = new Body(config.else)
	}

	execute() {
		const value = variableMap.get(this.variable)
		// TODO what to do if value is undefined/null

		let result = false
		switch(this.operator) {
			case 'eq':
				if (this.rhsConstants.includes(value)) {
					result = true
				}
				break
		}

		if (result) {
			this.onTrue.execute()
		} else {
			this.onFalse.execute()
		}

		return true
	}
}

class End extends Executable {
	constructor(config) {
		super()
	}

	execute() {
		return false
	}
}

class Body {
	constructor(config) {
		this.elements = []
		let labelCount = 0
		for (const i in config) {
			const element = config[i]
			if (element.label) {
				labelMap.set(element.label.name, {body: this, index: i - labelCount})
				++labelCount
			} else if (element.display) {
				this.elements.push(new Display(element.display))
			} else if (element.assign) {
				this.elements.push(new Assign(element.assign))
			} else if (element.assign_null) {
				this.elements.push(new AssignNull(element.assign_null))
			} else if (element.clear) {
				this.elements.push(new Clear(element.clear))
			} else if (element.goto) {
				this.elements.push(new Goto(element.goto))
			} else if (element.ask) {
				this.elements.push(new Ask(element.ask))
			} else if (element.if) {
				this.elements.push(new If(element.if))
			} else if (element.end) {
				this.elements.push(new End(element.end))
			}
		}
	}

	async execute(index) {
		let output = ''
		for (let i = index ? index : 0; i < this.elements.length; ++i) {
			const e = this.elements[i];
			if (e instanceof Executable) {
				if (!e.execute()) {
					break
				}
			}
			else if (e instanceof Renderable) {
				output += e.render()
			} else if (e instanceof Prompt) {
				await e.respond()
			}
		}

		if (output.length > 0) {
			io.respond(output)
		}

		return true
	}
}

function getMessageContent(id) {
	return config.messages[id].content[lang]
}

async function main() {
	for (const menuLabel in config.menus) {
		const menu = new Menu(config.menus[menuLabel])
		menuMap.set(menuLabel, menu)
	}
	const menu = menuMap.get(config.root_menu)
	try {
		await menu.respond()
	} catch(e) {}

	console.log(variableMap)
}

main()