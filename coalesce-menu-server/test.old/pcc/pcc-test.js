#!/usr/bin/env node

const path = require('path') 
	, Server = require('../airsim').Server
	, sendUssdRequest = require('../hxcsim').sendUssdRequest
	;
	
class Test {

	async load(file) {
		const cfg = require(file);

		const server = new Server;

		server.addSubscribers(cfg.subscribers);
		server.start();

		console.log('Executing test set: ', cfg.title);

		for (const t of cfg.tests) {
			console.log('Name: ', t.name);
			const responses = t.responses;
			//console.debug(responses);

			await sendUssdRequest('http://127.0.0.1:4021/RPC2', t.msisdn, t.ussd, (status) => {
				const r = responses.shift();
				if (status == r.status)
					console.log('Status match');
				else
					console.log('Status NOT match: ', status, ', ', r.status);

				return r.respond;
			});

		}
	}
}

const test = new Test;
test.load(path.join(__dirname, 'test2.json') );
	