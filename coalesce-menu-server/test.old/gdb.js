#!/usr/bin/env node

const request = require('request')
	, readlineSync = require('readline-sync')
	//, random = require('random')
	, URL = require('url')
	, util = require('@coalesce/util')
	, hux = require('@coalesce/xmlrpc')
	, config = require('./sample_menu.json')
	;
	
const yargs = require('yargs')
	.usage('Usage: $0 [options]')
	.option( 'h', { alias: 'host',   demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
	.option( 'm', { alias: 'msisdn', demand: false,  describe: 'MSISDN', type: 'string', default: '123456789' } )
	.option( 's', { alias: 'ussd',   demand: false,  describe: 'USSD string', type: 'string', default: '*123#' } )
	.help('help')
	.example('$0 -h http://127.0.0.1:4021 -m12345675 -s*353#')
	.example('$0 -h https://csys-products-coalesce-studio-coalesce-menu-server-staging.coalescelab.com -m10003 -s*353#')
	.wrap(null)
	.version('1.0.0')
	.epilog('\251 2019. Concurrent Systems');

const argv = yargs.argv;

function usage() {
	yargs.showHelp();
}

function getUrl() {
	const url = URL.parse(argv.host);

	if (url.host)
		return url.path == '/' ? url.href + 'dbg' : url.href;

	
	return 'http://' + argv.host + ':' + String(argv.port) + '/dbg';	
}

function sendRequest(args) {
	return new Promise((resolve, reject) => {
		request.post(args, (error, response, data) => {
			if (error) 
				reject(error);
			else {
				//console.log('DATA: ' + data);
				resolve(data);
			}
		});
	});
}

async function sendJsonRequest(url, req) {
	const args = { url, body: JSON.stringify(req), headers: { 'Content-Type': 'application/json' } };
	const r = await sendRequest(args);

	if (r) return JSON.parse(r);
}

const boundary = "xxxxxxxxxx";

function encode(r) {
	let data = '--' + boundary + '\r\n';
	data += 'Content-Disposition: form-data; name="file"; filename="test.json"\r\n';
	//data += 'Content-Type:application/json\r\n\r\n';
	data += 'Content-Type:application/octet-stream\r\n\r\n';
	data += Buffer.concat([ Buffer.from(JSON.stringify(r), 'binary' ) ]);
	//data += '\r\n--' + boundary + '\r\n';
	data += '\r\n--' + boundary + '--\r\n';
	return data;
}

async function main() {
	let url = getUrl();

	const json = await sendRequest({ url, body: encode(config), headers: {
		'Content-Type':  util.format('multipart/form-data; boundary=%s', boundary)
		}});

	const r = JSON.parse(json);
	console.log('Debug session created: %s', r.id);

	url += '/' + r.id;
	
	let response = false;
	let done = false;
	let output;
	while (!done) {

		if (response) {
			console.log(output);
			const input = readlineSync.question('> ');
			const r = await sendJsonRequest(url, { command: 'CONTINUE', input });

			done = r.done;
			response = r.response;
			output = r.output;

			if (r.node_id) {
				console.log('BP: ' + r.node_id);
				console.log('ASM: ' + r.asm);
				console.log('OUTPUT: ' + output);
				console.log('VARS: ', r.vars);
			}

			continue;
		}

		const cmd = readlineSync.question('# ').split(/\s+/);
		
		switch (cmd[0]) {
			case 'b':
			await sendJsonRequest(url, { command: 'BREAKPOINT', node_id: cmd[1] });
			break;

			case 'd':
			await sendJsonRequest(url,{ command: 'DELETE', node_id: cmd[1] });
			break;

			case 'c': {
				const r = await sendJsonRequest(url, { command: 'CONTINUE' });

				done = r.done;
				response = r.response;
				output = r.output;

				if (r.node_id) {
					console.log('BP: ' + r.node_id);
					console.log('ASM: ' + r.asm);
					console.log('OUTPUT: ' + output);
					console.log('VARS: ', r.vars);
				}
			}
			break;
		}
	}

	console.log(output);
	console.log('Bye !!!');
}

main();