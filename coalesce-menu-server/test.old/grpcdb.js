#!/usr/bin/env node

const PROTO_PATH = __dirname + '/../proto/debugger.proto';
const grpc = require('grpc');
const protoLoader = require('@grpc/proto-loader');
const readline = require('readline-sync');
const fs = require('fs');

const yargs = require('yargs')
	.usage('Usage: $0 [options]')
	.option( 'h', { alias: 'host',   demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
	.option( 'p', { alias: 'port',   demand: false, describe: 'Host port', type: 'number', default: 5001 } )
	.option( 'c', { alias: 'config', demand: true, describe: 'Config json file', type: 'string' } )
	.option( 'm', { alias: 'msisdn', demand: false, describe: 'MSISDN', type: 'string', default: '27711234567' } )
	.option( 'i', { alias: 'imsi', demand: false, describe: 'IMSI', type: 'string', default: '123456789012345' } )
	.help('help')
	.example('$0 -c ./sample_menu.json')
	.wrap(null)
	.version('1.0.0')
	.epilog('\251 2019. Concurrent Systems');

const argv = yargs.argv;

if (!fs.existsSync(argv.config)) {
	console.error('Config file not exists: ' + argv.config);
	process.exit(1);
}

function usage() {
	yargs.showHelp();
}

// Suggested options for similarity to existing grpc.load behavior
const packageDefinition = protoLoader.loadSync(PROTO_PATH,
	{keepCase: true,
	 longs: Number,
	 enums: String,
	 defaults: false,
	 oneofs: true
	});
	
const proto = grpc.loadPackageDefinition(packageDefinition);
const client = new proto.Debugger(argv.host + ':' + argv.port, grpc.credentials.createInsecure());

var id;
var done = false;
var response = false;
var output;
let cmd;
let varsSet = {};
let varsClear = []

function handleInput() {
	if (done) {
		console.log('Bye !!!');
		return;
	}

	if (response) {
		//console.log(output);
		const input = readline.question('> ');
		if (cmd && cmd[0] == 'c') {
			handleContinueCommand(input);
		} else if (cmd && cmd[0] == 's') {
			handleStepCommand(input);
		}
		return;
	}

	cmd = readline.question('# ').split(/\s+/);
	switch (cmd[0]) {
		case 'h':
		console.log('h: shows this message');
		console.log('c: continue execution');
		console.log('s: step')
		console.log('b: set breakpoint');
		console.log('d: delete breakpoint');
		console.log('r: re-run menu with existing config');
		console.log('r r: re-run menu by reloading the config from file')
		console.log('v: set variable')
		console.log('e: clear variable(s)')
		handleInput();
		break;

		case 'r':
		handleRunCommand(cmd[1]);
		break;

		case 'c':
		handleContinueCommand();
		break;

		case 'b':
		handleAddBreakpointCommand(cmd[1]);
		break;

		case 's':
		handleStepCommand();
		break;

		case 'v':
		handleSetVariable(cmd[1], cmd[2]);
		break;

		case 'e':
		handleClearVariables(cmd.slice(1));
		break;
	}
}

function handleRunCommand(reload) {
	const req = { msisdn: argv.msisdn, imsi: argv.imsi };
	if (id) {
		req.id = id
	}

	if (reload == 'r') {
		console.log('Using config file', argv.config);
		req.config = fs.readFileSync(argv.config).toString();
		//console.log(req.config);
	}

	client.run(req, function(err, r) {
		//console.log('CALLBACK: ',  response);
	
		id = r.id;
		if (r.status > 0) {
			console.error('Failed to start debugging session');
			return;
		}

		handleInput();
	});
}

function handleContinueCommand(input) {
	const request = { id, command: 'CONTINUE', vars_set: JSON.stringify(varsSet), vars_clear: varsClear };
	if (input)
		request.input = input;

	client.command(request, function(err, r) {
		//console.log('COMMAND CB: ', r);
		done = r.done;
		output = r.output;
		response = r.response;

		printDebugInfo(r);

		handleInput();
	});

	varsSet = {};
	varsClear = [];
}

function handleAddBreakpointCommand(node_id) {
	client.command({ id, command: 'BREAKPOINT', input: node_id }, function(err, r) {
		handleInput();
	});
}

function handleStepCommand(input) {
	client.command({ id, command: 'STEP', input, vars_set: JSON.stringify(varsSet), vars_clear: varsClear }, (err, r) => {
		done = r.done;
		output = r.output;
		response = r.response;

		printDebugInfo(r);

		handleInput();
	});

	varsSet = {};
	varsClear = [];
}

function handleSetVariable(name, value) {
	if (name) {
		varsSet[name] = value;
	}
	varsClear = varsClear.filter((v) => {
		return v != name;
	});
	handleInput();
}

function handleClearVariables(vars) {
	varsClear.push(...vars);
	handleInput();
}

function printDebugInfo(r) {
	if (r.node_id) {
		console.log('BP: ' + r.node_id);
		console.log('VARS: ' + r.vars);
		console.log('OUTPUT: ' + output);
	} else {
		console.log(output);
	}
}

//handleInput();
handleRunCommand('r');
