{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"node_id": "faa71de0-971e-11e9-96b9-6f4be5d6caef", "message_id": "main_menu_header"}}, {"item": {"message_id": "usage_checks", "selector": "1", "body": [{"goto": {"node_id": "8f2ad4c0-971f-11e9-95a4-5d2a96b4e946", "target": "usage_check_menu"}}]}}, {"item": {"message_id": "enable_roaming", "selector": "2", "body": [{"ask": {"node_id": "974da7e0-971f-11e9-ab3b-0d0051689cf3", "message_id": "enable_roaming_confirmation", "variable": "v1"}}, {"if": {"node_id": "9ba25c00-971f-11e9-adcf-dfb1dc6b6b17", "operator": "eq", "variable": "v1", "rhs_constants": ["Y", "y", "yes", "1"], "body": [{"display": {"node_id": "a05b0210-971f-11e9-a2c2-352c18815044", "message_id": "roaming_enabled"}}, {"end": {"node_id": "a4b49830-971f-11e9-a8ad-c5c5249db892"}}, {"display": {"node_id": "a8464a70-971f-11e9-9543-d9edbf81fde8", "message_id": "roaming_enabled"}}], "else": [{"display": {"node_id": "aba13540-971f-11e9-b583-e730777934ca", "message_id": "roaming_not_enabled"}}]}}, {"end": {"node_id": "af5b08f0-971f-11e9-a404-f96b683e6205"}}]}}, {"item": {"message_id": "balance_check_entry", "selector": "3", "body": [{"goto": {"node_id": "b516ee30-971f-11e9-b532-c30c63fcf7d4", "target": "balance_check_label"}}]}}, {"item": {"message_id": "exit", "selector": "9", "body": [{"assign": {"node_id": "b988ee50-971f-11e9-8400-a1efb62c0b7a", "variable": "v2", "value": "v2_value"}}, {"assign": {"node_id": "bc56db60-971f-11e9-a6f8-2be4f01619c9", "variable": "v3", "value": "v3_value"}}, {"assign": {"node_id": "c9e6d780-971f-11e9-be5d-b5c12ef46a88", "variable": "v4", "value": "v4_value"}}, {"clear": {"node_id": "cd970e40-971f-11e9-ba00-d13e47f95d60", "variables": ["v4"]}}, {"assign_null": {"node_id": "d11586a0-971f-11e9-b333-63ce03a18e9a", "variables": ["v5_null"]}}, {"end": {"node_id": "d4f558e0-971f-11e9-8247-632913a84a70"}}]}}]}, "usage_check_menu": {"content": [{"display": {"node_id": "d97d03e0-971f-11e9-a46c-077b1de8ec7c", "message_id": "usage_check_menu_header"}}, {"item": {"message_id": "balance_check_entry", "selector": "1", "body": [{"label": {"node_id": "dd728100-971f-11e9-9e02-d96d60451aa6", "name": "balance_check_label"}}, {"display": {"node_id": "e240d1f0-971f-11e9-ac43-0d1f843c8541", "message_id": "balance_check_response"}}, {"end": {"node_id": "e516d550-971f-11e9-b2ea-03597c280587"}}]}}, {"item": {"message_id": "data_check_entry", "selector": "2", "body": [{"display": {"node_id": "e96dd360-971f-11e9-9a31-19b1fe770003", "message_id": "data_check_response"}}, {"end": {"node_id": "ec636cb0-971f-11e9-a57c-ed347dfb0319"}}]}}, {"item": {"message_id": "back", "selector": "3", "body": [{"goto": {"node_id": "f0cbcfe0-971f-11e9-9a63-1de5c232bbb5", "target": "main_menu"}}]}}]}}, "messages": {"usage_check_menu_header": {"description": "Usage check header line", "content": {"eng": "eng: Please select from the following menu:\n", "fra": "fra: Please select from the following menu:\n"}}, "balance_check_entry": {"description": "Balance check entry", "content": {"eng": "eng: Check Balance\n", "fra": "fra: Check Balance\n"}}, "balance_check_response": {"description": "Balance check response", "content": {"eng": "eng: Your balance is 100$\n", "fra": "fra: Your balance is 100$\n"}}, "data_check_entry": {"description": "Data check entry", "content": {"eng": "eng: Check Data\n", "fra": "fra: Check Data\n"}}, "data_check_response": {"description": "Data check response", "content": {"eng": "eng: You have 100MB left\n", "fra": "fra: You have 100MB left\n"}}, "main_menu_header": {"description": "Main menu header line", "content": {"eng": "eng: Welcome!\n", "fra": "fra: Welcome!\n"}}, "usage_checks": {"description": "Usage checks entry", "content": {"eng": "eng: Check usage\n", "fra": "fra: Check usage\n"}}, "exit": {"description": "Exit entry", "content": {"eng": "eng: Exit menu\n", "fra": "fra: Exit menu\n"}}, "back": {"description": "Back entry", "content": {"eng": "eng: Back\n", "fra": "fra: Back\n"}}, "enable_roaming": {"description": "Enable roaming entry", "content": {"eng": "eng: Enable roaming\n", "fra": "fra: Enable roaming\n"}}, "enable_roaming_confirmation": {"description": "Enable roaming confirmation", "content": {"eng": "eng: Are you sure you want to enable roaming?", "fra": "fra: Are you sure you want to enable roaming?"}}, "roaming_enabled": {"description": "Roaming enabled", "content": {"eng": "eng: You have successfully enabled roaming!", "fra": "fra: You have successfully enabled roaming!"}}, "roaming_not_enabled": {"description": "Roaming not enabled", "content": {"eng": "eng: You have not enabled roaming, please try again!", "fra": "fra: You have not enabled roaming, please try again!"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}, "ussd_matches": [{"request": "*2#", "body": [{"goto": {"target": "usage_check_menu"}}]}]}