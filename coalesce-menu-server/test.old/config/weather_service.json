{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"message_id": "main_menu_header"}}, {"item": {"message_id": "weather_london", "selector": "1", "body": [{"assign": {"variable": "v1", "value": "London"}}, {"component": {"name": "get_weather_info", "args": {"city": "v1"}}}, {"display": {"message_id": "weather_info"}}]}}, {"item": {"message_id": "weather_johannesburg", "selector": "2", "body": [{"assign": {"variable": "v1", "value": "Johannesburg"}}, {"component": {"name": "get_weather_info", "args": {"city": "v1"}}}, {"display": {"message_id": "weather_info"}}]}}, {"item": {"message_id": "weather_sofia", "selector": "3", "body": [{"assign": {"variable": "v1", "value": "Sofia"}}, {"component": {"name": "get_weather_info", "args": {"city": "v1"}}}, {"display": {"message_id": "weather_info"}}]}}, {"item": {"message_id": "exit", "selector": "9", "body": [{"display": {"message_id": "goodbye"}}]}}]}}, "messages": {"main_menu_header": {"description": "Main menu header line", "content": {"eng": "TODAY'S WEATHER\n", "fra": "LA MÉTÉO D'AUJOURD'HUI\n"}}, "weather_london": {"description": "Weather in London entry", "content": {"eng": "Weather in London\n", "fra": "Mé<PERSON>o à Londres\n"}}, "weather_johannesburg": {"description": "Weather in Johannesburg entry", "content": {"eng": "Weather in Johannesburg\n", "fra": "Météo à Johannesburg\n"}}, "weather_sofia": {"description": "Weather in Sofia entry", "content": {"eng": "Weather in Sofia\n", "fra": "Météo à Sofia\n"}}, "weather_info": {"description": "Weather information", "content": {"eng": "At the moment is %degrees% degrees\nFeels like %feel_degrees% degrees\nThe weather is %state%\nCurrent humidity is %humidity%%\nWind direction is %wind_direction%\nLast update time %last_update%\n", "fra": "Pour le moment est de %degrees% degrés\nSe sent comme %feel_degrees% degrés\nLe temps est %state%\nL'humidité actuelle est %humidity%%\nLa direction du vent est %wind_direction%\nDernière mise à jour %last_update%\n"}}, "goodbye": {"description": "Goodbye entry", "content": {"eng": "Goodbye\n", "fra": "Au revoir\n"}}, "exit": {"description": "Exit entry", "content": {"eng": "Exit Weather App\n", "fra": "<PERSON><PERSON><PERSON>\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}, "ussd_matches": [{"request": "*2#", "body": [{"goto": {"target": "usage_check_menu"}}]}]}