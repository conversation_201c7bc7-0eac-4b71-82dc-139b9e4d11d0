{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"node_id": "node_54d3f6f0-8dd0-4159-a8a8-ba3b6d51eac9", "message_id": "main_menu_header"}}, {"item": {"message_id": "mobile_internet", "node_id": "node_4cf21c9c-3d77-4c79-afaf-2fcd39a20fdd", "selector": "1", "body": [{"menu": {"content": [{"label": {"name": "internet_menu"}}, {"display": {"message_id": "internet_menu", "node_id": "node_b33970fb-fa8e-4725-bbbe-ac2648d316ca"}}, {"item": {"message_id": "internet_offers", "node_id": "node_dcfe4723-8e4e-4cdb-85e6-6863349f4b3b", "selector": "1", "body": [{"menu": {"content": [{"label": {"name": "internet_offers"}}, {"display": {"message_id": "internet_offers", "node_id": "node_b33970fb-fa8e-4725-bbbe-ac2648d316ca"}}, {"item": {"node_id": "node_fb7627a2-ffa1-46d4-a79f-6ffed7efd9b4", "selector": "1", "message_id": "day", "body": [{"menu": {"content": [{"display": {"node_id": "node_cbec542f-dfea-4cfa-a622-fd89d48fd2a4", "message_id": "day"}}, {"item": {"node_id": "node_c219cf0f-ebd3-438a-b51b-1e235bbac836", "selector": "1", "message_id": "100_mb_24h", "body": [{"display": {"node_id": "node_51d5fe35-c27c-4c2c-9511-fc3e36c3a717", "message_id": "100_mb_24h_enabled"}}]}}, {"item": {"node_id": "node_10fadc86-3f7d-446d-9eaf-edf5bc154e80", "selector": "2", "message_id": "200_mb_24h", "body": [{"display": {"node_id": "node_99c473c2-0108-4f5b-addb-6d180c6ac147", "message_id": "200_mb_24h_enabled"}}]}}, {"item": {"node_id": "node_cbec542f-dfea-4cfa-a622-fd89d48fd2a4", "selector": "3", "message_id": "500_mb_24h", "body": [{"display": {"node_id": "node_ed52342c-69b1-4ad8-8158-c6f0b9af1bc0", "message_id": "500_mb_24h_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"node_id": "node_fb7627a2-ffa1-46d4-a79f-6ffed7efd9b4", "selector": "2", "message_id": "weekly", "body": [{"menu": {"content": [{"display": {"message_id": "weekly"}}, {"item": {"selector": "1", "message_id": "week_1000", "body": [{"display": {"message_id": "week_1000_enabled"}}]}}, {"item": {"selector": "2", "message_id": "week_1500", "body": [{"display": {"message_id": "week_1500_enabled"}}]}}, {"item": {"selector": "3", "message_id": "week_2500", "body": [{"display": {"message_id": "week_2500_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"node_id": "node_fb7627a2-ffa1-46d4-a79f-6ffed7efd9b4", "selector": "3", "message_id": "monthly", "body": [{"menu": {"content": [{"display": {"message_id": "monthly"}}, {"item": {"selector": "1", "message_id": "month_7000", "body": [{"display": {"message_id": "month_7000_enabled"}}]}}, {"item": {"selector": "2", "message_id": "month_9000", "body": [{"display": {"message_id": "month_9000_enabled"}}]}}, {"item": {"selector": "3", "message_id": "month_15000", "body": [{"display": {"message_id": "month_15000_enabled"}}]}}, {"item": {"selector": "4", "message_id": "month_20000", "body": [{"display": {"message_id": "month_20000_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "4", "message_id": "nightly", "body": [{"menu": {"content": [{"display": {"message_id": "nightly"}}, {"item": {"selector": "1", "message_id": "night_200", "body": [{"display": {"message_id": "night_200_enabled"}}]}}, {"item": {"selector": "2", "message_id": "night_500", "body": [{"display": {"message_id": "night_500_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "5", "message_id": "weekend", "body": [{"menu": {"content": [{"display": {"message_id": "weekend"}}, {"item": {"selector": "1", "message_id": "weekend_250", "body": [{"display": {"message_id": "weekend_250_enabled"}}]}}, {"item": {"selector": "2", "message_id": "weekend_1500", "body": [{"display": {"message_id": "weekend_1500_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"message_id": "internet_services", "selector": "2", "body": [{"menu": {"content": [{"label": {"name": "internet_services"}}, {"display": {"message_id": "internet_services"}}, {"item": {"selector": "1", "message_id": "internet_volume", "body": [{"display": {"message_id": "internet_volume_response"}}]}}, {"item": {"selector": "2", "message_id": "transfer_internet_volume", "body": [{"menu": {"content": [{"label": {"name": "gifting"}}, {"display": {"message_id": "gifting"}}, {"item": {"selector": "1", "message_id": "500_24h", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "500_24h_enabled"}}]}}, {"item": {"selector": "2", "message_id": "1000_7d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "1000_7d_enabled"}}]}}, {"item": {"selector": "3", "message_id": "2500_7d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "2500_7d_enabled"}}]}}, {"item": {"selector": "4", "message_id": "7000_30d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "7000_30d_enabled"}}]}}, {"item": {"selector": "5", "message_id": "next_page", "body": [{"menu": {"content": [{"display": {"message_id": "gifting"}}, {"item": {"selector": "5", "message_id": "9000_30d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "9000_30d_enabled"}}]}}, {"item": {"selector": "6", "message_id": "15000_30d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "15000_30d_enabled"}}]}}, {"item": {"selector": "7", "message_id": "20000_30d", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "20000_30d_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "gifting"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_services"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "3", "message_id": "wifi_box_offers", "body": [{"menu": {"content": [{"display": {"message_id": "wifi_box_offers"}}, {"item": {"selector": "1", "message_id": "40000_30d", "body": [{"display": {"message_id": "40000_30d_enabled"}}]}}, {"item": {"selector": "2", "message_id": "60000_30d", "body": [{"display": {"message_id": "60000_30d_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_services"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"message_id": "lending_data_bundles", "selector": "3", "body": [{"display": {"message_id": "service_unavailable"}}]}}, {"item": {"message_id": "social_networks", "selector": "4", "body": [{"menu": {"content": [{"label": {"name": "social_networks"}}, {"display": {"message_id": "social_networks"}}, {"item": {"message_id": "100_2d", "selector": "1", "body": [{"display": {"message_id": "100_2d_enabled"}}]}}, {"item": {"message_id": "500_5d", "selector": "2", "body": [{"display": {"message_id": "500_5d_enabled"}}]}}, {"item": {"message_id": "1000_25d", "selector": "3", "body": [{"display": {"message_id": "1000_25d_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "internet_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"message_id": "combo_offers", "selector": "2", "body": [{"menu": {"content": [{"label": {"name": "combo_offers"}}, {"display": {"message_id": "combo_offers_short"}}, {"item": {"message_id": "3in1_offers", "selector": "1", "body": [{"menu": {"content": [{"label": {"name": "3in1"}}, {"display": {"message_id": "3in1_offers"}}, {"item": {"selector": "1", "message_id": "50_24h", "body": [{"display": {"message_id": "50_24h_enabled"}}]}}, {"item": {"selector": "2", "message_id": "100_24h", "body": [{"display": {"message_id": "100_24h_enabled"}}]}}, {"item": {"selector": "3", "message_id": "150_24h", "body": [{"display": {"message_id": "150_24h_enabled"}}]}}, {"item": {"selector": "4", "message_id": "200_24h", "body": [{"display": {"message_id": "200_24h_enabled"}}]}}, {"item": {"selector": "5", "message_id": "next_page", "body": [{"menu": {"content": [{"display": {"message_id": "3in1_more"}}, {"item": {"selector": "5", "message_id": "300_24h", "body": [{"display": {"message_id": "300_24h_enabled"}}]}}, {"item": {"selector": "6", "message_id": "500_24h", "body": [{"display": {"message_id": "500_48h_enabled"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "3in1"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "3in1_offers"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "3", "message_id": "offers_and_services", "body": [{"menu": {"content": [{"label": {"name": "offers_and_services"}}, {"display": {"message_id": "offers_and_services"}}, {"item": {"selector": "1", "message_id": "lending_credit", "body": [{"display": {"message_id": "dial_130"}}]}}, {"item": {"selector": "2", "message_id": "call_me", "body": [{"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}]}}, {"item": {"selector": "3", "message_id": "friends_and_family", "body": [{"display": {"message_id": "dial_107"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "4", "message_id": "my_account", "body": [{"menu": {"content": [{"label": {"name": "my_account"}}, {"display": {"message_id": "my_account"}}, {"item": {"selector": "1", "message_id": "recharge", "body": [{"menu": {"content": [{"display": {"message_id": "recharge"}}, {"item": {"selector": "1", "message_id": "recharge_myself", "body": [{"ask": {"message_id": "enter_refill_code", "variable": "v4"}}, {"display": {"message_id": "recharge_successful"}}]}}, {"item": {"selector": "2", "message_id": "recharge_3d_number", "body": [{"ask": {"message_id": "enter_refill_code", "variable": "v4"}}, {"ask": {"message_id": "pls_enter_3d_number", "variable": "MSISDNB"}}, {"display": {"message_id": "recharge_successful_bnumber"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "my_account"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "2", "message_id": "my_number", "body": [{"display": {"message_id": "your_number_is"}}]}}, {"item": {"selector": "3", "message_id": "voice_mail", "body": [{"display": {"message_id": "dial_220"}}]}}, {"item": {"selector": "77", "message_id": "main", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "99", "message_id": "back", "body": [{"goto": {"target": "main_menu"}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}]}}, {"item": {"selector": "0", "message_id": "exit", "body": [{"display": {"message_id": "menu_exit"}}]}}]}}, "messages": {"main_menu_header": {"description": "No description", "content": {"eng": "Dear %msisdn%, Pls choose:\n", "fra": "Cher %msisdn%, Choisissez:\n"}}, "mobile_internet": {"description": "No description", "content": {"eng": "Mobile Internet\n", "fra": "PASS Internet\n"}}, "internet_menu": {"description": "No description", "content": {"eng": "Internet menu\n", "fra": "Menu internet\n"}}, "internet_offers": {"description": "No description", "content": {"eng": "Internet offers\n", "fra": "Pass Internet\n"}}, "day": {"description": "No description", "content": {"eng": "Day\n", "fra": "Jour\n"}}, "100_mb_24h": {"description": "No description", "content": {"eng": "100 F/8 MB validity 24 hours\n", "fra": "100 F pour 8 Mo valables 24 heures\n"}}, "100_mb_24h_enabled": {"description": "No description", "content": {"eng": "100 MB in 24 hours enabled\n", "fra": "100 Mo activés en 24 heures\n"}}, "200_mb_24h": {"description": "No description", "content": {"eng": "200 F/20 MB validity 24 hours\n", "fra": "200 F pour 20 Mo valables 24 heures\n"}}, "200_mb_24h_enabled": {"description": "No description", "content": {"eng": "200 MB in 24 hours enabled\n", "fra": "200 Mo activés en 24 heures\n"}}, "500_mb_24h": {"description": "No description", "content": {"eng": "500 F/50 MB validity 24 hours\n", "fra": "500 F pour 50 Mo valables 24 heures\n"}}, "500_mb_24h_enabled": {"description": "No description", "content": {"eng": "500 MB in 24 hours enabled\n", "fra": "500 Mo activés en 24 heures\n"}}, "main": {"description": "No description", "content": {"eng": "Main\n", "fra": "Accueil\n"}}, "back": {"description": "No description", "content": {"eng": "Back\n", "fra": "Précédent\n"}}, "exit": {"description": "No description", "content": {"eng": "Exit\n", "fra": "Sortir\n"}}, "menu_exit": {"description": "No description", "content": {"eng": "<PERSON><PERSON> exit.\n", "fra": "<PERSON><PERSON> termin<PERSON>\n"}}, "weekly": {"description": "No description", "content": {"eng": "Weekly\n", "fra": "<PERSON><PERSON><PERSON>\n"}}, "week_1000": {"description": "No description", "content": {"eng": "1000 F/250 MB validity 7 days\n", "fra": "1000 F pour 250 Mo valables 7 jours\n"}}, "week_1000_enabled": {"description": "No description", "content": {"eng": "250 MB in 7 days enabled\n", "fra": "250 Mo activés en 7 jours\n"}}, "week_1500": {"description": "No description", "content": {"eng": "1500 F/500 MB validity 7 days\n", "fra": "1500 F pour 500 Mo valables 7 jours\n"}}, "week_1500_enabled": {"description": "No description", "content": {"eng": "500 MB in 7 days enabled\n", "fra": "500 Mo activés en 7 jours\n"}}, "week_2500": {"description": "No description", "content": {"eng": "2500 F/1 GB validity 7 days\n", "fra": "2500 F pour 1 Go valables 7 jours\n"}}, "week_2500_enabled": {"description": "No description", "content": {"eng": "1 GB in 7 days enabled\n", "fra": "1 Go activés en 7 jours\n"}}, "monthly": {"description": "No description", "content": {"eng": "Monthly\n", "fra": "<PERSON><PERSON>\n"}}, "month_7000": {"description": "No description", "content": {"eng": "7000 F/3.5 GB validity 1 month\n", "fra": "7000 F pour 3.5 Go valables 1 mois\n"}}, "month_7000_enabled": {"description": "No description", "content": {"eng": "3.5 GB for 1 month enabled\n", "fra": "3,5 Go pour 1 mois activé\n"}}, "month_9000": {"description": "No description", "content": {"eng": "9000 F/7 GB validity 1 month\n", "fra": "9000 F pour 7 Go valables 1 mois\n"}}, "month_9000_enabled": {"description": "No description", "content": {"eng": "7 GB for 1 month enabled\n", "fra": "7 Go pour 1 mois activé\n"}}, "month_15000": {"description": "No description", "content": {"eng": "15000 F/12 GB validity 1 month\n", "fra": "15000 F pour 12 Go valables 1 mois\n"}}, "month_15000_enabled": {"description": "No description", "content": {"eng": "12 GB for 1 month enabled\n", "fra": "12 Go pour 1 mois activé\n"}}, "month_20000": {"description": "No description", "content": {"eng": "20000 F/16 GB validity 1 month\n", "fra": "20000 F pour 16 Go valables 1 mois\n"}}, "month_20000_enabled": {"description": "No description", "content": {"eng": "16 GB for 1 month enabled\n", "fra": "16 Go pour 1 mois activé\n"}}, "nightly": {"description": "No description", "content": {"eng": "Nightly\n", "fra": "Nuit\n"}}, "night_200": {"description": "No description", "content": {"eng": "200 F/200 MB validity between 01H - 05H59\n", "fra": "200 F pour 200 Mo valables entre 00H et 05H59\n"}}, "night_200_enabled": {"description": "No description", "content": {"eng": "200 MB between 01H - 05H59 enabled\n", "fra": "200 Mo activés entre 01H et 05H59\n"}}, "night_500": {"description": "No description", "content": {"eng": "500 F/1 GB validity between 01H - 05H59\n", "fra": "500 F pour 1 Go valables entre 00H et 05H59\n"}}, "night_500_enabled": {"description": "No description", "content": {"eng": "500 MB between 01H - 05H59 enabled\n", "fra": "500 Mo activés entre 01H et 05H59\n"}}, "weekend": {"description": "No description", "content": {"eng": "Week end\n", "fra": "Week end\n"}}, "weekend_250": {"description": "No description", "content": {"eng": "250 F/100 MB validity Saturday & Sunday\n", "fra": "250 F pour 100 Mo valables Samedi et Dimanche\n"}}, "weekend_250_enabled": {"description": "No description", "content": {"eng": "100 MB validity Saturday & Sunday enabled\n", "fra": "Validité 100 Mo activée le samedi et le dimanche\n"}}, "weekend_1500": {"description": "No description", "content": {"eng": "1500 F/2 GB validity Saturday & Sunday\n", "fra": "1500 F pour 2 Go valables Samedi et Dimanche\n"}}, "weekend_1500_enabled": {"description": "No description", "content": {"eng": "2 GB validity Saturday & Sunday enabled\n", "fra": "Validité 2 Go activée le samedi et le dimanche\n"}}, "internet_services": {"description": "No description", "content": {"eng": "Internet services\n", "fra": "Services Internet\n"}}, "internet_volume": {"description": "No description", "content": {"eng": "Internet volume\n", "fra": "Solde Internet\n"}}, "internet_volume_response": {"description": "No description", "content": {"eng": "You have used 4 GB of data\n", "fra": "Vous avez utilisé 4 Go de donnéest\n"}}, "transfer_internet_volume": {"description": "No description", "content": {"eng": "Transfer Internet volume\n", "fra": "Transfert Internet\n"}}, "gifting": {"description": "No description", "content": {"eng": "Gifting\n", "fra": "<PERSON><PERSON>\n"}}, "pls_enter_3d_number": {"description": "No description", "content": {"eng": "Please enter the third number\n", "fra": "Saisis le numero tiers\n"}}, "500_24h": {"description": "No description", "content": {"eng": "500F/50MB (24H)\n", "fra": "500F pour 50Mo (24H)\n"}}, "500_24h_enabled": {"description": "No description", "content": {"eng": "50 MB (24H) for %MSISDNB% enabled\n", "fra": "50 Mo (24H) pour %MSISDNB% activé\n"}}, "1000_7d": {"description": "No description", "content": {"eng": "1000F/250MB (7 days)\n", "fra": "1000F pour 250Mo (7Jrs)\n"}}, "1000_7d_enabled": {"description": "No description", "content": {"eng": "250 MB (7 days) for %MSISDNB% enabled\n", "fra": "250 Mo (7 jours) pour %MSISDNB% activé\n"}}, "2500_7d": {"description": "No description", "content": {"eng": "2500F/1GB (7 days)\n", "fra": "2500F pour 1Go (7Jrs)\n"}}, "2500_7d_enabled": {"description": "No description", "content": {"eng": "1 GB (7 days) for %MSISDNB% enabled\n", "fra": "1 Go (7 jours) pour %MSISDNB% activé\n"}}, "7000_30d": {"description": "No description", "content": {"eng": "7000F/3.5GB (30 days)\n", "fra": "7000F pour 3.5Go (30Jrs)\n"}}, "7000_30d_enabled": {"description": "No description", "content": {"eng": "3.5 GB (30 days) for %MSISDNB% enabled\n", "fra": "3.5 Go (30 jours) pour %MSISDNB% activé\n"}}, "next_page": {"description": "No description", "content": {"eng": "Next page\n", "fra": "Suite\n"}}, "9000_30d": {"description": "No description", "content": {"eng": "9000F/7GB (30 days)\n", "fra": "9000F pour 7Go (30Jrs)\n"}}, "9000_30d_enabled": {"description": "No description", "content": {"eng": "7 GB (30 days) for %MSISDNB% enabled\n", "fra": "7 Go (30 jours) pour %MSISDNB% activé\n"}}, "15000_30d": {"description": "No description", "content": {"eng": "15000F/12GB (30 days)\n", "fra": "15000F pour 12Go (30Jrs)\n"}}, "15000_30d_enabled": {"description": "No description", "content": {"eng": "12 GB (30 days) for %MSISDNB% enabled\n", "fra": "12 Go (30 jours) pour %MSISDNB% activé\n"}}, "20000_30d": {"description": "No description", "content": {"eng": "20000F/16GB (30 days)\n", "fra": "20000F pour 16Go (30Jrs)\n"}}, "20000_30d_enabled": {"description": "No description", "content": {"eng": "16 GB (30 days) for %MSISDNB% enabled\n", "fra": "16 Go (30 jours) pour %MSISDNB% activé\n"}}, "wifi_box_offers": {"description": "No description", "content": {"eng": "Wifi Box offers\n", "fra": "Box Wifi\n"}}, "40000_30d": {"description": "No description", "content": {"eng": "40 000F/32 GB (30 days)\n", "fra": "40 000F pour 32 Go (30Jours)\n"}}, "40000_30d_enabled": {"description": "No description", "content": {"eng": "40 GB (30 days) enabled\n", "fra": "40 Go (30 jours) activé\n"}}, "60000_30d": {"description": "No description", "content": {"eng": "60 000F/50 GB (30 days)\n", "fra": "60 000F pour 50 Go (30Jours)\n"}}, "60000_30d_enabled": {"description": "No description", "content": {"eng": "60 GB (30 days) enabled\n", "fra": "60 Go (30 jours) activé\n"}}, "lending_data_bundles": {"description": "No description", "content": {"eng": "Lending data bundles\n", "fra": "Internet Secours\n"}}, "service_unavailable": {"description": "No description", "content": {"eng": "Service unavailable\n", "fra": "Service indisponible\n"}}, "social_networks": {"description": "No description", "content": {"eng": "Social networks\n", "fra": "<PERSON><PERSON><PERSON>\n"}}, "100_2d": {"description": "No description", "content": {"eng": "100F CFA = 5 MB (2 days)\n", "fra": "100F CFA = 5 Mo (2 Jours)\n"}}, "100_2d_enabled": {"description": "No description", "content": {"eng": "5 MB (2 days) enabled\n", "fra": "5 Mo (2 jours) activé\n"}}, "500_5d": {"description": "No description", "content": {"eng": "500F CFA = 30 MB (5 days)\n", "fra": "500F CFA = 30 Mo (5 Jours)\n"}}, "500_5d_enabled": {"description": "No description", "content": {"eng": "30 MB (5 days) enabled\n", "fra": "30 Mo (5 jours) activé\n"}}, "1000_25d": {"description": "No description", "content": {"eng": "1000F CFA = 150 MB (15 days)\n", "fra": "1000F CFA = 150 Mo (15 Jours)\n"}}, "1000_25d_enabled": {"description": "No description", "content": {"eng": "150 MB (25 days) enabled\n", "fra": "150 Mo (25 jours) activé\n"}}, "combo_offers": {"description": "No description", "content": {"eng": "Combo offers (voice, sms, data)\n", "fra": "Forfait (voix, sms, data)\n"}}, "combo_offers_short": {"description": "No description", "content": {"eng": "Combo offers\n", "fra": "Forfait\n"}}, "3in1_offers": {"description": "No description", "content": {"eng": "3in1 offers\n", "fra": "Offres Trois en Un\n"}}, "3in1": {"description": "No description", "content": {"eng": "3in1\n", "fra": "3in1\n"}}, "3in1_more": {"description": "No description", "content": {"eng": "3in1 more\n", "fra": "3in1 plus\n"}}, "50_24h": {"description": "No description", "content": {"eng": "50F=3 Min+3 Sms(24H)\n", "fra": "50F=3 Min+3 Sms(24H)\n"}}, "50_24h_enabled": {"description": "No description", "content": {"eng": "3 Min+3 Sms(24H) enabled\n", "fra": "3 Min+3 Sms(24H) activé\n"}}, "100_24h": {"description": "No description", "content": {"eng": "100F=10 Min+10 Sms+3 MB (24H)\n", "fra": "100F=10 Min+10 Sms+3 Mo (24H)\n"}}, "100_24h_enabled": {"description": "No description", "content": {"eng": "10 Min+10 Sms+3 MB (24H) enabled\n", "fra": "10 Min+10 Sms+3 Mo (24H) activé\n"}}, "150_24h": {"description": "No description", "content": {"eng": "150F=15 Min+30 Sms+5 MB (24H)\n", "fra": "150F=15 Min+30 Sms+5 Mo (24H)\n"}}, "150_24h_enabled": {"description": "No description", "content": {"eng": "15 Min+30 Sms+5 MB (24H) enabled\n", "fra": "15 Min+30 Sms+5 Mo (24H) activé\n"}}, "200_24h": {"description": "No description", "content": {"eng": "200F=25 Min+60 Sms+7 MB (24H)\n", "fra": "200F=25 Min+60 Sms+7 Mo (24H)\n"}}, "200_24h_enabled": {"description": "No description", "content": {"eng": "25 Min+60 Sms+7 MB (24H) enabled\n", "fra": "25 Min+60 Sms+7 Mo (24H) activé\n"}}, "300_24h": {"description": "No description", "content": {"eng": "300F=15 MB+25 Min+50 Sms to <PERSON><PERSON>+8 Min All Networks(24H)\n", "fra": "300F=15 Mo+25 Min+50 Sms vers <PERSON><PERSON>+ 8Min <PERSON><PERSON>(24H)\n"}}, "300_24h_enabled": {"description": "No description", "content": {"eng": "15 MB+25 Min+50 Sms to Moov+8 Min All Networks(24H) enabled\n", "fra": "15 Mo+25 Min+50 Sms vers <PERSON><PERSON>+ 8<PERSON><PERSON> <PERSON>(24H) activé\n"}}, "500_48h": {"description": "No description", "content": {"eng": "500F =30 MB+50 Min+ 100 Sms to <PERSON><PERSON>+10 Min All Networks(48H)\n", "fra": "500F =80 Mo+50 Min+100 Sms vers <PERSON><PERSON>+10 Min <PERSON><PERSON>(48H)\n"}}, "500_48h_enabled": {"description": "No description", "content": {"eng": "30 MB+50 Min+ 100 Sms to Moov+10 Min All Networks(48H) enabled\n", "fra": "80 Mo+50 Min+100 Sms vers <PERSON><PERSON>+10 Min <PERSON><PERSON>(48H) activé\n"}}, "moov_international": {"description": "No description", "content": {"eng": "<PERSON>ov International\n", "fra": "<PERSON>ov International\n"}}, "700_int": {"description": "No description", "content": {"eng": "700F=10 Min to International\n", "fra": "700F=10 Min vers International\n"}}, "offers_and_services": {"description": "No description", "content": {"eng": "Offers & Services\n", "fra": "Offers & Services\n"}}, "lending_credit": {"description": "No description", "content": {"eng": "Lending credit\n", "fra": "SosCredit\n"}}, "dial_130": {"description": "No description", "content": {"eng": "Dial *130*100# or *130*150#\n", "fra": "Tapez *130*100# ou *130*150#\n"}}, "call_me": {"description": "No description", "content": {"eng": "Call me\n", "fra": "<PERSON><PERSON><PERSON> moi\n"}}, "friends_and_family": {"description": "No description", "content": {"eng": "Friends and family(FaF)\n", "fra": "Amis et famille (FaF)\n"}}, "dial_107": {"description": "No description", "content": {"eng": "Dial *107#\n", "fra": "Tapez *107#\n"}}, "my_account": {"description": "No description", "content": {"eng": "My account\n", "fra": "Mon compte\n"}}, "recharge": {"description": "No description", "content": {"eng": "Recharge\n", "fra": "Rechargement\n"}}, "recharge_myself": {"description": "No description", "content": {"eng": "To recharge myself\n", "fra": "Pour moi-même\n"}}, "enter_refill_code": {"description": "No description", "content": {"eng": "Enter the code Refill Coupon\n", "fra": "Saisir le code Coupon de Recharge\n"}}, "recharge_3d_number": {"description": "No description", "content": {"eng": "To recharge third number\n", "fra": "Pour un autre numéro\n"}}, "enter_number": {"description": "No description", "content": {"eng": "Enter the number\n", "fra": "<PERSON><PERSON> le numéro\n"}}, "my_number": {"description": "No description", "content": {"eng": "My number\n", "fra": "Mon numéro\n"}}, "your_number_is": {"description": "No description", "content": {"eng": "Your number is : %msisdn%\n", "fra": "Votre numéro est : %msisdn%\n"}}, "voice_mail": {"description": "No description", "content": {"eng": "Voice Mail\n", "fra": "Boite vocale\n"}}, "dial_220": {"description": "No description", "content": {"eng": "Dial 220\n", "fra": "Appelez le 220\n"}}, "recharge_successful": {"description": "No description", "content": {"eng": "Recharge successful\n", "fra": "<PERSON><PERSON><PERSON> r<PERSON>\n"}}, "recharge_successful_bnumber": {"description": "No description", "content": {"eng": "Recharge successful for %MSISDNB%\n", "fra": "Recharge réussie pour %MSISDNB%\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}}