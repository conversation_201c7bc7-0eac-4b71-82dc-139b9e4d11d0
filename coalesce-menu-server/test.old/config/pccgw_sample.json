{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"component": {"name": "pccgw", "config": {"pcc": {"url": "http://127.0.0.1:4021/RPC2"}, "air": {"url": "http://127.0.0.1:10011/Air", "nodetype": "EXT", "hostname": "hxc", "nai": 2}, "langs": ["eng", "eng", "fra"]}}}, {"if": {"operator": "eq", "variable": "overwrite", "rhs_constants": [true], "body": [{"display": {"message_id": "status"}}], "else": [{"display": {"message_id": "response"}}]}}]}}, "messages": {"response": {"description": "", "content": {"eng": "%result%", "fra": "%result%"}}, "status": {"description": "", "content": {"eng": "You have $sum([DA97,DA104])$ and mobile data $data(UC10/3)$", "fra": "Vous avez $sum([DA97,DA104])$ et données mobiles $data(UC10)$"}}, "mb": {"description": "", "content": {"eng": "MB", "fra": "MO"}}, "gb": {"description": "", "content": {"eng": "GB", "fra": "GO"}}, "kb": {"description": "", "content": {"eng": "KB", "fra": "KO"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}, "ussd_matches": []}