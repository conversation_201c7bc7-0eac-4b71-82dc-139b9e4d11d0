{"menu_code": {"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"message_id": "main_menu_header"}}, {"item": {"message_id": "square", "selector": "1", "body": [{"assign": {"variable": "v1", "value": 5}}, {"component": {"name": "test_component"}}, {"display": {"message_id": "result"}}]}}, {"item": {"message_id": "exit", "selector": "9", "body": [{"display": {"message_id": "goodbye"}}]}}]}}, "messages": {"main_menu_header": {"description": "Main menu header line", "content": {"eng": "TEST MENU\n"}}, "square": {"description": "", "content": {"eng": "Square it\n"}}, "result": {"description": "", "content": {"eng": "%v1% on square is %result%\n"}}, "goodbye": {"description": "Goodbye entry", "content": {"eng": "Goodbye\n"}}, "exit": {"description": "Exit entry", "content": {"eng": "Exit Weather App\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}, "ussd_matches": [{"request": "*2#", "body": [{"goto": {"target": "usage_check_menu"}}]}]}, "components": {"test_component": "async function call(vars, ctx) { vars.result = vars.v1 * vars.v1; } module.exports = { call }"}}