{"root_menu": "main_menu", "menus": {"main_menu": {"content": [{"display": {"message_id": "main_menu_header"}}, {"item": {"message_id": "usage_checks", "selector": "1", "body": [{"goto": {"target": "usage_check_menu"}}]}}, {"item": {"message_id": "enable_roaming", "selector": "2", "body": [{"ask": {"message_id": "enable_roaming_confirmation", "variable": "v1"}}, {"if": {"operator": "eq", "variable": "v1", "rhs_constants": ["Y", "y", "yes", "1"], "body": [{"display": {"message_id": "roaming_enabled"}}, {"end": {}}, {"display": {"message_id": "roaming_enabled"}}], "else": [{"display": {"message_id": "roaming_not_enabled"}}]}}, {"end": {}}]}}, {"item": {"message_id": "balance_check_entry", "selector": "3", "body": [{"goto": {"target": "balance_check_label"}}]}}, {"item": {"message_id": "crazy_label", "selector": "4", "body": [{"menu": {"content": [{"display": {"message_id": "crazy_menu_header"}}, {"item": {"message_id": "usage_checks", "selector": "1", "body": [{"goto": {"target": "usage_check_menu"}}]}}]}}]}}, {"item": {"message_id": "exit", "selector": "9", "body": [{"assign": {"variable": "v2", "value": "v2_value"}}, {"assign": {"variable": "v3", "value": "v3_value"}}, {"assign": {"variable": "v4", "value": "v4_value"}}, {"clear": {"variables": ["v4"]}}, {"assign_null": {"variables": ["v5_null"]}}, {"end": {}}]}}]}, "usage_check_menu": {"content": [{"display": {"message_id": "usage_check_menu_header"}}, {"item": {"message_id": "balance_check_entry", "selector": "1", "body": [{"label": {"name": "balance_check_label"}}, {"assign_null": {"variables": ["balance", "currency"]}}, {"component": {"name": "get_balance"}}, {"display": {"message_id": "balance_check_response"}}, {"clear": {"variables": ["balance", "currency"]}}, {"end": {}}]}}, {"item": {"message_id": "data_check_entry", "selector": "2", "body": [{"display": {"message_id": "data_check_response"}}, {"end": {}}]}}, {"item": {"message_id": "back", "selector": "3", "body": [{"goto": {"target": "main_menu"}}]}}]}}, "messages": {"usage_check_menu_header": {"description": "Usage check header line", "content": {"eng": "eng: Please select from the following menu:\n", "fra": "fra: Please select from the following menu:\n"}}, "balance_check_entry": {"description": "Balance check entry", "content": {"eng": "eng: Check Balance\n", "fra": "fra: Check Balance\n"}}, "balance_check_response": {"description": "Balance check response", "content": {"eng": "eng: Your balance is %balance% %currency%\n", "fra": "fra: Your balance is %balance% %currency%\n"}}, "data_check_entry": {"description": "Data check entry", "content": {"eng": "eng: Check Data\n", "fra": "fra: Check Data\n"}}, "data_check_response": {"description": "Data check response", "content": {"eng": "eng: You have 100MB left\n", "fra": "fra: You have 100MB left\n"}}, "main_menu_header": {"description": "Main menu header line", "content": {"eng": "eng: Welcome!\n", "fra": "fra: Welcome!\n"}}, "usage_checks": {"description": "Usage checks entry", "content": {"eng": "eng: Check usage\n", "fra": "fra: Check usage\n"}}, "exit": {"description": "Exit entry", "content": {"eng": "eng: Exit menu\n", "fra": "fra: Exit menu\n"}}, "back": {"description": "Back entry", "content": {"eng": "eng: Back\n", "fra": "fra: Back\n"}}, "enable_roaming": {"description": "Enable roaming entry", "content": {"eng": "eng: Enable roaming\n", "fra": "fra: Enable roaming\n"}}, "enable_roaming_confirmation": {"description": "Enable roaming confirmation", "content": {"eng": "eng: Are you sure you want to enable roaming?", "fra": "fra: Are you sure you want to enable roaming?"}}, "roaming_enabled": {"description": "Roaming enabled", "content": {"eng": "eng: You have successfully enabled roaming!", "fra": "fra: You have successfully enabled roaming!"}}, "roaming_not_enabled": {"description": "Roaming not enabled", "content": {"eng": "eng: You have not enabled roaming, please try again!", "fra": "fra: You have not enabled roaming, please try again!"}}, "crazy_label": {"description": "Crazy stuff", "content": {"eng": "Do some crazy stuff\n", "fra": "fra: Do some crazy stuff\n"}}, "crazy_menu_header": {"description": "Crazy stuff", "content": {"eng": "Crazy menu!\n", "fra": "fra: <PERSON>\n"}}}, "global_messages": {"invalid_option": {"description": "Subscriber has selected an invalid option", "content": {"eng": "eng: Invalid option selected, please try again", "fra": "fra: Invalid option selected, please try again"}}, "no_ussd_match": {"description": "No USSD request has matched", "content": {"eng": "eng: Invalid request, please try again", "fra": "fra: Invalid request, please try again"}}}, "ussd_matches": [{"request": "*2#", "body": [{"goto": {"target": "usage_check_menu"}}]}]}