#!/usr/bin/env node

const request = require('request')
	, readlineSync = require('readline-sync')
	//, random = require('random')
	, URL = require('url')
	, hux = require('@coalesce/xmlrpc')
	;

if (require.main === module) {
	
	const yargs = require('yargs')
		.usage('Usage: $0 [options]')
		.option( 'h', { alias: 'host',   demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
		.option( 'p', { alias: 'port',   demand: false, describe: 'Host port', type: 'number', default: 8080 } )
		.option( 'm', { alias: 'msisdn', demand: true,  describe: 'MSISDN', type: 'string' } )
		.option( 's', { alias: 'ussd',   demand: false,  describe: 'USSD string', type: 'string', default: '*123#' } )
		.option( "t", { alias: "tls",    demand: false, describe: "Enable TLS", type: "boolean", default: false } )
		.help('help')
		.example('$0 -h http://127.0.0.1:4021 -m27711234567 -s*353*1#')
		.example('$0 -h https://csys-products-coalesce-studio-coalesce-menu-server-staging.coalescelab.com -m10003 -s*353#')
		.wrap(null)
		.version('1.0.0')
		.epilog('\251 2019. Concurrent Systems');

	const argv = yargs.argv;
	const [ussdCode, ussdRequest] = getUSSD(argv.ussd);
	const url = getUrl(argv.host);
	const msisdn = argv.msisdn;
	const ussd = argv.ussd;

	sendUssdRequest(url, msisdn, ussd, handleUserInput);
}

function usage() {
	yargs.showHelp();
}

function getUSSD(ussd) {
	const r = ussd.match(/^\*([0-9]+)(.*?)#$/);
	return r.slice(1);
}

function sendRequest(args) {
	return new Promise((resolve, reject) => {
		request.post(args, (error, response, data) => {
			if (error) 
				reject(error);
			else {
				//console.log('DATA: ' + data);
				resolve(data);
			}
		});
	});
}

function randomTransactionId() {
	return Math.floor(100000000 + Math.random() * 900000000);
}

function getUrl(host) {
	const url = URL.parse(host);

	if (url.host)
		return url.path == '/' ? url.href + 'RPC2' : url.href;

	
	return 'http://' + argv.host + ':' + String(argv.port) + '/RPC2';	
}

async function sendUssdRequest(url, msisdn, ussd, userCallback) {
	const [ussdCode, ussdRequest] = getUSSD(ussd);

	let tid = randomTransactionId();
	let USSDRequestString = ussdRequest + '#';
	let response = 'false';

	while (true) {

		const params = {
			TransactionId: String(tid++),
			TransactionTime: new Date(),
			MSISDN: msisdn,
			USSDServiceCode: ussdCode,
			USSDRequestString,
			response
		};

		const body = hux.encodeRequest('handleUSSDRequest', params);

		const xml = await sendRequest({ url, body, headers: {
			'Content-Type': 'text/xml',
			'SOAPAction': '\"\"'
			}});

		//console.debug('XML response:\n', xml);
		const r = hux.parseResponse(xml);
		const text = r.USSDResponseString;

		if (text)
			console.log(text);

		if (r.action && r.action == 'request') {
			//console.debug('RESPONSE CODE: ', r.ResponseCode);
			USSDRequestString = userCallback(r.ResponseCode);
			response = 'true';
			continue;
		}
		
		break;
	}

	console.log('Bye !!!');
}

function handleUserInput() {
	return readlineSync.question('Select item: ');
}

module.exports = {
	sendUssdRequest
}