const config = require('./sample_menu.json')
const readlineSync = require('readline-sync')

const rootMenuLabel = config.menus.root_menu
const rootMenu = config.menus[rootMenuLabel]
let currentMenu = rootMenu
const lang = 'eng'
const sessionVariables = {}

function displayMenu(menu) {
    let response = ''
    for (const i of menu.content) {
        if (i.display) {
            response += getMessageContent(i.display.message_id)
        } else if (i.item) {
            response += (i.item.selector + '. ' + getMessageContent(i.item.message_id))
        }
    }

    return response
}

function getMenuSelectors(menu) {
    const selectors = []
    for (const i of menu.content) {
        if (i.item) {
            selectors.push(i.item.selector)
        }
    }

    return selectors
}

function getMessageContent(id) {
    return config.messages[id].content[lang]
}

function getGlobalMessageContent(id) {
    return config.global_messages[id].content[lang]
}

function getMenu(label) {
    return config.menus[label]
}

function getUssdRequest(req) {
    const parts = splitUssdRequest(req)
    return parts.slice(1)
}

function splitUssdRequest(req) {
    req = req.substring(1, req.length - 1)
    const parts = req.split('*')
    return parts
}

function collectLabels(config) {
}

function handleBody(body, index) {
    let output = ''
    for (const j of body) {
        if (j.display) {
            output += getMessageContent(j.display.message_id)
        } else if (j.goto) {
            currentMenu = getMenu(j.goto.target)
            handleMenuSelection(currentMenu)
        } else if (j.end) {
            return output
        } else if (j.ask) {
            output = getMessageContent(j.ask.message_id)
            const userInput = readlineSync.question(output)
            output = ''
            sessionVariables[j.ask.variable] = userInput
        } else if (j.assign) {
            sessionVariables[j.assign.variable] = j.assign.value
        } else if (j.clear) {
            for (const v of j.clear.variables) {
                delete sessionVariables[v]
            }
        } else if (j.assign_null) {
            for (const v of j.assign_null.variables) {
                sessionVariables[v] = undefined
            }
        } else if (j.if) {
            const variableName = j.if.variable
            const variable = sessionVariables[variableName]
            const op = j.if.operator
            const rhsConstants = j.if.rhs_constants
            let result = false

            switch(op) {
                case 'eq':
                    if (rhsConstants.includes(variable)) {
                        result = true
                    }
                    break
            }

            if (result) {
                output += handleBody(j.if.body)
            } else {
                output += handleBody(j.if.else)
            }
        } else if (j.label) {

        }
    }

    return output
}

function handleMenuSelection(menu) {
    let output = displayMenu(menu)
    const userInput = readlineSync.question(output)

    output = ''

    const menuSelectors = getMenuSelectors(menu)    
    if (menuSelectors.includes(userInput)) {
        for (const i of menu.content) {
            if (i.item && i.item.selector == userInput) {
                output += handleBody(i.item.body)
                break
            }
        }
        console.log(output)
    } else {
        console.log(config.global_messages.invalid_option.content[lang])
    }
}

const ussdRequestParts = getUssdRequest(process.argv[2])

if (ussdRequestParts.length == 0) {
    handleMenuSelection(rootMenu)
} else {
    let matchBody = null
    for (const match of config.ussd_matches) {
        const matchParts = splitUssdRequest(match.request)
        if (matchParts.length == ussdRequestParts.length) {
            matchBody = match.body
            break
        }
    }

    if (matchBody) {
    } else {
        console.log(getGlobalMessageContent('no_ussd_match'))
    }
}

console.log('Session variables: ', sessionVariables)