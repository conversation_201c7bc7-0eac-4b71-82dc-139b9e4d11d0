#!/usr/bin/env node

const request = require('request')
	, URL = require('url')
	, hux = require('@coalesce/xmlrpc')
	;
	
const yargs = require('yargs')
	.usage('Usage: $0 [options]')
	.command('details', 'Calls GetAccountDetails method')
	.command('balance', 'Calls GetBalanceAndDate method')
	.command('update', 'Calls UpdateBalanceAndDate method')
	.command('offer', 'Calls UpdateOffer method')
	.command('threshold', 'Calls GetUsageThresholdsAndCounters method')
	.command('refill', 'Calls Refill method')
	.command('periodic', 'Calls AddPeriodicAccountManagementData method')
	
	.option( 'h', { alias: 'host',   demand: false, describe: 'Host address', type: 'string', default: '127.0.0.1' } )
	.option( 'p', { alias: 'port',   demand: false, describe: 'Host port', type: 'number', default: 10011 } )
	.option( 'm', { alias: 'msisdn', demand: true,  describe: 'MSISDN', type: 'string' } )
	//.option( 's', { alias: 'ussd',   demand: false,  describe: 'USSD string', type: 'string', default: '*123#' } )
	//.option( "t", { alias: "tls",    demand: false, describe: "Enable TLS", type: "boolean", default: false } )
	.help('help')
	.example('$0 -h 127.0.0.1 -p4021 -m212345678 -s*353#')
	.wrap(null)
	.version('1.0.0')
	.epilog('\251 2019. Concurrent Systems');

const argv = yargs.argv;

function usage() {
	yargs.showHelp();
}



function sendRequest(args) {
	return new Promise((resolve, reject) => {
		request.post(args, (error, response, data) => {
			if (error) 
				reject(error);
			else {
				//console.log('DATA: ' + data);
				resolve(data);
			}
		});
	});
}

function getUrl() {
	const url = URL.parse(argv.host);

	if (url.host)
		return url.path == '/' ? url.href + 'RPC2' : url.href;

	
	return 'http://' + argv.host + ':' + String(argv.port) + '/RPC2';	
}

// UpdateBalanceAndDate
//const body = '<?xml version="1.0" encoding="UTF-8"?><methodCall><methodName>UpdateBalanceAndDate</methodName><params><param><value><struct><member><name>originNodeType</name><value><string>EXT</string></value></member><member><name>originHostName</name><value><string>hxc</string></value></member><member><name>originTransactionID</name><value><string>3</string></value></member><member><name>originTimeStamp</name><value><dateTime.iso8601>20190306T10:21:32+0000</dateTime.iso8601></value></member><member><name>subscriberNumberNAI</name><value><i4>2</i4></value></member><member><name>subscriberNumber</name><value><string>*********</string></value></member><member><name>transactionCurrency</name><value><string>CFR</string></value></member><member><name>dedicatedAccountUpdateInformation</name><value><array><data><value><struct><member><name>dedicatedAccountID</name><value><i4>1</i4></value></member><member><name>dedicatedAccountValueNew</name><value><string>500</string></value></member><member><name>expiryDate</name><value><dateTime.iso8601>20190309T10:21:32+0000</dateTime.iso8601></value></member><member><name>dedicatedAccountUnitType</name><value><i4>0</i4></value></member></struct></value></data></array></value></member><member><name>externalData1</name><value><string>1,2,27*********,,DEFAULT-USSD</string></value></member><member><name>externalData2</name><value><string>2,2,27*********,,DEFAULT-USSD</string></value></member></struct></value></param></params></methodCall>';

// GetBalanceAndDate
//const body = '<?xml version="1.0" encoding="UTF-8"?><methodCall><methodName>GetBalanceAndDate</methodName><params><param><value><struct><member><name>originNodeType</name><value><string>EXT</string></value></member><member><name>originHostName</name><value><string>hxc</string></value></member><member><name>originTransactionID</name><value><string>4</string></value></member><member><name>originTimeStamp</name><value><dateTime.iso8601>20190306T10:21:32+0000</dateTime.iso8601></value></member><member><name>subscriberNumberNAI</name><value><i4>2</i4></value></member><member><name>subscriberNumber</name><value><string>*********</string></value></member></struct></value></param></params></methodCall>';

async function start() {
	const url = `http://${argv.host}:${argv.port}/Air`;

	let req;
	let method;
	const cmd = argv._[0];
	switch (cmd) {
		case 'details':
		method = 'GetAccountDetails';
		req = getAccountDetails(argv.msisdn);
		break;

		case 'balance':
		method = 'GetBalanceAndDate';
		req = getBalanceAndDate(argv.msisdn);
		break;

		case 'update':
		method = 'UpdateBalanceAndDate';
		req = updateBalanceAndDate(argv.msisdn);
		break;

		case 'offer':
		method = 'UpdateOffer';
		req = updateOffer(argv.msisdn);
		break;

		case 'threshold':
		method = 'GetUsageThresholdsAndCounters';
		req = getUsageThresholdsAndCounters(argv.msisdn);
		break;

		case 'refill':
		method = 'Refill';
		req = refill(argv.msisdn);
		break;

		case 'periodic':
		method = 'AddPeriodicAccountManagementData';
		req = addPeriodicAccountManagementData(argv.msisdn);
		break;
	}

	const body = hux.encodeRequest(method, req);
	console.debug('Sending request\n', body);
	const xml = await sendRequest({ url, body, headers: {
		'Content-Type': 'text/xml',
		'SOAPAction': '\"\"'
		}});

	console.debug('Response\n', xml);
	const res = hux.parseResponse(xml);
	console.log(res);
	console.log('Bye !!!');
}

start();

function getAccountDetails(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
		, requestedInformationFlags: { requestMasterAccountBalanceFlag: true }
		, requestPamInformationFlag: true
	};
}

function getBalanceAndDate(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
	};
}

function updateBalanceAndDate(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
		, transactionCurrency: 'EUR'
		, dedicatedAccountUpdateInformation: [ 
			{
				dedicatedAccountID: 1
				, dedicatedAccountValueNew: 500
				, expiryDate: date(10)
				, dedicatedAccountUnitType: 0
			} ]
		, externalData1: `1,2,${msisdn},,DEFAULT-USSD`
		, externalData2: `2,2,${msisdn},,DEFAULT-USSD`
	};
}

function updateOffer(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
		, offerID: 1
		, expiryDate: date(10)
		, offerType: 0
	};
}

function getUsageThresholdsAndCounters(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
	};
}

function refill(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
		, transactionAmount: 20
		, transactionCurrency: 'EUR'
		, refillProfileID: 666
		, externalData1: 's=$SUBSCRIBER_CELL_GROUP_CODE$'
		, externalData2: 'a=$AGENT_CELL_GROUP_CODE$'
		, externalData3: 'g=$CELL_GROUP_CODE$'
		, externalData4: 'c=$SUBSCRIBER_CELL_ID$'
		, messageCapabilityFlag: { accountActivationFlag: true }

	};
}

function addPeriodicAccountManagementData(msisdn) {
	const subscriberNumberNAI = Number(msisdn[0]);
	const subscriberNumber = msisdn.substr(1);

	return {
		originNodeType: 'EXT'
		, originHostName: 'hxc'
		, originTransactionID: String(getTransactionId())
		, originTimeStamp: new Date()
		, subscriberNumberNAI
		, subscriberNumber
		, originOperatorID: 78
		, pamInformationList: [ {
			pamServiceID: 1
			, pamClassID: 2
			, scheduleID: 3
		} ]
	};
}

function getTransactionId() {
	return Math.floor(Math.random() * 1000000) + *********;
}

function date(days) {
	return new Date(Date.now() + days * 60 * 60 * 24 * 1000);
}