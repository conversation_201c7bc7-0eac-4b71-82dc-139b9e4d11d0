const mockAxios = require("axios");
const parser = require("xml2json");
const mm = require("@coalesce/mobileMoney");

const ctx = {
  connectors: {
    mobileMoney: {
      mm: {
        protocol: "https",
        hostname: "localhost",
        port: "4004",
        url: "/",
        authType: "basic",
        authDetails: {
          key: "tlc12345tlc12345tlc12345tlc12345",
          username: "myusername",
          password: "mypassword",
        },
      },
    },
  },
};

const baseResponse = {
  "soap:Envelope": {
    "soap:Body": {
      "ns2:getBalanceResponse": {
        response: {},
        "xmlns:ns2": "http://api.debit.flooz.com/",
      },
    },
    "xmlns:soap": "http://schemas.xmlsoap.org/soap/envelope/",
  },
};

baseResponse["soap:Envelope"]["soap:Body"]["ns2:getBalanceResponse"].response = {
  $t: JSON.stringify({ status: 0, accounttype: "INTR", balance: [{ walletid: 0, amount: 0 }] }),
};

describe("Test MobileMoney API", () => {
  test("getBalance(...) request without msisdn", async () => {
    expect.assertions(2);
    await mm.getBalance(ctx, null).catch((error) => {
      expect(error.name).toBe("MobileMoneyError");
      expect(error.message).toBe("Missing msisdn in getBalance(...) request");
    });
  });
  test("getBalance(...) request without passing context", async () => {
    expect.assertions(4);
    await mm.getBalance(null, "**********").catch((error) => {
      expect(error.name).toBe("MobileMoneyError");
      expect(error.message).toBe("mobileMoney getBalance method failed");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });
  test("getBalance(...) request with correct parameters - success", async () => {
    expect.assertions(4);
    const msisdn = "01234";

    let xmlResponse = parser.toXml(baseResponse);
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: xmlResponse }));

    const getBalanceResponse = await mm.getBalance(ctx, msisdn);
    console.debug(getBalanceResponse);
    expect(getBalanceResponse.data).toEqual({
      status: 0,
      accounttype: "INTR",
      balance: [{ walletid: 0, amount: 0 }],
    });

    const reqUrl = mockAxios.post.mock.calls[0][0];
    const reqData = mockAxios.post.mock.calls[0][1];
    const jsonReq = parser.toJson(reqData, { object: true });
    const checkReq = jsonReq["soapenv:Envelope"]["soapenv:Body"]["api:getBalance"];
    expect(reqUrl).toEqual("https://localhost:4004/");
    expect(checkReq.msisdn).toEqual("01234");
    expect(checkReq.token).toEqual("7a1FPyjZ4eqAhFIoHpLumttby9GoBPGuwV5AIaB22tc=");
  });
});
