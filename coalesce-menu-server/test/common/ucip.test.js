const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));

process.env.AXIOS_NO_KEEP_ALIVE = true;
process.env.AXIOS_GET_RETRY_COUNT = 0;
const fs = require("fs");
const path = require("path");
const mockAxios = require("axios");
jest.mock("axios");
const ucip = require("@coalesce/ucip");

let defaultCtx = {
  menuState: {},
  request: {
    MSISDN: "123456789",
  },
  connectors: {
    ucip: {
      air: {
        hostname: "localhost",
        url: "/Air",
        port: 10010,
        timeout: 5000,
        auth: {
          username: "",
          password: "",
        },
        subscriberNumberNAI: 1,
        hostIdentity: {
          originHostName: "HxC",
          originNodeType: "HxC",
        },
        /*
        operatorFormats: [
          {
            prefixes: { national: '0', country: '27', international: '' },

          },
        ],
        */
      },
    },
  },
};

defaultCtx.connectors.ucip.air3 = { ...defaultCtx.connectors.ucip.air };

describe("Test 'UCIP' library", () => {
  beforeEach(() => {
    initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints", "{}");
  });

  const XMLRPCFaultResponse = {
    rc1001: fs.readFileSync("./test/common/responses/XMLRPCFault.response.1001.xml"),
  };

  const getBalanceAndDateResponse = {
    rc999: fs.readFileSync("./test/common/responses/getBalanceAndDate.response.999.xml"),
    rc102: fs.readFileSync("./test/common/responses/getBalanceAndDate.response.102.xml"),
    rc100: fs.readFileSync("./test/common/responses/getBalanceAndDate.response.100.xml"),
    rc0: fs.readFileSync("./test/common/responses/getBalanceAndDate.response.0.xml"),
  };

  test("test getBalanceAndDate - Failure - 400: Bad Request", async () => {
    expect.assertions(6);
    try {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(mockAxios.response.http400_BadRequest));
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx);
    } catch (error) {
      console.error("FULL ERROR", error)
      expect(error.name).toBe("UCIPLibraryException");
      expect(error.message).toBe("Problem making callAir(...) request");
      expect(error.innerError.name).toBe("ComponentRequestError");
      expect(error.innerError.message).toBe("== Axios Request Error ==");
      expect(error.innerError.status).toBe(400);
      expect(error.innerError.statusText).toBe("Bad Request");
    }
  });

  test("test getBalanceAndDate - Failure - ECONNRESET", async () => {
    expect.assertions(6);
    try {
      // default axios response is ECONNRESET - no need to mock
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx);
    } catch (error) {
      expect(error.name).toBe("UCIPLibraryException");
      expect(error.message).toBe("Problem making callAir(...) request");
      expect(error.innerError.name).toBe("ComponentRequestError");
      expect(error.innerError.message).toBe("== Axios Request Error ==");
      expect(error.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(error.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("Fails if default AIR not specified", async () => {
    expect.assertions(4);
    try {
      delete defaultCtx.connectors.ucip.air;
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx);
    } catch (error) {
      expect(error.name).toBe("UCIPLibraryException");
      expect(error.message).toBe("Problem making callAir(...) request");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    }
  });

  test("test getBalanceAndDate - Success - responseCode = 0", async () => {
    expect.assertions(4);
    try {
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: String(getBalanceAndDateResponse.rc0) }));

      ucip.setEndpoint("air3", defaultCtx);

      const result = await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx);

      expect(result.responseCode).toEqual(0);
      expect(result.serviceClassCurrent).toEqual(4);
      expect(result.languageIDCurrent).toEqual(2);
      expect(result.accountValue1).toEqual("100");
    } catch (error) {
      console.error(error);
    }
  });

  test("test getBalanceAndDate - Subscriber Not Found - 102", async () => {
    mockAxios.post.mockImplementationOnce(() =>
      Promise.resolve({ headers: {}, data: String(getBalanceAndDateResponse.rc102) })
    );
    const result = await ucip
      .getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx)
      .then((result) => {
        expect(result.responseCode).toEqual(102);
        console.info("success", result);
      })
      .catch((err) => {
        console.error("error", err);
        expect("failure").toBe("success");
      });
    console.debug("THE RESULT", result);
  });

  test("test getBalanceAndDate - Other Error - 100", async () => {
    mockAxios.post.mockImplementationOnce(() =>
      Promise.resolve({ headers: {}, data: String(getBalanceAndDateResponse.rc100) })
    );
    try {
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx)
    } catch(err) {
      expect(err.innerError.name).toBe("UCIPProtocolException");
      expect(err.innerError.responseCode).toBe(100);
    }
  });

  test("test getBalanceAndDate - XMLRPC Fault - 1001 - Mandatory field missing.", async () => {
    mockAxios.post.mockImplementationOnce(() =>
      Promise.resolve({ headers: {}, data: String(XMLRPCFaultResponse.rc1001) })
    );
    try {
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx)
    } catch(err) {
      expect(err.name).toBe("XMLRPCFaultException");
      expect(err.faultCode).toBe(1001);
      expect(err.faultString).toContain("Mandatory field missing");
    }
  });

  test("test getBalanceAndDate - Other Error No Retry - 999", async () => {
    mockAxios.post.mockImplementationOnce(() =>
      Promise.resolve({ headers: {}, data: String(getBalanceAndDateResponse.rc999) })
    );
    try {
      await ucip.getBalanceAndDate({ subscriberNumber: "********" }, defaultCtx)
    } catch(err) {
      expect(err.innerError.name).toBe("UCIPProtocolException");
      expect(err.innerError.responseCode).toBe(999);
    }
  });


  test("test getAccountDetails request without passing context", async () => {
    try {
      // without context
      await ucip.getAccountDetails({ subscriberNumber: "********" }, null);
    } catch (err) {
      expect(err.name).toBe('UCIPLibraryException');
      expect(err.innerError.name).toBe('EndpointException');
      expect(err.innerError.message).toBe("Invalid context provided");
    }
  });
});
