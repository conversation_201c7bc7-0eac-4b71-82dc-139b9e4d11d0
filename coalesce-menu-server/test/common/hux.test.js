process.env.AXIOS_NO_KEEP_ALIVE = true;
process.env.AXIOS_GET_RETRY_COUNT = 0;
const hux = require("@coalesce/hux");

describe("Test 'HuX' Library, Make sure <PERSON>rro<PERSON> and <PERSON>hrows work as expected", () => {
  test(`hux.sendUSSD() request with no params`, async () => {
    expect.assertions(2);
    await hux.sendUSSD().catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Empty USSD String for HuX Request");
    });
  });
  test(`hux.sendUSSD('*')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid USSD String for HuX Request");
    });
  });
  test(`hux.sendUSSD('#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });
  test(`hux.sendUSSD('*123**#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123**#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid USSD String for HuX Request");
    });
  });

  test(`hux.sendUSSD('*123*999*#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123*999*#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid USSD String for HuX Request");
    });
  });
  test(`hux.sendUSSD('*123*999*#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123*999*#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid USSD String for HuX Request");
    });
  });
  test(`hux.sendUSSD('*123*0*testing#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123*0*testing#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid USSD String for HuX Request");
    });
  });
  test(`hux.sendUSSD('*123*555*000#')`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123*555*000#").catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });
  test(`hux.sendUSSD("*123*555*000#", { new: true, connectors: {} })`, async () => {
    expect.assertions(2);
    await hux.sendUSSD("*123*555*000#", { new: true, connectors: {} }).catch((error) => {
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    });
  });
  test(`hux.sendUSSD("*123*555*000#", exampleContext)`, async () => {
    expect.assertions(4);
    const exampleContext = {
      new: true,
      request: { MSISDN: "123456789", TransactionId: "abcdef" },
      connectors: { hux: { smartshop: { hostname: "localhost", port: 3000, url: "", timeout: 3000 } } },
    };

    hux.setEndpoint("non-existant", exampleContext);

    await hux.sendUSSD("*123*555*000#", exampleContext).catch((error) => {
      expect(error.name).toBe("HuxError");
      expect(error.message).toBe("hux.sendUSSD(...) Failed");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    });
  });
});
