const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));
jest.mock("axios");
const mockAxios = require("axios");
const crediverse = require("@coalesce/crediverse");
const { CREDIVERSE_MAX_POOL_SIZE_ETCD_KEY, ENDPOINTS_ETCD_KEY } = require("@coalesce/env");
const { getActiveConnection } = require("@coalesce/crediverse/poolController");

const defaultCtx = {
  menuState: {},
  request: {
    MSISDN: "70000295",
  },
  connectors: {
    crediverse: {
      myecds: {
        hostname: "localhost",
        url: "/api",
        port: 9085,
        timeout: 5000,
        auth: {
          client_id: "ecdsclient",
          client_secret: "7a361a9c87824855a9cfba63129730af",
          username: "coalesce",
          password: "vqxhdz&%",
        },
      },
    },
  },
};

const expiresInTwoSecondsToken = {
  // We have an intentional BUFFER of 20 seconds before expiry for the renewal action
  //  Therefore... If the 'expires_in' is 22 seconds, the token validation considers it invalid in 2 seconds
  expires_in: 22,
  access_token: "987-123456",
};

const originalConsoleError = console.error;

console.error = jest.fn((...args) => {
  originalConsoleError(...args);
});

describe("Test 'Crediverse' library", () => {
  afterEach(() => {
    // restores mock request/response values to 0
    //  so you can assume in each test, request/responses start at 0
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    expect.assertions(0);
    try {
      initializeHotConfig();
      const etcdConnectorData = JSON.stringify({
        crediverse: { myecds: [{ ...defaultCtx.connectors.crediverse.myecds }] },
      });
      getHotConfig().setKeyValue(ENDPOINTS_ETCD_KEY, etcdConnectorData);
      getHotConfig().setKeyValue(CREDIVERSE_MAX_POOL_SIZE_ETCD_KEY, 1);
    } catch (e) {
      console.debug("test", e);
      expect("failure").toBe("success");
    }
  });

  test("Missing request data in ctx", async () => {
    expect.assertions(1);
    try {
      await crediverse.getAgentDetails();
    } catch (e) {
      expect(e.message).toBe("Missing request data in the session ctx");
    }
  });

  test("setEndpoint(...) method not called", async () => {
    expect.assertions(4);
    try {
      // Before making a details request, an oauth request is made by the connection pool.
      //  First mock simulates the feedback to the OAuth request (and thus when invalid data is provided, it throws)
      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(e.name).toBe("CrediverseLibraryException");
      expect(e.message).toBe("Failed to Get Agent details");
      expect(e.innerError.name).toBe("EndpointException");
      expect(e.innerError.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    }
  });

  test("OAuth returns ECONNRESET", async () => {
    expect.assertions(7);
    try {
      crediverse.setEndpoint("myecds", defaultCtx);

      // default axios mock is ECONNRESET ... no need to mock it
      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(console.error).toHaveBeenCalledWith(expect.stringMatching(/EXCEPTION.*ECONNREFUSED/))
      expect(e.name).toBe("CrediverseConnectionPoolException");
      expect(e.message).toBe("Failed to login to Crediverse API");
      expect(e.innerError.name).toBe("ComponentRequestError");
      expect(e.innerError.message).toBe("== Axios Request Error ==");
      expect(e.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(e.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("Invalid OAuth response", async () => {
    expect.assertions(2);
    try {
      // Before making a details request, an oauth request is made by the connection pool.
      //  Simulate feedback to the OAuth request (and thus when invalid data is provided, it throws)
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ status: 200, data: {} }));

      crediverse.setEndpoint("myecds", defaultCtx);

      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(e.name).toBe("CrediverseConnectionPoolException");
      expect(e.message).toBe("Invalid/Missing token or expiry information in oauth response, did oauth fail?");
    }
  });

  test("Test 'account locked' response (HTTP 400 Bad Request)", async () => {
    expect.assertions(3);
    try {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(mockAxios.response.http400_BadRequest));
      crediverse.setEndpoint("myecds", defaultCtx);
      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(console.error).toHaveBeenCalledWith(expect.stringMatching(/EXCEPTION.*400/))
      expect(e.name).toBe("CrediverseConnectionPoolException");
      expect(e.message).toBe(
        "Crediverse /oauth/token request failed. " + //
          "Unable to authenticate, your account may be locked out?"
      );
    }
  });

  test("Test 'Unathorized' response (HTTP 401 Unauthorized)", async () => {
    expect.assertions(2);
    try {
      mockAxios.post.mockImplementationOnce(() => Promise.reject(mockAxios.response.http401_Unauthorized));
      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(e.name).toBe("CrediverseConnectionPoolException");
      expect(e.message).toBe(
        "Crediverse /oauth/token request failed. " + //
          "Please check the credentials provided?"
      );
    }
  });

  test("Bearer is valid but agent details gives ECONNRESET", async () => {
    expect.assertions(6);
    try {
      mockAxios.post.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: expiresInTwoSecondsToken,
        })
      );

      /**
       * NOTE: The "GET" part of this test will fail ... but the OAuth succeeded.
       *  That means the NEXT test will not need to authenticate, it will get the auth from THIS test
       */

      await crediverse.getAgentDetails(defaultCtx);
    } catch (e) {
      expect(e.name).toBe("CrediverseLibraryException");
      expect(e.message).toBe("Failed to Get Agent details");
      expect(e.innerError.name).toBe("ComponentRequestError");
      expect(e.innerError.message).toBe("== Axios Request Error ==");
      expect(e.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(e.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("Bearer is valid and agent details provided", async () => {
    expect.assertions(3);
    try {
      const agentData = {
        status: 200,
        statusText: "OK",
        agentId: "58",
        state: "DEACTIVATED",
        tier: { name: "eCabine", type: "R" },
      };
      /**
       * IMPORTANT ... the previous test Succesfully performed OAuth ... BUT tested the failure of the GET request
       *  Because of this... the OAuth is STILL VALID ... and does not need to be mocked here.
       *  We will not perform an OAuth request .... the connection pool will provide a valid connection back.
       */
      mockAxios.get.mockImplementationOnce(() => Promise.reject(mockAxios.response.http400_BadRequest));
      await crediverse.getAgentDetails(defaultCtx).catch(() => {
        expect(console.error).toHaveBeenCalledWith(expect.stringMatching(/EXCEPTION.*400:Bad/))
        return Promise.resolve();
      })

      mockAxios.get.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: agentData,
        })
      );

      response = await crediverse.getAgentDetails(defaultCtx);

      expect(response).toEqual(agentData);
      expect(mockAxios.get.mock.calls[0][1].headers.Authorization).toBe("Bearer 987-123456");
    } catch (e) {
      console.error(e);
      expect("failure").toBe("success");
    }
  });

  /*
  test("Updating pool connection size.", async () => {
    expect.assertions(2);
    let poolSize = getHotConfig().getKeyValue(CREDIVERSE_MAX_POOL_SIZE_ETCD_KEY);
    expect(poolSize).toBe(1);

    // mock out debug
    const origConsoleDebug = console.info;
    console.info = jest.fn();

    // Due to the simulation of a config update - the POOL re-creates the login connection.
    //  Thus we must simulate it's response so next tests can re-use this token
    mockAxios.post.mockImplementationOnce(() =>
      Promise.resolve({
        status: 200,
        data: expiresInTwoSecondsToken,
      })
    );

    getHotConfig().simulateConfigUpdate(CREDIVERSE_MAX_POOL_SIZE_ETCD_KEY, 5);
    await new Promise((r) => setTimeout(r, 1000)); // Give time for the ASYNC call that updates the pool to run in the background --- THERE IS NOT WAY TO AWAIT IT :/

    // If the pool is correctly drained and re-created with the new pool size ... it will Log the following on DEBUG level :)
    expect(console.info).toHaveBeenCalledWith("Crediverse Library connection pool size changed to: 5");

    // restore unmocked version
    console.info = origConsoleDebug;
  });
  */

  test("Expired Token makes a new OAuth request", async () => {
    expect.assertions(4);
    try {
      const agentData = {
        status: 200,
        statusText: "OK",
        agentId: "101",
        state: "ACTIVE",
        tier: { name: "eCabine", type: "R" },
      };

      // First we ensure the existing token provides a VALID response
      mockAxios.get.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: agentData,
        })
      );

      crediverse.setEndpoint("myecds", defaultCtx);

      let response = await crediverse.getAgentDetails(defaultCtx);
      expect(response).toEqual(agentData);
      let requestData = mockAxios.get.mock.calls[0][1];
      expect(requestData.headers.Authorization).toEqual("Bearer 987-123456");

      // ... Then, we wait 3 seconds to invalidate the token in the connection pool
      // NOTE: The token being used expires in 2 seconds --- see LAST test
      await new Promise((r) => setTimeout(r, 3000));

      //
      // Lastly, we must MOCK a new token for the next request, because the pool will make a new authentication request
      //
      mockAxios.post.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: { expires_in: 500, access_token: "555-222-333" },
        })
      );
      mockAxios.get.mockImplementationOnce(() =>
        Promise.resolve({
          status: 200,
          data: agentData,
        })
      );

      response = await crediverse.getAgentDetails(defaultCtx);
      expect(response).toEqual(agentData);
      requestData = mockAxios.get.mock.calls[1][1];
      expect(requestData.headers.Authorization).toEqual("Bearer 555-222-333");
    } catch (e) {
      console.error(e);
      expect("failure").toBe("success");
    }
  });

  test("Validate that a new token is required when HotConfig is updated", async () => {
    expect.assertions(5);

    defaultCtx.connectors.crediverse.myecds.url = "/apiii";
    defaultCtx.connectors.crediverse.myecds.timeout = 8000;

    const connectionManager = await getActiveConnection();
    expect(connectionManager._poolOptions.acquireTimeoutMillis).toEqual(5200);

    const connectorData = JSON.stringify({ crediverse: { myecds: [{ ...defaultCtx.connectors.crediverse.myecds }] } });
    getHotConfig().simulateConfigUpdate("coalesce/cs/development/menu/preprod/endpoints", connectorData);

    await new Promise((r) => setTimeout(r, 1000));

    const completeAxios404Response = mockAxios.response.http404_NotFound;
    const returned404Response = completeAxios404Response.response;

    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ status: 200, data: expiresInTwoSecondsToken }));
    mockAxios.get.mockImplementationOnce(() => Promise.reject(completeAxios404Response));

    const response = await crediverse.getAgentDetails(defaultCtx);

    expect(connectionManager._poolOptions.acquireTimeoutMillis).toEqual(8200);

    expect(console.error).toHaveBeenCalledWith(expect.stringMatching(/EXCEPTION.*404/))

    expect(response.code).toEqual(404);
    const requestUrl = mockAxios.get.mock.calls[0][0];
    expect(requestUrl).toEqual(
      "http://localhost:9085" + `${defaultCtx.connectors.crediverse.myecds.url}` + "/service/agent/70000295/details"
    );
  });
});
