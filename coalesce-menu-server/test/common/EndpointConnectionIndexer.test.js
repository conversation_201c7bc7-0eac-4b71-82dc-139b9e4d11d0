const { EndpointManager } = require("@coalesce/utils");

const endpointConnectionIndexer = require("@coalesce/EndpointConnectionIndexer");

describe("EndpointConnectionIndexer", () => {
  test("flattens the connector endpoint connections to the indicated one", () => {
    const connectors = {
      crediverse: {
        air: [
          {
            hostname: "air5",
          },
        ],
      },
      ucip: {
        air: [
          {
            hostname: "air1",
          },
          {
            hostname: "air2",
          },
        ],
        air_endpoint: [
          {
            hostname: "air_endpoint1",
          },
        ],
        genet_air: [
          {
            hostname: "air_endpoint1",
          },
        ],
      },
    };

    const expectedFlattenedConnectors = {
      crediverse: {
        air: {
          hostname: "air5",
        },
      },
      ucip: {
        air: {
          hostname: "air2",
        },
        air_endpoint: {
          hostname: "air_endpoint1",
        },
        genet_air: {
          hostname: "air_endpoint1",
        },
      },
    };

    endpointConnectionIndexer.createConnectionIndex("ucip.air", 2);
    endpointConnectionIndexer.createConnectionIndex("ucip.air_endpoint", 1);
    endpointConnectionIndexer.createConnectionIndex("ucip.genet_air", 1);

    endpointConnectionIndexer.cycleConnectionIndex("ucip.air");

    let flattenedConnectors = endpointConnectionIndexer.flattenConnectionArray(connectors);

    expect(flattenedConnectors).toEqual(expectedFlattenedConnectors);
  });

  test("Returns 0 when no endpoint by that name was set up", () => {
    let index = endpointConnectionIndexer.getConnectionIndex("test");
    expect(index).toBe(0);
  });

  test("Returns 0 when endpoint connection index is set and not yet cycled.", () => {
    endpointConnectionIndexer.createConnectionIndex("test1", 3);
    expect(endpointConnectionIndexer.getConnectionIndex("test1")).toBe(0);
  });

  test("cycles throug 0 to max index and back to 0 when cycled repeatedly.", () => {
    endpointConnectionIndexer.createConnectionIndex("test2", 3);
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(0);
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(0);
    endpointConnectionIndexer.cycleConnectionIndex("test2");
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(1);
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(1);
    endpointConnectionIndexer.cycleConnectionIndex("test2");
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(2);
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(2);
    endpointConnectionIndexer.cycleConnectionIndex("test2");
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(0);
    expect(endpointConnectionIndexer.getConnectionIndex("test2")).toBe(0);
  });

  test("calling create on a already created index resets it", () => {
    endpointConnectionIndexer.createConnectionIndex("test3", 3);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
    endpointConnectionIndexer.cycleConnectionIndex("test3");
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(1);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(1);

    endpointConnectionIndexer.createConnectionIndex("test3", 3);

    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
    endpointConnectionIndexer.cycleConnectionIndex("test3");
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(1);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(1);
    endpointConnectionIndexer.cycleConnectionIndex("test3");
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(2);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(2);
    endpointConnectionIndexer.cycleConnectionIndex("test3");
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
    expect(endpointConnectionIndexer.getConnectionIndex("test3")).toBe(0);
  });
});
