jest.mock("axios");
const mockAxios = require("axios");

const parser = require("xml2json");
const fs = require("fs");
const { advance, statusCheck } = require("@coalesce/comzaAcs");

const ctx = {
  connectors: {
    comzaAcs: {
      acs: {
        protocol: "http",
        hostname: "localhost",
        url: "/RequestHandlerAPI",
        port: "8087",
        timeout: "3000",
        authType: "basic",
        authDetails: {
          token: "ewtyetyutiolipu[",
          username: "admin",
          password: "admin",
          key: "jfkklgu;hi'jk",
        },
        description: "ACS Endpoint",
        countryCode: "21",
      },
    },
  },
};

describe("Test comzaAcs API", () => {
  afterEach(() => {
    // restores mock request/response values to 0
    //  so you can assume in each test, request/responses start at 0
    jest.clearAllMocks();
  });

  test("statusCheck(...) request with invalid params", async () => {
    expect.assertions(2);
    try {
      // without msisdn
      const params = {
        ["ApplicationId/ShortCode"]: 10005,
        TransactionId: "10324342343243434",
      };
      await statusCheck(ctx, params);
    } catch (err) {
      expect(err.message).toBe(`ComzaAcs 'callComzaAcs' method for (statusCheck request) failed`);
      expect(err.innerError.message).toBe("Missing Mandatory comzaAcs Parameter");
    }
  });
  test("statusCheck(...) request without passing context", async () => {
    expect.assertions(4);
    try {
      const params = {
        Msisdn: "250728508538",
        ["ApplicationId/ShortCode"]: 10005,
        TransactionId: "10324342343243434",
      };
      await statusCheck(null, params); // context is null
    } catch (err) {
      expect(err.message).toBe(`ComzaAcs 'callComzaAcs' method for (statusCheck request) failed`);
      expect(err.innerError.message).toBe(`ComzaAcs sendRequest method for (statusCheck request) failed`);
      expect(err.innerError.innerError.name).toBe("EndpointException");
      expect(err.innerError.innerError.message).toBe("Invalid context provided");
    }
  });
  test("statusCheck(...) request with correct parameters -- success", async () => {
    expect.assertions(8);
    try {
      const statusCheckXmlResponse = fs.readFileSync(`${__dirname}/responses/comzAcs.statusCheck.response.xml`, "utf8");
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: statusCheckXmlResponse }));

      const statusCheckParams = {
        Msisdn: "250728508538",
        ["ApplicationId/ShortCode"]: 10005,
        TransactionId: "10324342343243434",
      };

      const statusCheckResponse = await statusCheck(ctx, statusCheckParams);

      expect(statusCheckResponse.Msisdn).toBe(statusCheckParams.Msisdn);
      expect(statusCheckResponse.TransactionId).toBe(statusCheckParams.TransactionId);
      expect(statusCheckResponse.ApplicationId).toBe(statusCheckParams["ApplicationId/ShortCode"]);
      expect(statusCheckResponse.ResponseCode).toBe(1);
      expect(statusCheckResponse.ResponseText).toBe("You are eligible for the service.");
      expect(statusCheckResponse["Allowed Services"]).toEqual([10001, 10002, 10003, 10004, 10005, 10006]);

      const reqUrl = mockAxios.post.mock.calls[0][0];
      const reqData = mockAxios.post.mock.calls[0][1];
      const jsonReq = parser.toJson(reqData, { object: true });
      const methodName = jsonReq["methodCall"]["methodName"];
      expect(reqUrl).toEqual("http://localhost:8087/RequestHandlerAPI");
      expect(methodName).toEqual("statusCheck");
    } catch (err) {
      console.debug(err);
      expect("failure").toBe("success");
    }
  });

  test("test advance request with invalid params", async () => {
    expect.assertions(2);
    try {
      // insufficient params
      const params = {
        ["ApplicationId/ShortCode"]: 10005,
        TransactionId: "10324342343243434",
      };
      await advance(ctx, params);
    } catch (err) {
      expect(err.message).toBe(`ComzaAcs 'callComzaAcs' method for (advance request) failed`);
      expect(err.innerError.message).toBe("Missing Mandatory comzaAcs Parameter");
    }
  });
  test("test advance without passing context", async () => {
    expect.assertions(5);
    try {
      const params = {
        Msisdn: "250728508538",
        ["ApplicationId/ShortCode"]: 10005,
        ServiceId: 10006,
        TransactionId: "10324342343243434",
      };

      await advance(null, params); // context is null
    } catch (err) {
      expect(err.name).toBe("ComzaAcsError");
      expect(err.message).toBe(`ComzaAcs 'callComzaAcs' method for (advance request) failed`);
      expect(err.innerError.message).toBe(`ComzaAcs sendRequest method for (advance request) failed`);
      expect(err.innerError.innerError.name).toBe("EndpointException");
      expect(err.innerError.innerError.message).toBe("Invalid context provided");
    }
  });
  test("advance", async () => {
    expect.assertions(7);
    try {
      const xmlResponse = fs.readFileSync(`${__dirname}/responses/comzAcs.advance.response.xml`, "utf8");
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: xmlResponse }));

      const params = {
        Msisdn: "250728508538",
        ["ApplicationId/ShortCode"]: 10005,
        ServiceId: 10006,
        TransactionId: "10324342343243434",
      };

      const response = await advance(ctx, params);

      expect(response.Msisdn).toBe(params.Msisdn);
      expect(response.TransactionId).toBe(params.TransactionId);
      expect(response.ApplicationId).toBe(params["ApplicationId/ShortCode"]);
      expect(response.ResponseCode).toBe(1);
      expect(response.ResponseText).toBe("You have successfully borrowed 500 CFR amount.");

      const reqUrl = mockAxios.post.mock.calls[0][0];
      const reqData = mockAxios.post.mock.calls[0][1];
      const jsonReq = parser.toJson(reqData, { object: true });
      const methodName = jsonReq["methodCall"]["methodName"];
      expect(reqUrl).toEqual("http://localhost:8087/RequestHandlerAPI");
      expect(methodName).toEqual("advance");
    } catch (err) {
      console.debug(err);
      expect("failure").toBe("success");
    }
  });
});
