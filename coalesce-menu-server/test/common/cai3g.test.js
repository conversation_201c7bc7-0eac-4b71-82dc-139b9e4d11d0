const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));
jest.mock("axios");
const mockAxios = require("axios");
const fs = require("fs");
const cai3g = require("@coalesce/cai3g");

const ctx = {
  connectors: {
    cai3g: {
      cai3g: {
        hostname: "localhost",
        url: "/",
        port: "8089",
        timeout: 1000,
        auth: {
          username: "test",
          password: "etst323",
        },
        description: "The Default cai3g Endpoint",
      },
    },
  },
};
describe("Test Cai3G library methods ", () => {
  const LoginXmlResponse = fs.readFileSync(`${__dirname}/responses/cai3g-Login-Response.xml`, "utf8");

  beforeAll(() => {
    initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints", "{}");
  });

  afterEach(() => {
    // restores mock request/response values to 0
    //  so you can assume in each test, request/responses start at 0
    jest.clearAllMocks();
  });

  test("is4GCompatible(...) method without passing context", async () => {
    expect.assertions(4);
    await cai3g.is4GCompatible(null, "1234567").catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed is4GCompatible(...) request");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });
  test("is4GCompatible(...) method without subscriberNumber", async () => {
    expect.assertions(4);
    await cai3g.is4GCompatible(ctx, null).catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed is4GCompatible(...) request");
      expect(error.innerError.name).toBe("Cai3gError");
      expect(error.innerError.message).toBe('Missing required field "subscriberNumber"');
    });
  });

  test("is4GCompatible(...) method with ECONNRESET response", async () => {
    expect.assertions(6);
    // default axios response is ECONNRESET - no need to mock it
    await cai3g.is4GCompatible(ctx, "12334433").catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed is4GCompatible(...) request");
      expect(error.innerError.name).toBe("Cai3gError");
      expect(error.innerError.message).toBe("Failed to login to cai3g server");
      expect(error.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(error.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    });
  });

  test("is4GCompatible(...) method with correct parameters", async () => {
    expect.assertions(1);
    const GetHLRXmlResponse = fs.readFileSync(
      `${__dirname}/responses/cai3g-HLR-getResponse-4g-provisioned.xml`,
      "utf8"
    );
    const GetAUCXmlResponse = fs.readFileSync(`${__dirname}/responses/cai3g-AUC-getResponse-is-4g.xml`, "utf8");
    // IMPORTANT NOTE:
    // Cai3g makes MULTIPLE axios requests to LOCALHOST
    // So we need to mock axios responses in sequence...
    //
    // ALSO. The FIRST request - is a login using the connection pool
    //
    // Subsequently, the method: is4GCompatible(...) makes 2 individual axios calls for an HLR and AUC response...
    // thus we need 3 Total axios responses in sequence for this is4GCompatible method being called.
    //
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: LoginXmlResponse }));
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetAUCXmlResponse }));

    const response = await cai3g.is4GCompatible(ctx, "12334433");
    expect(response).toEqual(true);
  });

  test("is4GCompatible(...) method with failing AUC GET with 302 -- incompatible", async () => {
    const GetHLRXmlResponse = fs.readFileSync(
      `${__dirname}/responses/cai3g-HLR-getResponse-4g-provisioned.xml`,
      "utf8"
    );
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));
    mockAxios.post.mockImplementationOnce(() => Promise.reject(mockAxios.response.http302_Found));

    const response = await cai3g.is4GCompatible(ctx, "12334433").catch((error) => {
      expect(error).toBe("test");
      expect(error).toBe("test");
    });

    expect(response).toBe(false);
  });

  test("is4GProvisioned(...) method without passing context", async () => {
    expect.assertions(4);
    await cai3g.is4GProvisioned(null, "12345343").catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed is4GProvisioned(...) request");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });

  test("is4GProvisioned(...) method without subscriberNumber", async () => {
    expect.assertions(4);
    await cai3g.is4GProvisioned(ctx, null).catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed is4GProvisioned(...) request");
      expect(error.innerError.name).toBe("Cai3gError");
      expect(error.innerError.message).toBe('Missing required field "subscriberNumber"');
    });
  });

  test("is4GProvisioned(...) method with ECONNRESET response", async () => {
    expect.assertions(6);
    try {
      // default axios response is ECONNRESET - no need to mock it
      await cai3g.is4GProvisioned(ctx, "12334433");
    } catch (e) {
      expect(e.name).toBe("Cai3gError");
      expect(e.message).toBe("Failed is4GProvisioned(...) request");
      expect(e.innerError.name).toBe("Cai3gError");
      expect(e.innerError.message).toBe("GET HLRRequest failed");
      expect(e.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(e.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("is4GProvisioned(...) method with correct parameters", async () => {
    expect.assertions(1);
    const GetHLRXmlResponse = fs.readFileSync(
      `${__dirname}/responses/cai3g-HLR-getResponse-4g-provisioned.xml`,
      "utf8"
    );
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));

    const response = await cai3g.is4GProvisioned(ctx, "12334433");
    expect(response).toEqual(true);
  });

  test("provision4G(...) method without passing context", async () => {
    expect.assertions(4);
    await cai3g.provision4G(null, "12345343").catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed provision4G(...) request");
      expect(error.innerError.name).toBe("EndpointException");
      expect(error.innerError.message).toBe("Invalid context provided");
    });
  });
  test("provision4G(...) method without subscriberNumber", async () => {
    expect.assertions(4);
    await cai3g.provision4G(ctx, null).catch((error) => {
      expect(error.name).toBe("Cai3gError");
      expect(error.message).toBe("Failed provision4G(...) request");
      expect(error.innerError.name).toBe("Cai3gError");
      expect(error.innerError.message).toBe('Missing required field "subscriberNumber"');
    });
  });

  test("provision4G(...) method with ECONNRESET HLR response", async () => {
    expect.assertions(8);
    try {
      // default axios response is ECONNRESET - no need to mock it
      await cai3g.provision4G(ctx, "12334433");
    } catch (e) {
      expect(e.name).toBe("Cai3gError");
      expect(e.message).toBe("Failed provision4G(...) request");
      expect(e.innerError.name).toBe("Cai3gError");
      expect(e.innerError.message).toBe("Failed is4GProvisioned(...) request");
      expect(e.innerError.innerError.name).toBe("Cai3gError");
      expect(e.innerError.innerError.message).toBe("GET HLRRequest failed");
      expect(e.innerError.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(e.innerError.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("provision4G(...) method with ECONNRESET AUC response", async () => {
    expect.assertions(6);
    try {
      const GetHLRXmlResponse = fs.readFileSync(
        `${__dirname}/responses/cai3g-HLR-getResponse-not-4g-provisioned.xml`,
        "utf8"
      );
      // first response
      mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));
      // second response is AUC request ... it will default to the axios response: ECONNRESET - no need to mock it

      await cai3g.provision4G(ctx, "12334433");
    } catch (e) {
      expect(e.name).toBe("Cai3gError");
      expect(e.message).toBe("Failed provision4G(...) request");
      expect(e.innerError.name).toBe("Cai3gError");
      expect(e.innerError.message).toBe("SET HLRRequest failed");
      expect(e.innerError.innerError.code).toBe("ECONNREFUSED");
      expect(e.innerError.innerError.message).toBe("connect ECONNREFUSED 127.0.0.1:5000");
    }
  });

  test("provision4G(...) method successful - when already provisioned", async () => {
    const GetHLRXmlResponse = fs.readFileSync(
      `${__dirname}/responses/cai3g-HLR-getResponse-4g-provisioned.xml`,
      "utf8"
    );
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));
    const response = await cai3g.provision4G(ctx, "12334433");
    expect(response).toBe(true);
  });

  test("provision4G(...) method successful - when not provisioned, and successfully set", async () => {
    const GetHLRXmlResponse = fs.readFileSync(
      `${__dirname}/responses/cai3g-HLR-getResponse-not-4g-provisioned.xml`,
      "utf8"
    );
    const SetHLRXmlResponse = fs.readFileSync(`${__dirname}/responses/cai3g-HLR-setResponse-success.xml`, "utf8");

    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));
    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: SetHLRXmlResponse }));

    const response = await cai3g.provision4G(ctx, "12334433");
    expect(response).toBe(true);
  });
});
