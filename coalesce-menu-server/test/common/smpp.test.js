const mockSmpp = require("smpp");
const { sendSms, setEndpoint } = require("@coalesce/smpp");
const componentManager = require("../../server/componentManager");
const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));

const ctx = {
  menuState: {},
  connectors: {
    smpp: {
      smsc: [
        {
          hostname: "localhost",
          protocol: "SMPP",
          port: 2775,
          timeout: "2000",
          encodings: ["ASCII", "LATIN1", "UCS2"],
          auth: {
            system_id: "sysID",
            password: "admin",
          },
          description: "smsc",
        },
      ],
    },
  },
};

describe("Test smsc library", () => {
  beforeEach(() => {
    const key = "coalesce/cs/development/menu/preprod/endpoints";
    initializeHotConfig();
    getHotConfig().setKeyValue(key, JSON.stringify(ctx.connectors));
    componentManager.load();
  });
  afterEach(() => {
    // delete current connection (session)
    // (check the construction of Session class in smsc library)
    // we have to simulate an emitted update event to re-create the current connection
    const key = "coalesce/cs/development/menu/preprod/endpoints";
    getHotConfig().simulateConfigUpdate(key, JSON.stringify(ctx.connectors));
  });

  test("sendSms(...) request without or with invalid setEndpoint(...)", async () => {
    expect.assertions(4);
    try {
      const subscriberNumber = "123123123123";
      const smsText = "Testing smsc library";
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (e) {
      expect(e.name).toBe("EndpointException");
      expect(e.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    }

    try {
      const subscriberNumber = "123123123123";
      const smsText = "Testing smsc library";
      setEndpoint("invalid-endpoint", ctx);
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (e) {
      expect(e.name).toBe("EndpointException");
      expect(e.message).toBe(
        "No endpoint found, did you use setEndpoint(name, ctx) method with a valid endpoint name?"
      );
    }
  });

  test("sendSms - Success", async () => {
    expect.assertions(3);
    // command_status: 0 corresponds success (i,e message would be sent successfully)
    mockSmpp.setResponse({ command_status: 0 });

    const subscriberNumber = "123123123123";
    const smsText = "Testing smsc library";

    try {
      setEndpoint("smsc", ctx);
      const res = await sendSms({ subscriberNumber, smsText }, ctx);
      expect(res.ok).toBe(true);
      expect(res.message).toBe("message sent successfully");
      expect(res.response.command_status).toBe(0);
    } catch (err) {
      console.error("error", err);
      expect("failure").toBe("success");
    }
  });

  test("sendSms(...) with invalid params", async () => {
    expect.assertions(2);
    try {
      await sendSms(null, ctx);
    } catch (err) {
      expect(err.message).toBe("Missing mandatory parameters [subscriberNumber or smsText]");
    }

    try {
      await sendSms({}, ctx);
    } catch (err) {
      expect(err.message).toBe("Missing mandatory parameters [subscriberNumber or smsText]");
    }
  });

  test("sendSms - failure (Bind Failed)", async () => {
    // command_status: 14 corresponds to Bind Failed
    mockSmpp.setResponse({ command_status: 13 });

    const subscriberNumber = "123123123123";
    const smsText = "Testing smsc library";

    try {
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (err) {
      expect(err.message).toBe("SMPP connector failed to connect: Bind Failed");
    }
  });
  test("sendSms - failure (Invalid Password)", async () => {
    // command_status: 14 corresponds to Invalid Password
    mockSmpp.setResponse({ command_status: 14 });

    const subscriberNumber = "123123123123";
    const smsText = "Testing smsc library";

    try {
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (err) {
      expect(err.message).toBe("SMPP connector failed to connect: Invalid Password");
    }
  });
  test("sendSms - failure (Invalid System ID)", async () => {
    // command_status: 14 corresponds to Invalid System ID
    mockSmpp.setResponse({ command_status: 15 });

    const subscriberNumber = "123123123123";
    const smsText = "Testing smsc library";

    try {
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (err) {
      expect(err.message).toBe("SMPP connector failed to connect: Invalid System ID");
    }
  });
  test("sendSms - failure (submit_sm or submit_multi failed)", async () => {
    // command_status: 14 corresponds to Invalid Password
    mockSmpp.setResponse({ command_status: 69 });

    const subscriberNumber = "123123123123";
    const smsText = "Testing smsc library";

    try {
      await sendSms({ subscriberNumber, smsText }, ctx);
    } catch (err) {
      expect(err.message).toBe("SMPP connector failed to connect: submit_sm or submit_multi failed");
    }
  });
});
