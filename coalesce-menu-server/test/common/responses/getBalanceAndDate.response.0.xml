<methodResponse>
  <params>
    <param>
      <value>
        <struct>
          <member>
            <name>responseCode</name>
            <value>
              <i4>0</i4>
            </value>
          </member>
          <member>
            <name>originTransactionID</name>
            <value>
              <string>100773605</string>
            </value>
          </member>
          <member>
            <name>serviceClassCurrent</name>
            <value>
              <i4>4</i4>
            </value>
          </member>
          <member>
            <name>supervisionExpiryDate</name>
            <value>
              <dateTime.iso8601>20210709T09:52:17+0000</dateTime.iso8601>
            </value>
          </member>
          <member>
            <name>creditClearanceDate</name>
            <value>
              <dateTime.iso8601>20210712T09:52:17+0000</dateTime.iso8601>
            </value>
          </member>
          <member>
            <name>serviceRemovalDate</name>
            <value>
              <dateTime.iso8601>20210714T09:52:17+0000</dateTime.iso8601>
            </value>
          </member>
          <member>
            <name>serviceFeeExpiryDate</name>
            <value>
              <dateTime.iso8601>20210710T09:52:17+0000</dateTime.iso8601>
            </value>
          </member>
          <member>
            <name>languageIDCurrent</name>
            <value>
              <i4>2</i4>
            </value>
          </member>
          <member>
            <name>accountFlagsAfter</name>
            <value>
              <struct>
                <member>
                  <name>activationStatusFlag</name>
                  <value>
                    <boolean>1</boolean>
                  </value>
                </member>
                <member>
                  <name>negativeBarringStatusFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>supervisionPeriodWarningActiveFlag</name>
                  <value>
                    <boolean>1</boolean>
                  </value>
                </member>
                <member>
                  <name>serviceFeePeriodWarningActiveFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>supervisionPeriodExpiryFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>serviceFeePeriodExpiryFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
              </struct>
            </value>
          </member>
          <member>
            <name>accountFlagsBefore</name>
            <value>
              <struct>
                <member>
                  <name>activationStatusFlag</name>
                  <value>
                    <boolean>1</boolean>
                  </value>
                </member>
                <member>
                  <name>negativeBarringStatusFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>supervisionPeriodWarningActiveFlag</name>
                  <value>
                    <boolean>1</boolean>
                  </value>
                </member>
                <member>
                  <name>serviceFeePeriodWarningActiveFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>supervisionPeriodExpiryFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
                <member>
                  <name>serviceFeePeriodExpiryFlag</name>
                  <value>
                    <boolean>0</boolean>
                  </value>
                </member>
              </struct>
            </value>
          </member>
          <member>
            <name>currency1</name>
            <value>
              <string>EUR</string>
            </value>
          </member>
          <member>
            <name>accountValue1</name>
            <value>
              <string>100</string>
            </value>
          </member>
          <member>
            <name>currency2</name>
            <value>
              <string>EUR</string>
            </value>
          </member>
          <member>
            <name>accountValue2</name>
            <value>
              <string>100</string>
            </value>
          </member>
          <member>
            <name>dedicatedAccountInformation</name>
            <value>
              <array>
                <data/>
              </array>
            </value>
          </member>
          <member>
            <name>OriginTimeStamp</name>
            <value>
              <dateTime.iso8601>20210702T09:52:17+0000</dateTime.iso8601>
            </value>
          </member>
        </struct>
      </value>
    </param>
  </params>
</methodResponse>