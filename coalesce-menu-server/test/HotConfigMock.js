class HotConfigMock {
  constructor() {
    this._keys = new Map();
    this._updatableKeys = {};
    this._updateCalled = null;
  }

  async createAndWatch(key, defaultValue, updateConfig) {

    let keyValue = this._keys.get(key);
    if (keyValue) {
      updateConfig(keyValue, key);
    } else {
      updateConfig(defaultValue, key);
    }

    this._updatableKeys[key] = { updateConfigCallback: updateConfig };
  }

  simulateConfigUpdate(key, value) {
    const { updateConfigCallback } = this._updatableKeys[key];
    this.setKeyValue(key, value);
    updateConfigCallback(value, key);
  }

  resetUpdateCalled() {
    this._updateCalled = false;
  }

  setKeyValue(key, value) {
    this._keys.set(key, value);
  }

  getKeyValue(key) {
    return this._keys.get(key);
  }
}

let config = null;
module.exports = {
  initializeHotConfig: () => {
    console.info("MOCKED initializeHotConfig");
    config = new HotConfigMock();
  },
  getHotConfig: () => {
    console.info("MOCKED getHotConfig");
    if (config) return config;
    else throw "Call initializeFirst";
  },
};
