const { Server, HandleUSSDRequestException, ExpressRequestException } = require("../../server/Server");
const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
const xmlrpc = require("@coalesce/xmlrpc");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));

const validMembers = [
  "<member><name>TransactionTime</name><value><dateTime.iso8601>20210901T10:08:06</dateTime.iso8601></value></member>",
  "<member><name>MSISDN</name><value><string>27721114444</string></value></member>",
  "<member><name>USSDRequestString</name><value><string>#</string></value></member>",
  "<member><name>response</name><value><string>false</string></value></member>",
  "<member><name>USSDEncoding</name><value><string>GSM0338</string></value></member>",
  "<member><name>USSDServiceCode</name><value><string>111</string></value></member>",
  "<member><name>TransactionId</name><value><string>1000000006</string></value></member>",
];

const validMethodName = "handleUSSDRequest";

const successfulRequest = [
  '<?xml version="1.0" encoding="UTF-8"?>',
  "<methodCall>",
  "<methodName>",
  "__METHOD_NAME__",
  "</methodName>",
  "<params><param><value><struct>",
  "__MEMBERS__", // to be replaced as needed
  "</struct></value></param></params></methodCall>",
];

const structureRequest = (methodName, members, responseOption) => {
  try {
    const { asJson } = responseOption || {};
    let request = successfulRequest
      .map((i) => {
        if (i.match(/__METHOD_NAME__/)) i = methodName;
        if (i.match(/__MEMBERS__/)) i = members.join("");
        return i;
      })
      .join("");

    // WARNING!!!!! if you parse invalid XML .... this will obviously break!
    // This is a HELPER method meant for use in this test environment only .... don't use it for INVALID xml !
    if (asJson) request = xmlrpc.parseRequest(request);

    return request;
  } catch (e) {
    console.error(e);
  }
  return {};
};

const structureRequestAsXML = (methodName, members) => structureRequest(methodName, members);
const structureRequestAsJSON = (methodName, members) => structureRequest(methodName, members, { asJson: true });

const expressResMock = {
  setHeader: jest.fn(),
  send: jest.fn(),
};
const nextMock = jest.fn();

const waitForProcess = (s) => new Promise((r) => setTimeout(r, (s || 1) * 1000));

let server;

describe("Test xmlrpc parsing", () => {
  beforeAll(() => {
    // WARNING ::: Because we mock these ... you won't see the console output on the terminal ....
    // so if you want terminal output ... comment these out
    //    WITH EXCEPTION of console.error ... some tests RELY on that mock
    console.fatal = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
    console.info = jest.fn();
    console.debug = jest.fn();
    console.trace = jest.fn();

    server = new Server();
    server.menuManager = {
      processRequest: jest.fn(),
    };
  });

  beforeEach(() => {
    initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints", "{}");
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  test("Test invalid XML request", () => {
    expect.assertions(6);
    let request = {};
    server._serverMiddlewareXMLParser({ body: request }, expressResMock);

    let faultResponse = expressResMock.send.mock.calls[0][0];
    expect(faultResponse).toMatch("<string>No XML body provided, did you use 'Content-Type: */xml' header?</string>");
    expect(faultResponse).toMatch(
      /<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>/
    );

    request = "";
    server._serverMiddlewareXMLParser({ body: request }, expressResMock);

    faultResponse = expressResMock.send.mock.calls[1][0];
    expect(faultResponse).toMatch("<string>No XML body provided</string>");
    expect(faultResponse).toMatch(
      /<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>/
    );

    request = "<invalid-xml .. <";
    server._serverMiddlewareXMLParser({ body: request }, expressResMock);

    faultResponse = expressResMock.send.mock.calls[2][0];
    expect(faultResponse).toMatch("<string>XML Parsing Failure</string>");
    expect(faultResponse).toMatch(
      /<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>/
    );
  });

  test("Test body missing or invalid required fields", () => {
    expect.assertions(4);
    const invalidMethodName = "invalid-method-name";
    request = structureRequestAsJSON(invalidMethodName, validMembers);
    server.handleRequest({ body: request }, expressResMock);
    faultResponse = expressResMock.send.mock.calls[0][0];
    expect(faultResponse).toMatch(
      `<string>Expected method 'handleUSSDRequest' but got '${invalidMethodName}'</string>`
    );
    expect(faultResponse).toMatch(
      /<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>/
    );

    request = structureRequestAsXML(validMethodName, []);
    server._serverMiddlewareXMLParser({ body: request }, expressResMock, nextMock);
    faultResponse = expressResMock.send.mock.calls[1][0];
    expect(faultResponse).toMatch("<string>Missing methodName or structMembers</string>");
    expect(faultResponse).toMatch(
      /<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>/
    );
  });

  test("Test partly invalid body through xml parser middleware", async () => {
    request = structureRequestAsXML(
      validMethodName,
      validMembers.map((el, idx) => (idx % 2 ? el : undefined))
    );

    server._serverMiddlewareXMLParser({ body: request }, expressResMock, nextMock);
    expect(nextMock).toHaveBeenCalled();
  });

  test("Test partly invalid body", async () => {
    expect.assertions(1);

    request = structureRequestAsJSON(
      validMethodName,
      validMembers.map((el, idx) => (idx % 2 ? el : undefined))
    );

    server.handleRequest({ body: request }, expressResMock);

    let faultResponse = expressResMock.send.mock.calls[0][0];

    const errorText = [
      "Mandatory field TransactionId is missing",
      "Mandatory field TransactionTime is missing",
      "Mandatory field USSDRequestString is missing",
    ].join(", ");

    expect(faultResponse).toMatch(`<string>${errorText}</string>`);
  });

  test("XMLRPC handleUSSDRequest Passed validation - But Menu Server throws", async () => {
    expect.assertions(6);
    //

    const errorText = "Something went wrong";

    const request = structureRequestAsJSON(validMethodName, validMembers);

    server.menuManager.processRequest.mockImplementation(() => Promise.reject(new Error(errorText)));

    server.handleRequest({ body: request }, expressResMock);

    await waitForProcess();

    expect(console.error).toHaveBeenCalledWith(
      "handleUssdRequest",
      new HandleUSSDRequestException(null, "Could not handle USSD Request")
    );

    const faultResponse = expressResMock.send.mock.calls[0][0];

    expect(faultResponse).toMatch(
      /^<methodResponse><fault><value><struct>.*<\/struct><\/value><\/fault><\/methodResponse>$/
    );
    expect(faultResponse).toMatch(/<member><name>TransactionId<\/name><value><string>.*<\/string><\/value><\/member>/);
    expect(faultResponse).toMatch(
      /<member><name>TransactionTime<\/name><value><dateTime\.iso8601>[0-9]{8}T[0-9]{2}:[0-9]{2}:[0-9]{2}\+[0-9]{1,4}<\/dateTime\.iso8601><\/value><\/member>/
    );
    expect(faultResponse).toMatch(/<member><name>faultCode<\/name><value><i4>-?[0-9]+<\/i4><\/value><\/member>/);
    expect(faultResponse).toMatch(
      new RegExp(`<member><name>faultString<\/name><value><string>${errorText}<\/string><\/value><\/member>`)
    );
  });
  test("XMLRPC handleUSSDRequest Passed validation - Test menuManager responses", async () => {
    expect.assertions(7);
    const done = true;
    const text = "ussd response goes here";
    const language = "my-language-here";
    let componentContext = null;

    server.menuManager.processRequest.mockImplementation(() =>
      Promise.resolve([done, text, language, componentContext])
    );

    const request = structureRequestAsJSON(validMethodName, validMembers);

    server.handleRequest({ body: request }, expressResMock);

    await waitForProcess();

    let xmlResponse = expressResMock.send.mock.calls[0][0];

    expect(xmlResponse).toMatch(
      /^<methodResponse><params><param><value><struct>.*<\/struct><\/value><\/param><\/params><\/methodResponse>$/
    );
    expect(xmlResponse).toMatch(
      /<member><name>TransactionId<\/name><value><string>[0-9]+<\/string><\/value><\/member>/
    );
    expect(xmlResponse).toMatch(
      /<member><name>TransactionTime<\/name><value><dateTime\.iso8601>[0-9]{8}T[0-9]{2}:[0-9]{2}:[0-9]{2}\+[0-9]{1,4}<\/dateTime\.iso8601><\/value><\/member>/
    );
    expect(xmlResponse).toMatch(
      new RegExp(`<member><name>USSDResponseString<\/name><value><string>${text}<\/string><\/value>`)
    );
    expect(xmlResponse).toMatch(
      new RegExp(`<member><name>language<\/name><value><string>${language}<\/string><\/value>`)
    );
    expect(xmlResponse).toMatch(new RegExp("<member><name>ResponseCode</name><value><i4>1</i4></value>"));

    componentContext = { respond: true, raw: "<xml>some xml response here</xml>" };

    server.menuManager.processRequest.mockImplementation(() =>
      Promise.resolve([done, text, language, componentContext])
    );

    server.handleRequest({ body: request }, expressResMock);

    await waitForProcess();

    xmlResponse = expressResMock.send.mock.calls[1][0];

    expect(xmlResponse).toMatch(componentContext.raw);
  });
});
