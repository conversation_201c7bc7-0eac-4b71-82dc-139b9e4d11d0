let responseData = {};

const setResponse = (response) => {
  responseData = response;
};

const connection = {
  bind_transmitter: (req, cb) => {
    cb(responseData);
  },

  submit_sm: (req, cb) => {
    cb(responseData); // message sent
  },

  on: (event, cb) => {
    console.info(`event`, event);
  },
  close: () => '',
};

const connect = (req, callback) => {
  return connection;
};
const encodings = {
  detect: (msg) => 'ASCII',
};

module.exports = {
  connect,
  encodings,
  setResponse,
  ESME_RBINDFAIL: 13,
  ESME_RINVPASWD: 14,
  ESME_RINVSYSID: 15,
  ESME_RSUBMITFAIL: 69,
};
