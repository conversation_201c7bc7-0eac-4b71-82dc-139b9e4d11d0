const util = require("util")


let state = {
	req: {},
	res: {},
	action: 1
}

function validStringValue(val) {
	let result = false
	if (typeof val === 'string'  && val.length > 0) result = true
	return result
}

function getRequest(reqst) {
	return state.req
}

function setResponse(resp) {
	state.res = resp
}

function setResponseConnectionRefused() {
	state.action = 2
}

const post = jest.fn((req, callback) => {
	state.req = req.body
	if (state.action === 1)
		callback(null, true, state.res)
	if (state.action === 2) {
		callback({
				address: "127.0.0.1",
				code: "ECONNREFUSED",
				errno: "ECONNREFUSED",
				message: "Error: connect ECONNREFUSED 127.0.0.1:5000",
				port: 5000,
				stack: "Error: connect ECONNREFUSED 127.0.0.1:5000",
				syscall: "connect"
		})
		state.action = 1
	}
});
module.exports.post = post;
module.exports.getRequest = getRequest;
module.exports.setResponse = setResponse;
module.exports.setResponseConnectionRefused = setResponseConnectionRefused;