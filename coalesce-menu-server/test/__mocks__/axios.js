const ERROR_DEFAULTS = {
  address: "127.0.0.1",
  config: { url: "REQUEST URL", method: "REQUEST METHOD", headers: {}, data: "REQUEST DATA" },
  isAxiosError: true,
  port: 5000,
  syscall: "connect",
};

let axios = {
  ETIMEDOUT: {
    ...ERROR_DEFAULTS,
    code: "ETIMEDOUT",
    errno: "ETIMEDOUT",
    message: "connect ETIMEDOUT 127.0.0.1:5000 at ....",
    stack: "Error: connect ETIMEDOUT 127.0.0.1:5000 at ....",
  },
  ECONNRESET: {
    ...ERROR_DEFAULTS,
    code: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    errno: "EC<PERSON><PERSON><PERSON><PERSON>",
    message: "read <PERSON><PERSON><PERSON><PERSON><PERSON>",
    stack: "Error: read <PERSON><PERSON><PERSON><PERSON><PERSON>",
    syscall: "read",
  },
  ECONNREFUSED: {
    ...ERROR_DEFAULTS,
    code: "ECONNREFUSED",
    errno: "ECONNREFUSED",
    message: "connect ECONNREFUSED 127.0.0.1:5000",
    stack: "Error: connect ECONNREFUSED 127.0.0.1:5000",
  },
};

const axiosError = (response) => ({ isAxiosError: true, response });

const genericResponseMocks = {
  http302_Found: axiosError({ status: 302, statusText: "Found" }),
  http400_BadRequest: axiosError({ status: 400, statusText: "Bad Request" }),
  http401_Unauthorized: axiosError({  status: 401, statusText: "Unauthorized" }),
  http404_NotFound: axiosError({ status: 404, statusText: "Not Found" }),
  http500_InternalServerError: axiosError({ status: 500, statusText: "Internal Server Error" }),
  http501_NotImplemented: axiosError({ status: 501, statusText: "Not Implemented" }),
};

axios = {
  ...axios,
  response: { ...genericResponseMocks },
};

// methods to mock
["get", "post", "put", "delete"].forEach((method) => {
  // ECONNREFUSED is the default response if you don't mock one...
  axios[method] = jest.fn(() => Promise.reject(axios.ECONNREFUSED));
});

axios.create = () => axios;

module.exports = axios;
