// __mocks__/etcd3.js

class Etcd3 {
  constructor(options) {
    this.options = options;

    this.ttl;
    this.putKey;
    this.putValue;

    this.leasePutExecInstance = jest.fn();
    this.leaseInstance = {
      put: jest.fn((key) => {
        this.putKey = key;
        return this.leaseInstance;
      }),
      value: jest.fn((value) => {
        this.putValue = value;
        return this.leaseInstance;
      }),
      exec: this.leasePutExecInstance,
      release: jest.fn(() => {
        setTimeout(() => {
          console.debug('deleting keys ... ttl was: ', this.ttl);
          delete this.putKey;
          delete this.putValue;
        }, this.ttl * 1000);
      }),
    };
    this.lease = jest.fn((ttl) => {
      this.ttl = ttl;
      return this.leaseInstance;
    });

    this.getExecInstance = jest.fn(() => Promise.resolve({ count: 0, kvs: [] }));
    this.get = jest.fn((key) => {
      if (this.putKey == key && typeof this.putValue !== 'undefined') {
        this.getExecInstance.mockReturnValueOnce(
          Promise.resolve({
            count: 1,
            kvs: [{ value: Buffer.from(this.putValue) }],
          }),
        );
      }

      return {
        exec: this.getExecInstance,
      };
    });

    this.deleteKey;
    this.deleteExecInstance = jest.fn(() => {
      let deleted = false;
      if (this.deleteKey == this.putKey) {
        delete this.putKey;
        delete this.putValue;
        deleted = true;
      }

      return { deleted };
    });
    this.keyInstance = jest.fn();
    this.deleteInstance = {
      key: jest.fn((key) => {
        this.deleteKey = key;
        return this.deleteInstance;
      }),
      exec: this.deleteExecInstance,
    };
    this.delete = jest.fn(() => this.deleteInstance);
  }
}

module.exports = { Etcd3 };
