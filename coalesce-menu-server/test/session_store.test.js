const Context = require('../server/Context.js');
const SessionStore = require('../server/session_store.js');

jest.mock('etcd3');

const sleep = (seconds) => new Promise((r) => setTimeout(r, seconds * 1000));

describe('SessionStore', () => {
  it('should set and get a session value', async () => {
    const TTL = 3;

    const sessionStore = new SessionStore({ ttl: TTL });
    const context = new Context();

    await sessionStore.set('123456789', context);
    expect(sessionStore.etcd.lease).toHaveBeenCalledTimes(1);
    expect(sessionStore.etcd.leasePutExecInstance).toHaveBeenCalledTimes(1);
    const deleted = (await sessionStore.delete('123456789')).deleted;
    expect(sessionStore.etcd.lease).toHaveBeenCalledTimes(1);
    expect(sessionStore.etcd.deleteExecInstance).toHaveBeenCalledTimes(1);
    expect(deleted).toEqual(true);

    await sessionStore.set('123456789', context);
    expect(sessionStore.etcd.lease).toHaveBeenCalledTimes(2);
    expect(sessionStore.etcd.leasePutExecInstance).toHaveBeenCalledTimes(2);

    let result = await sessionStore.get('invalidKey');
    expect(sessionStore.etcd.get).toHaveBeenCalledTimes(1);
    expect(sessionStore.etcd.getExecInstance).toHaveBeenCalledTimes(1);
    expect(result).toEqual(undefined);

    result = await sessionStore.get('123456789');
    expect(sessionStore.etcd.get).toHaveBeenCalledTimes(2);
    expect(sessionStore.etcd.getExecInstance).toHaveBeenCalledTimes(2);
    expect(result.toString()).toEqual(context.toString());

    await sleep(TTL - 1);

    result = await sessionStore.get('123456789');
    delete result.lastupdate;
    delete context.lastupdate;
    expect(sessionStore.etcd.get).toHaveBeenCalledTimes(3);
    expect(sessionStore.etcd.getExecInstance).toHaveBeenCalledTimes(3);
    expect(result.toString()).toEqual(context.toString());

    await sleep(TTL + 1);

    result = await sessionStore.get('123456789');
    expect(sessionStore.etcd.get).toHaveBeenCalledTimes(4);
    expect(sessionStore.etcd.getExecInstance).toHaveBeenCalledTimes(4);
    expect(result).toEqual(undefined);
  }, 10000);
});
