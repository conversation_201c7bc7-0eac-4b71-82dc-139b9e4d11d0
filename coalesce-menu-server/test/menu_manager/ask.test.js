const MenuManager = require('../../server/menu_manager');

const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));
 
// Ask statement outside of menu item is not supported
// test('Test the ask statement', a()sync () => {
// 	const config = require('./data/askTest1.json');
// 	let menuTest = new MenuManager()
// 	menuTest.load(config, true)
// 	let response = await menuTest.processRequest(request)

// 	expect(response[1]).toContain('Continue?');
// });

describe("Test 'ask' statement", () => {
	beforeEach(()=>{
		initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});

	test('Test the ask statement inside menu item', async () => {
		try{
			const request = {
				"TransactionTime": "2020-04-11T11:11:20.000Z",
				"MSISDN": "96000122",
				"USSDRequestString": "*1#",
				"response": "false",
				"USSDEncoding": "GSM0338",
				"USSDServiceCode": "123",
				"TransactionId": "1000000002"
			};

			const config = require('./data/askTest2.json');
			let menuTest = new MenuManager()
			await menuTest.load(config, true);

			let response = await menuTest.processRequest(request)
			expect(response[1]).toContain('Continue?');

			request.USSDRequestString = 'yes';
			request.response = 'true';

			response = await menuTest.processRequest(request);
			expect(response[1]).toContain('yes');
		} catch (error){
			console.error("UHANDLED:  " ,error);
		}

	});
});

