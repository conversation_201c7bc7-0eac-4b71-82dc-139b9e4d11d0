const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));
jest.mock("axios");

const xmlrpc = require("@coalesce/xmlrpc");
const request = require("axios");

const MenuManager = require("../../server/menu_manager.js");

const realRequest = {
  TransactionTime: "2020-04-11T11:11:20.000Z",
  MSISDN: "096000122",
  USSDRequestString: "#",
  response: "false",
  USSDEncoding: "GSM0338",
  USSDServiceCode: "123",
  TransactionId: "1000000002",
};

const realResponse = xmlrpc.encodeResponse({
  TransactionId: "1000000002",
  TransactionTime: "2020-04-11T11:11:20.000Z",
  USSDEncoding: "GSM0338",
  action: "end",
  ResponseCode: "0",
  USSDResponseString: "Hello World",
});

const realFaultResponse = xmlrpc.encodeFaultResponse({
  TransactionId: "1000000002",
  TransactionTime: "2020-04-11T11:11:20.000Z",
  faultCode: -11,
  faultString: "some error",
});

describe("Test callMenu", () => {
  beforeEach(() => {
    initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints", "{}");
  });
  test("empty", () => {});
  /*
	test('Test calling a remote menu, text response', async () => {
		const config = require('./data/callMenuTest1.json');
		let menuTest = new MenuManager()
		await menuTest.load(config, true)
		request.setResponse({
			data: realResponse,
			url: 'http://localhost:5000/RPC2'
		})
		let response = await menuTest.processRequest(realRequest)
		let checkRequest = request.getRequest()
		let recievedRequest = xmlrpc.parseRequest(checkRequest.data)

		// Verify that the recieved request was correct
		expect(recievedRequest.method).toMatch('handleUSSDRequest')
		expect(recievedRequest.members.TransactionId).toBeDefined()
		expect(recievedRequest.members.TransactionTime).toBeDefined()
		expect(recievedRequest.members.MSISDN).toMatch('096000122')
		expect(recievedRequest.members.USSDServiceCode).toMatch('9999')
		expect(recievedRequest.members.USSDEncoding).toMatch('GSM0338')
		expect(recievedRequest.members.USSDRequestString).toMatch('#')
		expect(recievedRequest.members.response).toMatch('false')

		expect(response[0]).toBe(true)
		expect(response[1]).toMatch('Hello World')
	
	})
})

describe('Test callMenu', () => {
	test('Test calling unavailable remote menu', async () => {
		const config = require('./data/callMenuTest1.json');
		let menuTest = new MenuManager()
		await menuTest.load(config, true)
		request.setResponseConnectionRefused()
		let response = await menuTest.processRequest(realRequest)
		let checkRequest = request.getRequest()
		let recievedRequest = xmlrpc.parseRequest(checkRequest.data)

		// Verify that the recieved request was correct
		expect(recievedRequest.method).toMatch('handleUSSDRequest')
		expect(recievedRequest.members.TransactionId).toBeDefined()
		expect(recievedRequest.members.TransactionTime).toBeDefined()
		expect(recievedRequest.members.MSISDN).toMatch('096000122')
		expect(recievedRequest.members.USSDServiceCode).toMatch('9999')
		expect(recievedRequest.members.USSDEncoding).toMatch('GSM0338')
		expect(recievedRequest.members.USSDRequestString).toMatch('#')
		expect(recievedRequest.members.response).toMatch('false')

		expect(response[0]).toBe(true)
		expect(response[1]).toMatch('Service Unavailable')
	
	})
})
*/

  const firstRequest = {
    TransactionTime: "2020-04-11T11:11:20.000Z",
    MSISDN: "096000122",
    USSDRequestString: "#",
    response: "false",
    USSDEncoding: "GSM0338",
    USSDServiceCode: "123",
    TransactionId: "1000000002",
  };
  const firstResponse = xmlrpc.encodeResponse({
    TransactionId: "1000000002",
    TransactionTime: "2020-04-11T11:11:20.000Z",
    USSDEncoding: "GSM0338",
    action: "request",
    ResponseCode: "0",
    USSDResponseString: "1 option1\n2 option2\n",
  });
  const secondRequest = {
    TransactionTime: "2020-04-11T11:11:20.000Z",
    MSISDN: "096000122",
    USSDRequestString: "2",
    response: "true",
    USSDEncoding: "GSM0338",
    USSDServiceCode: "123",
    TransactionId: "1000000002",
  };
  const secondResponse = xmlrpc.encodeResponse({
    TransactionId: "1000000002",
    TransactionTime: "2020-04-11T11:11:20.000Z",
    USSDEncoding: "GSM0338",
    action: "end",
    ResponseCode: "0",
    USSDResponseString: "Your option was option 2",
  });

  /*
    2nd test for call menu
*/

  /*
describe('Test callMenu', () => {
	test('Test calling a remote menu, menu response', async () => {
		const config = require('./data/callMenuTest1.json');
		let menuTest = new MenuManager()
		await menuTest.load(config, true)
		request.setResponse({
			data: firstResponse,
			url: 'http://localhost:5000/RPC2'
		})
		let response = await menuTest.processRequest(firstRequest)
		let checkRequest = request.getRequest()
		let recievedRequest = xmlrpc.parseRequest(checkRequest.data)

    // Verify that the recieved request was correct

		expect(recievedRequest.method).toMatch('handleUSSDRequest')
		expect(recievedRequest.members.TransactionId).toBeDefined()
		expect(recievedRequest.members.TransactionTime).toBeDefined()
		expect(recievedRequest.members.MSISDN).toMatch('096000122')
		expect(recievedRequest.members.USSDServiceCode).toMatch('9999')
		expect(recievedRequest.members.USSDEncoding).toMatch('GSM0338')
		expect(recievedRequest.members.USSDRequestString).toMatch('#')
		expect(recievedRequest.members.response).toMatch('false')

		expect(response[0]).toBe(false)
		expect(response[1]).toMatch("1 option1\n2 option2")
    
        
    // 2nd request (after receiving response from user)

		request.setResponse({
			data: secondResponse,
			url: 'http://localhost:5000/RPC2'
		})
		let response2 = await menuTest.processRequest(secondRequest)
		let checkRequest2 = request.getRequest()
		let recievedRequest2 = xmlrpc.parseRequest(checkRequest2.data)


    // Verify that the recieved request2 was correct

		expect(recievedRequest2.method).toMatch('handleUSSDRequest')
		expect(recievedRequest2.members.TransactionId).toBeDefined()
		expect(recievedRequest2.members.TransactionTime).toBeDefined()
		expect(recievedRequest2.members.MSISDN).toMatch('096000122')
		expect(recievedRequest2.members.USSDServiceCode).toMatch('9999')
		expect(recievedRequest2.members.USSDEncoding).toMatch('GSM0338')
		expect(recievedRequest2.members.USSDRequestString).toMatch('2')
		expect(recievedRequest2.members.response).toMatch('true')

		expect(response2[0]).toBe(true)
		expect(response2[1]).toMatch("Your option was option 2")


	})

})
*/

  const encodingSelectionRequest = {
    TransactionTime: "2020-04-11T11:11:20.000Z",
    MSISDN: "096000122",
    USSDRequestString: "#",
    response: "false",
    USSDEncoding: "GSM0338",
    USSDServiceCode: "123",
    TransactionId: "1000000002",
  };

  const encodingSelectionResponse =
    "<methodResponse><params><param><value><struct><member><name>TransactionId</name>" +
    "<value><string>970963247</string></value></member><member><name>TransactionTime</name><value>" +
    "<dateTime.iso8601>20200325T08:14:13+0000</dateTime.iso8601></value></member><member><name>USSDResponseString</name>" +
    "<value><string>encodingSelection Response</string></value></member><member><name>language</name><value>" +
    "<string>FRA</string></value></member><member><name>ResponseCode</name><value><i4>1</i4></value></member>" +
    "<member><name>action</name><value><string>request</string></value></member><member><name>encodingSelection</name>" +
    "<value><array><data><value><struct><member><name>alphabet</name><value><string>LATN</string></value></member>" +
    "</struct><struct><member><name>language</name><value><string>FRA</string></value></member></struct></value>" +
    "</data></array></value></member></struct></value></param></params></methodResponse>"; /**
	})
	*/

  /*
describe('Test callMenu', () => {
	test('Test call menu with encodingSelection in response', async () => {
		const config = require('./data/callMenuTest1.json');
		let menuTest = new MenuManager()
		await menuTest.load(config, true)
		request.setResponse({
			data: encodingSelectionResponse,
			url: 'http://localhost:5000/RPC2'
		})
		let response = await menuTest.processRequest(encodingSelectionRequest)
		let checkRequest = request.getRequest()
		let recievedRequest = xmlrpc.parseRequest(checkRequest.data)

		// Verify that the recieved request was correct
		expect(recievedRequest.method).toMatch('handleUSSDRequest')
		expect(recievedRequest.members.TransactionId).toBeDefined()
		expect(recievedRequest.members.TransactionTime).toBeDefined()
		expect(recievedRequest.members.MSISDN).toMatch('096000122')
		expect(recievedRequest.members.USSDServiceCode).toMatch('9999')
		expect(recievedRequest.members.USSDEncoding).toMatch('GSM0338')
		expect(recievedRequest.members.USSDRequestString).toMatch('#')
		expect(recievedRequest.members.response).toMatch('false')

		expect(response[0]).toBe(false)
		expect(response[1]).toMatch('encodingSelection Response')
	

		/*const config = require('./data/callMenuTest1.json');
		let menuTest = new MenuManager()
		await menuTest.load(config)
		let response = await menuTest.processRequest(request)
		//console.trace(JSON.stringify(response, null, 3))
		expect(response[1]).toContain('New Look packages');*/
});
