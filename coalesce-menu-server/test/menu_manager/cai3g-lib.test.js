const caiLib = require("@coalesce/cai3g/cai3g.lib").EPSMultiSC;
const mockAxios = require("axios");
const parser = require("xml2json");
const fs = require("fs");

caiConfig = {
  url: "http://localhost:8085/",
};

test('Test the cai3g "sendSet" request', async () => {
  const SetXmlResponse = fs.readFileSync(`${__dirname}/responses/HLR-setResponse.xml`, "utf8");
  const jsonResponse = parser.toJson(SetXmlResponse, { object: true });

  const request = new caiLib.Set();
  const headers = new caiLib.Set.Header();
  headers.SessionId = jsonResponse["S:Envelope"]["S:Header"]["cai3g:SessionId"];
  request.MOType = "motype@testUrl";

  request.MOAttributes = new caiLib.Set.MOAttributes();
  request.MOAttributes.SetEPSMultiSC = new caiLib.SetEPSMultiSC();

  request.MOAttributes.SetEPSMultiSC.msisdn = "987654321";
  // example data
  request.MOAttributes.SetEPSMultiSC.epsLocationState = "PURGED";
  request.MOAttributes.SetEPSMultiSC.epsAccessRestriction = "E-UTRAN-DENIED";
  request.MOAttributes.SetEPSMultiSC.epsTenantId = 160;
  request.MOAttributes.SetEPSMultiSC.epsUserIpV4Address = "test$************";

  request.url = caiConfig.url;

  mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: SetXmlResponse }));

  await caiLib
    .sendSet(request, headers)
    .then((result) => {
      expect(result.data).toBe(SetXmlResponse);
    })
    .catch((errResult) => {
      console.error("Error: ", errResult);
      expect("failure").toBe("success");
    });
});

function getConfigureRequest(SessionId, identifier, optional) {
  if (!optional) optional = {};

  const configuredRequest = {
    headers: new caiLib.Get.Header(),
    request: new caiLib.Get(),
  };

  const allowedTypes = ["HLR", "AUC"];

  const isAUC = optional.nodeType && allowedTypes.includes(optional.nodeType) && optional.nodeType != "HLR";

  configuredRequest.headers.SessionId = SessionId;

  configuredRequest.request.MOId = new caiLib.Get.MOId();
  if (isAUC) configuredRequest.request.MOId.imsi = identifier;
  else configuredRequest.request.MOId.msisdn = identifier;

  configuredRequest.request.MOAttributes = new caiLib.Get.MOAttributes();
  configuredRequest.request.MOAttributes.GetEPSMultiSC = new caiLib.GetEPSMultiSC();

  configuredRequest.request.url = caiConfig.url;

  if (isAUC) configuredRequest.request.MOAttributes.GetEPSMultiSC.imsi = identifier;
  else configuredRequest.request.MOAttributes.GetEPSMultiSC.msisdn = identifier;

  if (allowedTypes.includes(optional.nodeType) && optional.nodeType != "HLR") {
    configuredRequest.request.MOAttributes.GetEPSMultiSC.nodeType = optional.nodeType;
    configuredRequest.request.MOAttributes.GetEPSMultiSC.akaType = "1";
  }

  return configuredRequest;
}

test('Test the cai3g "sendGet" (HLR) request (Not Provisioned)', async () => {
  const GetHLRXmlResponse = fs.readFileSync(`${__dirname}/responses/HLR-getResponse.xml`, "utf8");
  const jsonResponse = parser.toJson(GetHLRXmlResponse, { object: true });
  const SessionId = jsonResponse["S:Envelope"]["S:Header"]["cai3g:SessionId"];
  const identifier =
    jsonResponse["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"]["hss:msisdn"];

  const { headers, request } = getConfigureRequest(SessionId, identifier);

  mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));

  await caiLib
    .sendGet(request, headers)
    .then((result) => {
      expect(result.data).toEqual(GetHLRXmlResponse);
    })
    .catch((errResult) => {
      console.error("Error: ", errResult);
      expect("failure").toBe("success");
    });
});

test('Test the cai3g "sendGet" (HLR) request (Provisioned)', async () => {
  const GetHLRXmlResponse = fs.readFileSync(`${__dirname}/responses/HLR-getResponse-4g-provisioned.xml`, "utf8");
  const jsonResponse = parser.toJson(GetHLRXmlResponse, { object: true });
  const SessionId = jsonResponse["S:Envelope"]["S:Header"]["cai3g:SessionId"];
  const identifier =
    jsonResponse["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"]["hss:msisdn"];

  const { headers, request } = getConfigureRequest(SessionId, identifier);

  mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetHLRXmlResponse }));

  await caiLib
    .sendGet(request, headers)
    .then((result) => {
      expect(result.data).toEqual(GetHLRXmlResponse);
    })
    .catch((errResult) => {
      console.error("Error: ", errResult);
      expect("failure").toBe("success");
    });
});

test('Test the cai3g "sendGet" (AUC) request', async () => {
  const GetAUCXmlResponse = fs.readFileSync(`${__dirname}/responses/AUC-getResponse-is-4g.xml`, "utf8");
  const jsonResponse = parser.toJson(GetAUCXmlResponse, { object: true });
  const SessionId = jsonResponse["S:Envelope"]["S:Header"]["cai3g:SessionId"];
  const identifier =
    jsonResponse["S:Envelope"]["S:Body"].GetResponse.MOAttributes["hss:GetResponseEPSMultiSC"]["hss:imsi"];

  const { headers, request } = getConfigureRequest(SessionId, identifier, {
    nodeType: "AUC",
  });

  mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: GetAUCXmlResponse }));

  await caiLib
    .sendGet(request, headers)
    .then((result) => {
      expect(result.data).toBe(GetAUCXmlResponse);
    })
    .catch((errResult) => {
      console.error("Error: ", errResult);
      expect("failure").toBe("success");
    });
});
