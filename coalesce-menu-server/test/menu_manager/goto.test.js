const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));

const MenuManager = require('../../server/menu_manager')

const request = {
	"TransactionTime": "2020-04-11T11:11:20.000Z",
	"MSISDN": "96000122",
	"USSDRequestString": "#",
	"response": "false",
	"USSDEncoding": "GSM0338",
	"USSDServiceCode": "123",
	"TransactionId": "1000000002"
}

describe("Test 'goto' statement", () => {
	beforeEach(()=>{
		initializeHotConfig();
		getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});

	// Note: 'goto' outside of an item statement doesn't work
	// test('Test the goto statement', async () => {
	// 	const config = require('./data/gotoTest1.json');
	// 	let menuTest = new MenuManager()
	// 	menuTest.load(config, true)
	// 	let response = await menuTest.processRequest(request)

	// 	expect(response[1]).toContain('Hello');
	// });

	test('Test the goto statement', async () => {
		const config = require('./data/gotoTest2.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		const r = { ...request };
		r.USSDRequestString = '*1#';

		let response = await menuTest.processRequest(r);

		expect(response[1]).toContain('hello');
	});
});

