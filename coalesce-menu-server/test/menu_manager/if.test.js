const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
const {Etcd3} = require('etcd3');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));

/*
	Note: 'if' statement without 'else' doesn't work
*/

const MenuManager = require('../../server/menu_manager')

jest.mock('etcd3')

const request = {
	"TransactionTime": "2020-04-11T11:11:20.000Z",
	"MSISDN": "96000122",
	"USSDRequestString": "#",
	"response": "false",
	"USSDEncoding": "GSM0338",
	"USSDServiceCode": "123",
	"TransactionId": "1000000002"
}

describe("Test 'if' statement", () => {
	beforeEach(()=>{
		initializeHotConfig();
		getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});

	test('Test the menu if statement with int value match', async () => {
		const config = require('./data/ifTest1.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)
		let response = await menuTest.processRequest(request)
		//console.trace(JSON.stringify(response, null, 3))
	expect(response[1]).toContain('true');
	});

	// else statement doesn't work
	// test('Test the menu if statement with int value NOT match', async () => {
	// 	const config = require('./data/ifTest2.json');
	// 	let menuTest = new MenuManager()
	// 	menuTest.load(config, true)
	// 	let response = await menuTest.processRequest(request)
	// 	//console.trace(JSON.stringify(response, null, 3))
	//   expect(response[1]).toContain('true');
	// });

	test('Test the menu if statement with string value match', async () => {
		const config = require('./data/ifTest3.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)
		let response = await menuTest.processRequest(request)
		//console.trace(JSON.stringify(response, null, 3))
	expect(response[1]).toContain('true');
	});

	test('Test the menu if statement inside menu item', async () => {
		const config = require('./data/ifTest4.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		{
			const r = { ...request };
			r.USSDRequestString = '*1#';

			let response = await menuTest.processRequest(r)
			console.debug(response)
			expect(response[1]).toContain('true');
		}

		{
			const r = { ...request };
			r.USSDRequestString = '*2#';

			let response = await menuTest.processRequest(r)
			expect(response[1]).toContain('true');
		}
	});

	test('Test the if inside a menu item will end session if end is present', async () => {
		const config = require('./data/ifTest7.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		const r = { ...request };
		r.USSDRequestString = '*1#';

		let response = await menuTest.processRequest(r)
		expect(response[1]).not.toContain('Done 2\n');

	});
});
