const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));
/*
	Note: 'if' statement without 'else' doesn't work
*/

const MenuManager = require("../../server/menu_manager");

const request = {
  TransactionTime: "2020-04-11T11:11:20.000Z",
  MSISDN: "96000122",
  USSDRequestString: "#",
  response: "false",
  USSDEncoding: "GSM0338",
  USSDServiceCode: "123",
  TransactionId: "1000000002",
};

const config = require("./data/dynamicMenuTest1.json");

let menuTest = new MenuManager();

describe("Test dynamic menu", () => {
	beforeEach(()=>{
		initializeHotConfig();
		getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});
  test("empty", () => {});

  /*
	test('Test the dynamic menu initial display', async () => {

		await menuTest.load(config, true)

		let response = await menuTest.processRequest(request)
		expect(response[1]).toContain('EN: Welcome to dynamic menus');
	});

	test('Test the dynamic menu oneshot', async () => {
		{
		await menuTest.load(config, true)
			const r = { ...request };
			r.USSDRequestString = '*1#';

			let response = await menuTest.processRequest(r)
			expect(response[1]).toContain('EN: You have 1000 MB');
		}
	});
	*/
});
