
const menu = require('@coalesce/dynamicmenu');

function* main(vars, ctx) {

	menu.display(ctx, 'Hello');
	menu.item(ctx, 1, 'Test');
	menu.item(ctx, 2, 'Submenu');

	yield * menu.answer(ctx);

	switch (ctx.input) {
		case '1': menu.display(ctx, 'Option1'); break;

		case '2': {
			menu.display(ctx, 'Submenu');
			menu.item(ctx, 9, 'back');

			yield * menu.answer(ctx);

			switch (ctx.input) {
				case '9': menu.back(ctx); break;
			}
		}
		break;
	}
}

const kMainMenu = 'Hello\n1. Test\n2. Submenu'
	, kSubmenu = 'Submenu\n9. back'
	, kInvalidOption = 'You have selected an invalid option\n1. Retry\n2. Exit';
	;


describe('Test dynamic menu 2', () => {
	test('Test the dynamic menu initial display', async () => {
		const ctx = { vars: {} };

		await menu.execute(ctx.vars, ctx, main);

		expect(ctx.output).toContain(kMainMenu);

		ctx.input = '1';
		await menu.execute(ctx.vars, ctx, main);

		expect(ctx.output).toContain('Option1');
	});

	test('Test the dynamic menu invalid option', async () => {
		const ctx = { vars: {} };

		// initial menu
		await menu.execute(ctx.vars, ctx, main);

		ctx.input = '9';
		await menu.execute(ctx.vars, ctx, main);
		expect(ctx.output).toContain(kInvalidOption);

		ctx.input = '1';
		await menu.execute(ctx.vars, ctx, main);
		expect(ctx.output).toContain(kMainMenu);

		ctx.input = '1';
		await menu.execute(ctx.vars, ctx, main);
		expect(ctx.output).toContain('Option1');
	});

	test('Test the dynamic menu back', async () => {
		const ctx = { vars: {} };

		// initial menu
		await menu.execute(ctx.vars, ctx, main);

		ctx.input = '2';
		await menu.execute(ctx.vars, ctx, main);
		expect(ctx.output).toContain(kSubmenu);

		ctx.input = '9';
		await menu.execute(ctx.vars, ctx, main);
		expect(ctx.output).toContain(kMainMenu);
	});
});
