const MenuManager = require('../../server/menu_manager')

const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));


const request = {
	"TransactionTime": "2020-04-11T11:11:20.000Z",
	"MSISDN": "96000122",
	"USSDRequestString": "#",
	"response": "false",
	"USSDEncoding": "GSM0338",
	"USSDServiceCode": "123",
	"TransactionId": "1000000002"
}
describe("Menu Manager",()=>{
	beforeEach(()=>{
		initializeHotConfig();
		getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});

	test('Test the menu display statement', async () => {
		const config = require('./data/displayTest1.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)
		let response = await menuTest.processRequest(request)
		//console.trace(JSON.stringify(response, null, 3))
		expect(response[1]).toContain('New Look packages');
	});

	test('Test the menu display statement using French', async () => {
		const config = require('./data/displayTest2.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)
		let response = await menuTest.processRequest(request)
		//console.trace(JSON.stringify(response, null, 3))
		expect(response[1]).toContain('Decouvrez les forfaits New Look');
	});

	test('Test the menu display statement variable evaluation', async () => {
		const config = require('./data/displayTest3.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)
		let response = await menuTest.processRequest(request)

		expect(response[1]).toContain(request['MSISDN']);
	});
});
