const {initializeHotConfig,getHotConfig} = require('../../server/HotConfig');
jest.mock('../../server/HotConfig', () => jest.requireActual('../HotConfigMock.js'));

const MenuManager = require('../../server/menu_manager')

const request = {
	"TransactionTime": "2020-04-11T11:11:20.000Z",
	"MSISDN": "96000122",
	"USSDRequestString": "#",
	"response": "false",
	"USSDEncoding": "GSM0338",
	"USSDServiceCode": "123",
	"TransactionId": "1000000002"
}

describe("Test 'item' statement", () => {
	beforeEach(()=>{
		initializeHotConfig();
		getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints","{}");
	});
	test('Test item selection', async () => {
		const config = require('./data/itemTest1.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		let response = await menuTest.processRequest(request)
		expect(response[1]).toContain('1item1');

		const request2 = { ...request };
		request2.USSDRequestString = '1';
		request2.response = 'true';

		response = await menuTest.processRequest(request2);
		expect(response[1]).toContain('hello');
	});

	test('Test item selection. Invalid option selected', async () => {
		const config = require('./data/itemTest1.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		let response = await menuTest.processRequest(request)
		expect(response[1]).toContain('1item1');

		const request2 = { ...request };
		request2.USSDRequestString = '2';
		request2.response = 'true';

		response = await menuTest.processRequest(request2);
		expect(response[1]).toContain('Invalid choice.');
	});

	test('Test item selection. Oneshot', async () => {
		const config = require('./data/itemTest1.json');
		let menuTest = new MenuManager()
		menuTest.load(config, true)

		const request2 = { ...request };
		request2.USSDRequestString = '*1#';

		response = await menuTest.processRequest(request2);
		expect(response[1]).toContain('hello');
	});
});
