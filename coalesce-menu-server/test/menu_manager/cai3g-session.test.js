const { initializeHotConfig, getHotConfig } = require("../../server/HotConfig");
jest.mock("../../server/HotConfig", () => jest.requireActual("../HotConfigMock.js"));

jest.mock("axios");
const mockAxios = require("axios");

const parser = require("xml2json");
const cai3gSessionCtl = require("@coalesce/cai3g/cai3g-session-ctl.lib").SessionControl;

let sessionId = "7fdd18ee67b54994a5279c4e1068cbd4";

const { host, port } = {
  host: "127.0.0.1",
  port: 8084,
};

let LoginXmlResponse = `<S:Envelope xmlns:S="http://schemas.xmlsoap.org/soap/envelope/">
<S:Body>
<LoginResponse xmlns="http://schemas.ericsson.com/cai3g1.2/">
<sessionId>31b47c76c326460e8d850ae2bc45dd52</sessionId>
<baseSequenceId>118536052</baseSequenceId>
</LoginResponse>
</S:Body>
</S:Envelope>`;

let LogoutXmlResponse = `<S:Envelope
xmlns:S="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:cai3g="http://schemas.ericsson.com/cai3g1.2/">
<S:Header>
<cai3g:SessionId>sessionId</cai3g:SessionId>
</S:Header>
<S:Body>
<LogoutResponse xmlns="http://schemas.ericsson.com/cai3g1.2/"/>
</S:Body>
</S:Envelope>`;

// let LoginJsonResponse = parser.toJson(LoginXmlResponse, { object: true });

describe("Test cai3gSessionCtl", () => {
  beforeEach(() => {
    initializeHotConfig();
    getHotConfig().setKeyValue("coalesce/cs/development/menu/preprod/endpoints", "{}");
  });
  test("Test Login call", async () => {
    let request = new cai3gSessionCtl.Login();

    request.userId = "987654321";
    request.pwd = "put_password_here";
    request.url = `http://${host}:${port}`;

    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: LoginXmlResponse }));

    await cai3gSessionCtl
      .sendLogin(request)
      .then((result) => {
        expect(result.data).toEqual(LoginXmlResponse);
      })
      .catch((errResult) => {
        console.error("The error (for sendLogin()): ", errResult);
        expect("failure").toBe("success");
      });
  });

  test("Test Logout call", async () => {
    let request = new cai3gSessionCtl.Logout();

    request.sessionId = sessionId;
    request.url = `http://${host}:${port}`;

    const header = new cai3gSessionCtl.Logout.Header();
    header.SessionId = "123456";

    mockAxios.post.mockImplementationOnce(() => Promise.resolve({ data: LogoutXmlResponse }));

    cai3gSessionCtl
      .sendLogout(request, header)
      .then((result) => {
        expect(result.data).toEqual(LogoutXmlResponse);
      })
      .catch((errResult) => {
        console.error("The error (for sendLogout()): ", errResult);
        expect("failure").toBe("success");
      });
  });
});
