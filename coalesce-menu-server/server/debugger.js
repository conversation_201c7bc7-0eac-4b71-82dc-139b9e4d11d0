#!/usr/bin/env node
require("./logger");

const PROTO_PATH = __dirname + "/../proto/debugger.proto";
const grpc = require("grpc");
const protoLoader = require("@grpc/proto-loader");
const { Roarr } = require("roarr");
const { nanoid } = require("nanoid");
const CONST = require("@coalesce/constants");

const Session = require("./session");
const InternalServerError = require("./internalServerError");
const CoalesceError = require("@coalesce/coalesceError");

const PORT = process.env.DEBUGGER_PORT || 5011;
// Suggested options for similarity to existing grpc.load behavior
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: Number,
  enums: String,
  defaults: false,
  oneofs: true,
});

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
//console.debug('protoDescriptor', protoDescriptor);
// The protoDescriptor object has the full package hierarchy
const proto = protoDescriptor.Debugger;

const sessions = new Map();

function handleRun(call, callback) {
  console.debug("Starting debugger handleRun() request");
  const r = call.request;
  const id = r.id ? r.id : getId();
  let status = 0;
  let errmsg = "";

  try {
    call.on("cancelled", () => {
      console.debug("call().on('cancelled') :: Peer got disconnected: " + id);
      deleteSession(id);
    });

    if (!r.msisdn) {
      errmsg = "Missing msisdn";
      console.error(errmsg);
      status = 1;
    } else {
      let session = sessions.get(id);
      if (session) {
        if (r.config) {
          console.debug("Re-running existing session with _new_ config");
          session = createSession(id, r.config);
        } else {
          console.debug("Re-running existing session with _existing_ config");
          session.reset();
        }
      } else if (r.config) {
        console.debug("Creating a new session");
        session = createSession(id, r.config);
      }

      if (!session) {
        errmsg = "Invalid or missing Menu Config";
        console.error(errmsg);
        status = 1;
      } else {
        session.setVariable("MSISDN", r.msisdn);
        session.setVariable("IMSI", r.imsi);

        if (r.lang) session.setVariable("language", r.lang);
      }
    }
    if (status === 0) callback(null, { id, status });
    else
      callback({
        code: 401,
        message: errmsg,
        status: grpc.status.FAILED_PRECONDITION,
      });
  } catch (err) {
    console.error(err);
    callback({
      code: 400,
      message: "Menu loading error " + err.message,
      status: grpc.status.INVALID_ARGUMENT,
    });
  }
}

function run(call, callback) {
  const r = call.request;

  Roarr.adopt(
    () => {
      handleRun(call, callback);
    },
    {
      msisdnA: r.msisdn,
      tid: String(r.TransactionId || nanoid(CONST.NANOID_LENGTH)),
    }
  );
}

async function handleCommand(call, callback) {

  try {
  
    const r = call.request;
    const id = r.id;
    if (!id) {
      console.error("Invalid session id");
      call.end();
      return;
    }

    call.on("cancelled", () => {
      console.debug("call().on('cancelled') :: Peer got disconnected: " + id);
      deleteSession(id);
    });

    //console.debug('COMMAND: ' + r.command);

    const session = sessions.get(id);
    if (!session) {
      console.error("Invalid session id: ", id);
      call.end();
      return;
    }

    const response = await session.processRequest(r);
    if (response instanceof InternalServerError) {
      callback(response, null);
      return;
    }

    const vars = { ...response.vars };
    delete vars.componentManager;
    response.vars = JSON.stringify(vars);

    callback(null, response);
  } catch (err) {
    console.error(err);
    callback({
      code: 500,
      message: "Menu execution error: " + err.message,
      status: grpc.status.UNKNOWN,
    });
  }
}

function command(call, callback) {

  const r = call.request;
  const id = r.id;
  let session = {
    ctx: {
      variables: {
        MSISDN: "*****",
      },
    },
  };
  if (id) session = sessions.get(id);

  Roarr.adopt(
    () => {
      handleCommand(call, callback);
    },
    {
      msisdnA: session.ctx.variables.MSISDN,
      tid: String(session.ctx.variables.TransactionId || nanoid(CONST.NANOID_LENGTH)),
    }
  );
}

function getId() {
  const id = 100000 + Math.floor(Math.random() * 1000000);
  return id;
}

function deleteSession(id) {
  sessions.delete(id);
}

function createSession(id, config) {
  const session = new Session(config);

  if (session.isValidMenuConfig()) {
    sessions.set(id, session);
    console.info("Session object created");
    return session;
  }

  console.warn("Invalid menu config, not creating session");

  return null;
}

function start() {
  const server = new grpc.Server();
  console.debug("Starting gRPC server on: ", PORT);
  server.addService(proto.service, { run, command });
  server.bind("0.0.0.0:" + PORT, grpc.ServerCredentials.createInsecure());
  server.start();
  console.info(`gRPC Server started on 0.0.0.0:${PORT}`);
}

module.exports = {
  start,
};

if (require.main === module) start();
