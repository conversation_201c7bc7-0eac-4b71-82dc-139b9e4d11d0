const Redis = require('ioredis');

let REDIS_NAMESPACE = process.env.REDIS_NAMESPACE || 'coalesce';
REDIS_NAMESPACE += `-menu:`;

// Parse REDIS_HOSTS from env
let redisHosts;
try {
  redisHosts = JSON.parse(process.env.REDIS_HOSTS || '[{"host":"localhost","port":6379}]');

  if (!Array.isArray(redisHosts)) {
    throw new Error('REDIS_HOSTS must be a JSON array');
  }
} catch (err) {
  console.error('Failed to parse REDIS_HOSTS:', err.message);
  process.exit(1);
}

class RedisClient {
  constructor() {

    // Single instance mode if REDIS_HOSTS is a single object
    if (redisHosts.length === 1) {
      const { host, port } = redisHosts[0];
      this.client = new Redis({
        host,
        port,
        keyPrefix: REDIS_NAMESPACE,
      });
      console.info(`[Redis] Connected in SINGLE instance mode → ${host}:${port}`);
    } else {
      this.client = new Redis.Cluster(redisHosts, {
        redisOptions: {
          keyPrefix: REDIS_NAMESPACE,
        },
      });
      console.info('[Redis] Connected in CLUSTER mode →', redisHosts.map(h => `${h.host}:${h.port}`).join(', '));
    }

    this.client.on('connect', () => {
      console.info('Redis connection established');
    });


    this.client.on('reconnecting', () => {
      console.warn('Redis client is reconnecting...');
    });

    // on disconnect
    this.client.on('disconnect', () => {
      console.error('Redis connection disconnected');
    });


    this.client.on('node added', (node) => {
      const { host = 'unknown', port = 'unknown', role = 'unknown' } = node?.options || {};
      console.info(`[Redis][Cluster] Node added → ${host}:${port} (${role})`);
    });
    
    this.client.on('node removed', (node) => {
      const { host = 'unknown', port = 'unknown', role = 'unknown' } = node?.options || {};
      console.warn(`[Redis][Cluster] Node removed → ${host}:${port} (${role})`);
    });
    
    this.client.on('node error', (err, node) => {
      const { host = 'unknown', port = 'unknown' } = node?.options || {};
      console.error(`[Redis][Node Error] ${host}:${port} →`, err?.message || err);
    });


    this.client.on('error', (err) => {
      console.error('Redis connection error:', err);
    });
  }

  getClient() {
    return this.client;
  }

  close() {
    this.client.quit();
    console.info('Redis connection closed');
  }
}

const redisClient = new RedisClient();

module.exports = {
  redisClient,

  redisKeys: {
    /**
     * Prefixes a key with the Redis namespace, used in the lua scripts or other commands which does not support the keyPrefix option
     * @param {string} key 
     * @returns {string} Prefixed Redis key
     */
    addNamespaceToKey: function (key) {
      return `${REDIS_NAMESPACE}:${key}`;
    },

    /**
     * Formats a session key for Redis Cluster hash slot compatibility
     * @param {string} key 
     * @returns {string} Cluster-safe session key
     */
    session: function (key) {
      return `session:{${key}}`;
    }
  },
};
