const { initializeHotConfig } = require('../HotConfig');
const { deleteMessageByKey } = require('../ins/messages');
const HelperFns = require('./helper');
const { CS_MENU_ENVIRONMENT } = require('@coalesce/env');


const CUSTOMER = process.env.CS_CUSTOMER_NAME;
const productionType = CS_MENU_ENVIRONMENT;

// Left-to-right precedence. The namespace was set on original code before refactor.
const namespace = process.env.NAMESPACE || process.env.STAGING_NAMESPACE || "default";


class MenuDataClass {
  constructor(etcd) {
    this.etcd = etcd;
    /**
     * The object to store the menu specific data,
     * @type {Doc_MenuData_MenuData}
     * 
     */
    this.menuData = {};
    /**
     * The object to store the menu config. The key will be the ussd code.
     * @type {Object.<string, Doc_MenuData_LaunchPadConfig & { moduleId: string}>}
     */
    this.ussdCodesMenu = {};

    /**
     * The object to store the menu managers. The key will be the module id.
     * It will be up to date with the latest versions!
     * @type {Object.<string, {moduleId: string, version: string, menuManager: import("../menu_manager")}>}
     */
    this.menuManagers = {};

  }

  /**
   * Launchpad etcd key for the launchpad configuration
   * @param {Doc_MenuData_ProductionType} productionType
   * @returns
   */
  launchpadConfigKey = (productionType) => `/coalesce/${CUSTOMER}/menu/launchpad/${productionType}`;

  /**
   * Launchpad etcd key for the menu specific version data
   * @param {Number} moduleId
   * @param {Number} version
   * @returns
   */
  menuVersionDataKey = (moduleId, version) => `/coalesce/${CUSTOMER}/launchpad/menu/${moduleId}/${version}`;

  /**
   * Function to set the launchpad configuration in etcd. And also store the menu specific version data if provided.
   * As the menu server is already watching for the changes so it will automatically update the menu in real time.
   * @param {Doc_MenuData_ProductionType} productionType The key to be fetched from etcd
   * @param {Number} moduleId The module ID, the id is the maria db row module id
   * @param {Doc_MenuData_LaunchPadConfig} launchPadConfig The launchPadConfig object
   * @param {Object} [menuVersionDataObj] The menu specific version data object if we want to store the menu specific version data also to the etcd
   */
  async addMenuLaunch(productionType, moduleId, launchPadConfig, menuVersionDataObj) {
    try {
      const key = this.launchpadConfigKey(productionType);

      // Fetch the current value
      let response = await this.etcd.get(key);

      // If it did not exist then set the empty object
      if (!response) {
        response = JSON.stringify({});
      }

      // Parse the current value
      /** @type {Doc_MenuData_Launches} */
      let currentValue = JSON.parse(response);

      currentValue[moduleId] = launchPadConfig;

      // Save the launchPadConfig in ETCD
      await this.etcd.put(this.launchpadConfigKey(productionType)).value(JSON.stringify(currentValue));

      // Save the menu specific version json data in ETCD, if provided
      if (menuVersionDataObj) {
        await this.addUpdateMenuVersionData(moduleId, launchPadConfig.version, menuVersionDataObj);
      }

      //console.log(`Launch for module id: ${moduleId} in ${productionType}: ${JSON.stringify(launchPadConfig)} set successfully`);

      return HelperFns.returnObj(true, 'USSD mapping set successfully');
    } catch (err) {
      console.error(`Failed to do menu launch for module id: ${moduleId}: with error ${err.message}`, err.stack || err);
      return HelperFns.returnObj(false, 'Failed to do menu launch');
    }
  }


  /**
   * Get the menu data for specific module id and version
   * @param {String | Number} moduleId 
   * @param {String | Number} version 
   * @returns {Promise.<{success: Boolean, data: Doc_MenuData_MenuData_Item}>}
   */
  async getMenuData(moduleId, version) {

    try {
      // Fetch the menu specific version data from etcd
      const menuVersionDataKey = this.menuVersionDataKey(moduleId, version);

      const response = await this.etcd.get(menuVersionDataKey);

      if (!response) {
        return HelperFns.returnObj(false, 'Menu version data not found');
      }

      const menuVersionData = JSON.parse(response);

      return HelperFns.returnObj(true, menuVersionData);

    } catch (err) {
      console.error(`Failed to get menu data for module id: ${moduleId} and version: ${version}: with error ${err.message}`, err.stack || err);
      return HelperFns.returnObj(false, 'Failed to get menu data');
    }
  }

  /**
   * Function to get the launchpad configuration for specific menu from etcd
   *
   * @param {Doc_MenuData_ProductionType} productionType
   * @param {String | Number} moduleId
   * @returns {Promise.<{success: Boolean, moduleFound: boolean, data: Doc_MenuData_LaunchPadConfig}>}
   */
  async getModuleLaunchConfig(productionType, moduleId) {
    try {
      const key = this.launchpadConfigKey(productionType);

      // Fetch the current value
      let response = await this.etcd.get(key);

      // If it did not exist then set the empty object
      if (!response) {
        response = JSON.stringify({});
      }

      // Parse the current value
      /** @type {Doc_MenuData_Launches} */
      let currentValue = JSON.parse(response);

      if (!currentValue[moduleId]) {
        return { ...HelperFns.returnObj(true, 'Module ID not found'), moduleFound: false };
      }

      return { ...HelperFns.returnObj(true, currentValue[moduleId]), moduleFound: true };
    } catch (err) {
      console.error(`Failed to get menu launch for module id: ${moduleId}: with error ${err.message}`, err.stack || err);
      return HelperFns.returnObj(false, 'Failed to get menu launch');
    }
  }

  /**
   * Function to update the launchpad configuration for specific menu and for specific properties in etcd
   * @param {Doc_MenuData_ProductionType} productionType
   * @param {String | Number} moduleId
   * @param {Doc_MenuData_LaunchPadConfig} propertiesUpdate The Object containing the key value pair to be updated
   * @returns
   */
  async updateLaunchPadConfigProperty(productionType, moduleId, propertiesUpdate) {
    try {
      const key = this.launchpadConfigKey(productionType);

      // Fetch the current value
      let response = await this.etcd.get(key);

      // If it did not exist then set the empty object
      if (!response) {
        response = JSON.stringify({});
      }

      // Parse the current value
      /** @type {Doc_MenuData_Launches} */
      let currentValue = JSON.parse(response);

      if (!currentValue[moduleId]) {
        return HelperFns.returnObj(false, 'Module ID not found');
      }

      // Update the properties
      currentValue[moduleId] = { ...currentValue[moduleId], ...propertiesUpdate };

      // Save the launchPadConfig in ETCD
      await this.etcd.put(this.launchpadConfigKey(productionType)).value(JSON.stringify(currentValue));

      return HelperFns.returnObj(true, 'Properties updated successfully');
    } catch (err) {
      console.error(`Failed to update property/s for module id: ${moduleId}: with error ${err.message}`, err.stack || err);

      return HelperFns.returnObj(false, 'Failed to update property');
    }
  }

  /**
   * Function to get the launchpad configuration from etcd
   * @param {Doc_MenuData_ProductionType} productionType
   * @returns {Promise.<{success: Boolean, data: Doc_MenuData_LaunchPadConfig}>}
   */
  async getMenuLaunch(productionType) {
    try {
      const key = this.launchpadConfigKey(productionType);

      // Fetch the current value
      let response = await this.etcd.get(key);

      // If it did not exist then set the empty object
      if (!response) {
        response = JSON.stringify({});
      }

      // Parse the current value
      /** @type {Doc_MenuData_Launches} */
      let currentValue = JSON.parse(response);

      return HelperFns.returnObj(true, currentValue);
    } catch (err) {
      console.error(`Failed to get menu launch for module id: ${moduleId}: with error ${err.message}`, err.stack || err);
      return HelperFns.returnObj(false, 'Failed to get menu launch');
    }
  }

  /**
   * Function to set the menu specific version data in etcd
   * @param {Number} moduleId
   * @param {Number} version
   * @param {Object} menuVersionDataObj
   */
  async addUpdateMenuVersionData(moduleId, version, menuVersionDataObj) {
    try {
      // Save the menu specific version json data in ETCD
      await this.etcd.put(this.menuVersionDataKey(moduleId, version)).value(JSON.stringify(menuVersionDataObj));

      //console.log(`Menu version data for module id: ${moduleId} and version: ${version} set successfully`);

      return HelperFns.returnObj(true, 'Menu version data set successfully');
    } catch (err) {
      console.error(
        `Failed to do menu version data set for module id: ${moduleId} and version: ${version}: with error ${err.message}`,
        err.stack || err,
      );
      return HelperFns.returnObj(false, 'Failed to do menu version data set');
    }
  }

  /**
   * Function to watch for changes in the launchpad config and update menuVersions and ussdCodesMenu.
   * When it's called then it will also fetch the current launchpad config from etcd and then watch for changes.
   * @param {Doc_MenuData_ProductionType} productionType
   */
  async watchLaunchpadConfig(productionType) {
    const key = this.launchpadConfigKey(productionType);

    try {


      // Fetch the current launchpad config from etcd
      const response = await this.getMenuLaunch(productionType);


      // if not success then return
      if (!response.success) {
        console.error(`Error loading menu dat and config for ${key}:`, response.data);
      } else {

        // Update the menuVersions and ussdCodesMenu
        await this.local_updateLaunchpadData(response.data);

      }


      // Watch for changes on the etcd key
      this.etcd
        .watch()
        .key(key)
        .create()
        .then((watcher) => {
          watcher.on('put', (value) => {

            const launchpadConfig = JSON.parse(value.value.toString());

            // Update the menuData and ussdCodesMenu
            this.local_updateLaunchpadData(launchpadConfig);

          });
        });
      console.debug(`Watching for changes on: ${key} for production type: ${productionType}`);
    } catch (err) {
      console.error(`Critical Error #15543: Fail while sycning the menu data, ussdCodes etc`, err.stack || err);
    }
  }

  /**
   * Updates the local variable menuData and ussdCodesMenu objects based on the launchpad configuration
   * @param {Doc_MenuData_Launches} launchpadConfig
   */
  async local_updateLaunchpadData(launchpadConfig) {

    const updatedUssdCodes = {};

    for (const moduleId in launchpadConfig) {
      const config = launchpadConfig[moduleId];


      // Update the menuData object, it will auto fetch the specific version data if not present
      await this.local_updateMenuDataModule(moduleId, config);


      // Update ussdCodesMenu object
      for (const ussdCode of config.codes) {

        updatedUssdCodes[ussdCode] = {
          moduleId: moduleId,
          ...config
        };
      }

    }


    // Update the ussdCodesMenu object
    this.ussdCodesMenu = updatedUssdCodes;

    //console.log('ussdCodesMenu Updated:', this.ussdCodesMenu);

  }

  /**
   * Update the local menu data module by the module id and config.
   * It will check if the object is already present the required version data then it will skip it else it will update
   * the requred version data.
   * @param {String | Number} moduleId
   * @param {String | Number | Doc_MenuData_LaunchPadConfig} versionOrConfigData
   * @param {Doc_MenuData_ProductionType} iProductionType
   */
  async local_updateMenuDataModule(moduleId, versionOrConfigData, iProductionType = productionType) {

    let version = versionOrConfigData;
    /** @type {Doc_MenuData_LaunchPadConfig} */
    let config = versionOrConfigData;

    if (typeof versionOrConfigData === 'object') {
      version = versionOrConfigData.version;
    } else {
      const configDataRes = await this.getModuleLaunchConfig(iProductionType, moduleId);

      if (!configDataRes.moduleFound) return false;

      config = configDataRes.data;
    }

    // If the menuData object is not present or the version is different then update it
    if (!this.menuData[moduleId] || this.menuData[moduleId].version.toString() !== version.toString()) {

      // Fetch the menu specific version data from etcd
      const menuDataResponse = await this.getMenuData(moduleId, version);

      // if failed
      if (!menuDataResponse.success) {
        console.warn(`Error fetching menu data for module id: ${moduleId} and version: ${version}:`, menuDataResponse.data);
        return false;
      }


      const fullMenuData = {
        ...config,
        menuData: menuDataResponse.data,
      };

      // Update menuData object
      this.menuData[moduleId.toString()] = fullMenuData;


      // Create the new MenuManager class instance by the menu menuData object i.e config, components etc
      const menuManager = await this._ready_MenuManagerClassByMenuCodes(fullMenuData, moduleId.toString(), true);

      // Update the menuManagers object
      this.menuManagers[moduleId.toString()] = {
        moduleId: moduleId,
        version: version,
        menuManager: menuManager,
      }
    }

    return true
  }

  /**
   * Get the menu data by the ussd code. It will always get the menu code of the active version
   * @param {String | Number} ussdCode 
   * @returns {Promise.<{success: Boolean, data: {menuData: Doc_MenuData_MenuData_Item, menuManagerClass: import("../menu_manager")}>}>}
   */
  async local_getMenuDataByUssdCode(ussdCode) {

    ussdCode = ussdCode.toString();

    const ussdCodeData = this.ussdCodesMenu[ussdCode];

    if (!ussdCodeData) {
      return HelperFns.returnObj(false, 'USSD code not found');
    }

    const moduleId = ussdCodeData.moduleId.toString();
    const version = ussdCodeData.version;

    // This function will update the menuData object if not present
    const resposne = await this.local_updateMenuDataModule(moduleId, version);

    // If failed
    if (!resposne) {
      return HelperFns.returnObj(false, 'Failed to get menu data');
    }

    const menuData = this.menuData[moduleId];
    const menuManagerClass = this.menuManagers[moduleId].menuManager;

    return HelperFns.returnObj(true, {
      menuData: menuData,
      menuManagerClass: menuManagerClass
    });
  }


  /**
   * Get the component by the name and ussd code
   * @param {string} componentName 
   * @param {number | string} ussdCode 
   * @returns {Promise.<{success: Boolean, data: function}>}
   */
  async local_getComponentByNameAndUssdCode(componentName, ussdCode) {

    // Get the menu data response
    const menuDataResponse = await this.local_getMenuDataByUssdCode(ussdCode.toString());

    // If not success
    if (!menuDataResponse.success) {
      return HelperFns.returnObj(false, 'Menu data not found');
    }

    // Get the component by the name
    const component = menuDataResponse.data.menuManagerClass.components[componentName];

    // If not component
    if (!component) {
      return HelperFns.returnObj(false, 'Component not found');
    }

    return HelperFns.returnObj(true, component);

  }

  /**
   * Create the new MenuManager class instance by the menu menuData object i.e config, components etc
   * @param {Doc_MenuData_MenuData[String]} origMenuData
   * @param {String | Number} moduleId
   * @param {Boolean} [deletePreviousGlobalMessages=false] To avoid the build up of the global messages object, if we want to remove the previous version of this menu messages from the global messages object. As this function is called when we have the update of the menu or initial load of the menu.
   */
  async _ready_MenuManagerClassByMenuCodes(origMenuData, moduleId, deletePreviousGlobalMessages = false) {

    // ----------- if deletePreviousGlobalMessages: Try to get the previous menuManager class if exist and remove the messages from the global message object ------------

    if (deletePreviousGlobalMessages) {
      /**
       * Note: To avoid the build up of the global messages object, we will remove the old messages from the global messages object
       */
      const menuOldObject = this.menuManagers[moduleId.toString()];

      if (menuOldObject) {

        const oldMessages = menuOldObject.menuManager.messages;
        for (const key in oldMessages) {
          deleteMessageByKey(key);
        }
      }
    }
    // ----------------------------------------------------------------------------------------------------------------------------------



    // Load the MenuManager class
    const MenuManager = require('../menu_manager');

    // clone the object
    /** @type {origMenuData} */
    const menuData = JSON.parse(JSON.stringify(origMenuData));

    // First we need to get the menuCodes as string and then have to replace the namespace with the actual namespace
    const menuCodes = JSON.stringify(menuData.menuData.menu_code).replace(/%NAMESPACE%/g, namespace);

    // Parse back the menuCodes
    /** @type {menuData["menuData"]["menu_code"]} */
    const parsedMenuData = JSON.parse(menuCodes);

    // Create the new MenuManager class instance
    const menuManager = new MenuManager();

    // Load the menu data
    await menuManager.load(parsedMenuData, menuData.menuData.components);

    return menuManager;

  }
}

const Etcd3Instance = initializeHotConfig().etcd;

const MenuData = new MenuDataClass(Etcd3Instance);

// Listen for changes in the launchpad config to keep our menu, ussd codes updated
MenuData.watchLaunchpadConfig(productionType);

module.exports = MenuData;


/**
 * @typedef {"preprod" | "prod"} Doc_MenuData_ProductionType
 */

/**
 * @typedef {Object} Doc_MenuData_LaunchPadConfig
 * @property {String} moduleName The name of the module
 * @property {Number[]} codes The ussd codes to be saved in etcd
 * @property {String} version The version of the module
 * @property {String} [comment] The comment regarding the launch if any
 * @property {String} date The date of the launch/update
 */

/**
 * Launchpad launches, the key will be the module id
 * @typedef {Object.<string, Doc_MenuData_LaunchPadConfig>} Doc_MenuData_Launches
 *
 */


/**
 * @typedef {Object.<string, Doc_MenuData_LaunchPadConfig & {menuData: Doc_MenuData_MenuData_Item}>} Doc_MenuData_MenuData
 */



/**
 * @typedef {Object} Doc_MenuData_MenuData_Item
 * @property {Object} menu_code The menu code generated by the gui
 * @property {String} menu_code.root_menu The name of the root menu
 * @property {Object.<string, {content: Array.<Object<String, any>>}} menu_code.menus The menu-config.json data generated by the gui
 * @property {Object.<string, {description: String, content: {eng: String, fra: String, ewe: String}}} menu_code.messages The messages generated by the gui
 * @property {String[]} menu_code.ussd_matches The ussd matches generated by the gui
 * @property {Object.<string, string>} components The component codes which we created on the gui, key will be the component name and value will be the component js code
 * @property {string} comment The comment while launching/releasing version of the menu in gui launchpad
*/