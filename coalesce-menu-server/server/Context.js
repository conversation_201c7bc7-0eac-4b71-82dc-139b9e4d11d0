const replacer = (key, value) => {
  if (value instanceof Map) {
    return { jsonDataType: 'map', data: Array.from(value.entries()) };
  } else {
    return value;
  }
};

const reviver = (key, value) => {
  if (value && value.jsonDataType === 'map') {
    return new Map(value.data);
  } else {
    return value;
  }
};

JSON.stringifyWithMap = (data) => {
  return JSON.stringify(data, replacer);
};

JSON.parseHasMap = (data) => {
  return JSON.parse(data, reviver);
};

module.exports = class Context {
  constructor() {
    this.reset();
  }

  reset() {
    this.variables = {};
    this.sp = 0;
    this.output = '';
    this.input = null;
    this.waiting = false;
    this.components = new Map();
    this.lastupdate = Math.floor(new Date() / 1000);
  }

  toString() {
    return JSON.stringifyWithMap(this);
  }

  static fromString(ctxString) {
    let ctx = new Context();
    try {
      const ctxObject = JSON.parseHasMap(ctxString);
      ctx = ctx._fromObject(ctxObject);
    } catch (e) {
      console.warn('Context.fromString(ctxString) Failed, where ctxString =' + ctxString);
      ctx.reset();
    }

    return ctx;
  }

  _fromObject(ctxObject) {
    Object.entries(ctxObject).forEach((entry) => {
      const [key, value] = entry;
      this[key] = value;
    });
    this.input = ctxObject.input || null;

    return this;
  }

  setvars(vars) {
    if (vars != null && typeof vars == 'object' && !Array.isArray(vars)) {
      this.variables = { ...this.variables, ...vars };
    }
  }
};
