#!/usr/bin/env node

// Init logger
require("./logger/logger");

const { nanoid } = require("nanoid");
const express = require("express");
const app = express();
const fs = require("fs");
const path = require("path");
const { Roarr } = require("roarr");
let http = require('http');
const http2 = require('http2');

const CONST = require("@coalesce/constants");
const xmlrpc = require("@coalesce/xmlrpc");
const { isPreviewer, LOG_MEMORY_DATA } = require("@coalesce/env");
const CoalesceError = require("@coalesce/coalesceError");

const previewerDebugger = require("./debugger");
const { InvalidXmlRpcRequestException } = xmlrpc;
const MenuManager = require("./menu_manager");
const { initializeHotConfig } = require("./HotConfig");
const { performance } = require('perf_hooks');
const v8 = require('v8');

require('dotenv').config()


// Ready the menu data class
const MenuData = require("./utils/menuData");



/**
 * NODE GARBAGE COLLECTION
 */
/*
const v8 = require('v8');
const vm = require('vm');

v8.setFlagsFromString('--expose_gc');
const runGarbageCollector = vm.runInNewContext('gc'); // nocommit
*/

const hotConfigHosts = (process.env.CS_CONFIG_PEERS || "http://localhost:2379").split(",");

if (process.env.USE_HTTP2 === 'true') http = http2

const HTTP_SERVER_LISTEN_ADDR = "0.0.0.0";
const HTTP_SERVER_PATH = "/RPC2";
const HTTP_SERVER_PORT = process.env.PORT || 8080;

const MIN_TIMEOUT = 5000;
const MAX_TIMEOUT = 60000;

const USSD_REQUEST_STRING_LENGTH_LIMIT = 182

class StartupException extends CoalesceError {
  constructor(...params) {
    super(StartupException, ...params);
  }
}
class ExpressRequestException extends CoalesceError {
  constructor(...params) {
    super(ExpressRequestException, ...params);
  }
}

class HandleUSSDRequestException extends CoalesceError {
  constructor(...params) {
    super(HandleUSSDRequestException, ...params);
  }
}

class Server {
  constructor() {
    this._expressServer = app;
    //this._expressServer.use(express.json());
    this._expressServer.use(express.text({ type: "*/xml" }));
    this._expressServer.use(express.urlencoded({ extended: true }));
  }

  _configureExpressTimeouts(server) {
    let headersTimeout = parseInt(process.env.EXPRESS_HEADERS_TIMEOUT);
    let keepAliveTimeout = parseInt(process.env.EXPRESS_KEEP_ALIVE_TIMEOUT);

    /**
     * Only override keepAliveTimeout and headersTimeout if they are valid from the environment ...
     *   i.e. if they are not valid, then retain whetever the defaults are for `server` that is passed into this method
     */
    let validKeepAliveTimeout = !isNaN(keepAliveTimeout) && keepAliveTimeout >= MIN_TIMEOUT && keepAliveTimeout <= MAX_TIMEOUT
    if (validKeepAliveTimeout) {
      server.keepAliveTimeout = keepAliveTimeout;
    }

    const currentKeepAliveTimeout = server.keepAliveTimeout;

    let validHeadersTimeout = !isNaN(headersTimeout) && headersTimeout >= MIN_TIMEOUT && headersTimeout <= MAX_TIMEOUT
    if (validHeadersTimeout) {
      if (headersTimeout < currentKeepAliveTimeout) headersTimeout = currentKeepAliveTimeout
      server.headersTimeout = headersTimeout;
    }
  }

  async stop() {
    await this._expressServer.close();
  }

  async start() {

    return new Promise(async (resolve, reject) => {
      try {

        console.info("CI / CD Build anv Version Information");
        console.info("CI_BUILD_DATETIME: ", process.env.CI_BUILD_DATETIME);
        console.info("CI_BUILD_NUMBER: ", process.env.CI_BUILD_NUMBER);
        console.info("CI_COMMIT_REF: ", process.env.CI_COMMIT_REF);
        console.info("CI_GITHUB_TAG: ", process.env.CI_GITHUB_TAG);
        console.info("CI_BRANCH_NAME: ", process.env.CI_BRANCH_NAME);
        console.info("CI_DOCKER_TAG: ", process.env.CI_DOCKER_TAG);

        this._expressServer.get("/", (req, res) => this._k8sKeepAliveResponse(res));

        console.debug("ENVIRONMENT: ", JSON.stringify(process.env, null, 2));
        console.debug("Will start HTTP server on", HTTP_SERVER_PORT);

        if (isPreviewer) {
          console.info("Starting Previewer Debugger");
          previewerDebugger.start();
        } else {
          this.menuManager = new MenuManager();

          const menujson = process.env.MENU_JSON || "menu.json";
          console.debug(`Using menu configuration file: ${menujson}`);


          // const imenu = JSON.stringify(getMenuConfig().menu_code);

          // const menuConfig = JSON.parse(
          //     imenu
          //     .toString()
          //     .replace(/%NAMESPACE%/g, namespace)
          // );

          // await this.menuManager.load(menuConfig);

          this._expressServer.post(
            `${HTTP_SERVER_PATH}*`,
            this._serverMiddlewareLog,
            this._serverMiddlewareXMLParser,
            /**
             * Take Note ... if we don't put this inside a closure, then `this` context is lost within the handleRequest() method...
             *               Therefore we must put this in a closure...
             */
            (req, res) => this.handleRequest(req, res)
          );


        }

        this._expressServer.use((err, req, res, next) => {
          console.error("Express received an error (which will call _defaultResponse(req, res) method):", err);
          this._defaultResponse(req, res)
        });


        const server = http.createServer(this._expressServer)
        this._configureExpressTimeouts(server);

        server.listen(HTTP_SERVER_PORT, HTTP_SERVER_LISTEN_ADDR, () => {
          console.info("Current keepAliveTimeout value: ", server.keepAliveTimeout)
          console.info("Current headersTimeout value: ", server.headersTimeout)
          console.info(
            "Started Menu Server. " + //
            `Listening on http://${HTTP_SERVER_LISTEN_ADDR}:${HTTP_SERVER_PORT}`
          );
          resolve();
        });

        console.debug("Express Loaded", server);
      } catch (error) {
        console.fatal("🔥 Startup failed:", error.stack); // Logs stack trace
        let exception = new StartupException(error, "Startup failed.");
        reject(exception);
      }
    });
  }

  _serverMiddlewareLog(req, res, next) {

    console.info('Connection OPENED');
    const startTime = performance.now()

    const originalSend = res.send;



    res.send = function (data) {


      const { method, url, headers, socket, query, body } = req;
      const clientIp = headers['x-forwarded-for'] || socket.remoteAddress;
      const statusCode = res.statusCode;

      const logInfo = {
        method,
        url,
        statusCode,
        ip: clientIp,
        userAgent: headers['user-agent'],
        query,
        body,
      };


      // Check if response status is 503
      // Log the details of the dropped connection
      if (res.statusCode === 503) {
        console.warn(`Incoming connection dropped with 503`, logInfo);
      } else {
        console.info(`Incoming connection completed with ${res.statusCode}`, logInfo);
      }

      // Call the original send function with the original arguments
      originalSend.apply(res, [data]);
      const endTime = performance.now()
      console.info(`Connection CLOSED, completed in ${(endTime - startTime).toFixed(3)}ms`);
    };

    next();
  }

  _serverMiddlewareXMLParser(req, res, next) {
    try {
      res.setHeader("Connection", "close");

      if (typeof req.body === "object") {
        throw new InvalidXmlRpcRequestException("No XML body provided, did you use 'Content-Type: */xml' header?");
      }

      req.rawBody = String(req.body);
      if (req.rawBody === "") throw new InvalidXmlRpcRequestException("No XML body provided");

      // Annonyms async function to log the incoming XML raw body
      (async () => {

        try {
          console.trace("Incoming XML (raw)", req.rawBody.replace(/>\s+</g, '><').trim());
        } catch (error) {
          console.trace("Incoming XML (raw)", req.rawBody);
        }

      })();

      req.body = xmlrpc.parseRequest(req.body);

      next();

    } catch (error) {
      if (!(error instanceof InvalidXmlRpcRequestException)) {
        console.error("XMLRPC failed to parse incoming XML", error);
        error.message = "XML Parsing Failure";
      }
      const exception = new ExpressRequestException(error, "Could not parse XML in request");
      console.error(exception);
      const { xmlEncodedResponse, responseObject } = getXMLRPCFaultResponse(
        error.code || 500,
        error.message || "Internal Server Error"
      );
      console.debug("Sending this Object Fault response as XML:", JSON.stringify(responseObject));
      res.setHeader("Content-Type", "text/xml");
      res.send(xmlEncodedResponse);
    }
  }

  handleRequest(req, res) {
    try {
      let { body } = req;
      xmlrpc.fieldsAreValid(body, "handleUSSDRequest", [
        "TransactionId",
        "TransactionTime",
        "MSISDN",
        "USSDServiceCode",
        "USSDRequestString",
      ]);
      body.members.TransactionId = body.members.TransactionId || nanoid(CONST.NANOID_LENGTH);

      console.info(`Incoming HuX request parsed, USSDServiceCode: ${body.members.USSDServiceCode}, Transaction ID: ${body.members.TransactionId}, MSISDN: ${body.members.MSISDN}, USSDRequestString: ${body.members.USSDRequestString}`);
      Roarr.adopt(
        () => {
          this.handleUssdRequest(body, res);
        },
        {
          msisdnA: body.members.MSISDN,
          tid: String(body.members.TransactionId || nanoid(CONST.NANOID_LENGTH)),
        }
      );
    } catch (e) {
      if (e instanceof InvalidXmlRpcRequestException) {
        console.error("Faulty XML:", req.rawBody);
      }
      const exception = new ExpressRequestException(e, "Could not process incoming request");
      console.error(exception);
      console.error(e.stack);

      const { xmlEncodedResponse, responseObject } = getXMLRPCFaultResponse(e.code, e.message);

      console.error("Replying with fault:", JSON.stringify(responseObject));
      res.setHeader("Content-Type", "text/xml");
      res.setHeader("Connection", "close");
      /**
       * IMPORTANT NOTE: Status code is NOT changed from 200
       *                 This is because is an XMLRPC response, not an HTTP response
       */
      res.send(xmlEncodedResponse);
      //runGarbageCollector();
    }
  }

  async handleUssdRequest(request, res) {
    console.debug("Method: " + request.method);

    // make sure the connection closes when it is sent
    res.setHeader("Connection", "close");

    if (request.method === "handleUSSDRequest") {
      res.setHeader("Content-Type", "text/xml");

      const tid = String(request.members.TransactionId);


      // ----------- Get the menuManager class -----------
      /**
       * As the etcd will keep the menu launches, versions, ussd codes assigning to the menus up-to-date. So we will check if the menuManager is available by the given ussd code. If not it means the ussd code is not assigned to any menu. So we will return the fault response.
       */

      // USSD code
      const ussdCode = request.members.USSDServiceCode;

      // Get the menu data by the ussd code
      const menuData = await MenuData.local_getMenuDataByUssdCode(ussdCode);

      // If no menu data found by the ussd code then it means no menu is assigned to the ussd code so send the fault response xml
      if (!menuData.success) {
        const { xmlEncodedResponse, responseObject } = getXMLRPCFaultResponse(404, `No menu assigned to the "${ussdCode}" USSD code`);
        res.send(xmlEncodedResponse);
        return;
      }

      // We are here it means the ussd code is assigned to a menu so get the menu manager class
      const iMenuManager = menuData.data.menuManagerClass;

      // -------------------------------------------------

      iMenuManager
        .processRequest(request.members)
        .then((r) => {
          let xml = null;
          const [done, text, language, componentContext, serverOptions] = r;
				console.debug("[DEBUG] Server.js - processRequest result:", JSON.stringify(r));

          const { unlimitedUSSDResponseStringLength } = serverOptions || {}

          if (componentContext !== null && componentContext.respond && componentContext.raw) {
            //xml = xmlrpc.encode(componentContext.raw);
            xml = componentContext.raw;
          } else {
            let finalText = text
            if (finalText.length > USSD_REQUEST_STRING_LENGTH_LIMIT) {
              console.warn(`USSDResponseString too long (max ${USSD_REQUEST_STRING_LENGTH_LIMIT}), truncating.  Length was ` + finalText.length);
              console.warn(`Original message: ${finalText}`);
              if (unlimitedUSSDResponseStringLength !== true) {
                finalText = finalText.substr(0, USSD_REQUEST_STRING_LENGTH_LIMIT);
              }
            }
					console.debug("[DEBUG] Server.js - finalText:", JSON.stringify(finalText), "text:", JSON.stringify(text));
            const response = {
              TransactionId: tid,
              TransactionTime: new Date(),
              USSDResponseString: finalText,
              language,
            };

            response.ResponseCode = 1;
            if (!done) response.action = "request";

            console.trace("Replying with:\n", JSON.stringify(response));
            xml = xmlrpc.encode(response);
          }

          this._logMemoryUsageData('handleUssdRequest');

          res.send(xml)
          //runGarbageCollector();
        })
        .catch((e) => {
          const exception = new HandleUSSDRequestException(e, "Could not handle USSD Request");
          console.error("handleUssdRequest", exception);

          const { xmlEncodedResponse, responseObject } = getXMLRPCFaultResponse(e.code, e.message);
          console.error("Replying with fault:", JSON.stringify(responseObject));
          res.send(xmlEncodedResponse);
          //runGarbageCollector();
        });
    }
  }

  async _logMemoryUsageData(context = '') {

    try {


      const formatMB = (bytes) => `${Math.round((bytes / 1024 / 1024) * 100) / 100} MB`;

      const memoryData = process.memoryUsage();
      const heapStats = v8.getHeapStatistics();

      const memoryUsage = {
        rss: `${formatMB(memoryData.rss)} -> Resident Set Size - total memory allocated for the process execution`,
        heapTotal: `${formatMB(memoryData.heapTotal)} -> Total size of the allocated heap`,
        heapUsed: `${formatMB(memoryData.heapUsed)} -> Actual memory used during the execution`,
        external: `${formatMB(memoryData.external)} -> V8 external memory`,
        heapSizeLimit: `${formatMB(heapStats.heap_size_limit)} -> Max heap size allowed`,
        totalAvailableHeap: `${formatMB(heapStats.total_available_size)} -> Estimated available heap`,
        mallocedMemory: `${formatMB(heapStats.malloced_memory)} -> Malloced memory outside V8 heap`,
        nativeContexts: `${heapStats.number_of_native_contexts} -> Active V8 native contexts`,
        detachedContexts: `${heapStats.number_of_detached_contexts} -> Detached contexts (possible leaks)`,
      };


      console.info(`Memory Usage${context ? ` (${context})` : ''}:`, JSON.stringify(memoryUsage));

      return memoryUsage;

    } catch (error) {
      console.error("Error logging memory usage data:", error);
    }
  }


  _k8sKeepAliveResponse(res) {
    res.status(200).end("Response to Kubernetes Keep Alive: I am Alive!");
  }

  _defaultResponse(req, res) {
    console.debug("Request is not implemented", req);
    res.status(501).end("Not Implemented");
  }
}

const getXMLRPCFaultResponse = (faultCode, faultString) => {
  const responseObject = {
    // Due to unknown fault, probably can't get the original transactionid
    TransactionId: String(nanoid(CONST.NANOID_LENGTH)),
    TransactionTime: new Date(),
    faultCode: faultCode || 500,
    faultString: faultString || "Internal Server Error",
  };

  const xmlEncodedResponse = xmlrpc.encodeFaultResponse(responseObject);

  return { xmlEncodedResponse, responseObject };
};

module.exports = { Server, HandleUSSDRequestException, ExpressRequestException };