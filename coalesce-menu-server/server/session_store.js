const { Etcd3 } = require('etcd3');
const Context = require('./Context');

const CoalescError = require('@coalesce/coalesceError');
const { epochNow } = require('@coalesce/utils');

const etcdEndpoints = (process.env.CS_CONFIG_PEERS || 'http://localhost:2379').split(',');

const MIN_TTL_SECONDS = 3;
const MAX_TTL_SECONDS = 60 * 30;

class SessionStoreException extends CoalescError {
  constructor(...params) {
    super(SessionStoreException, ...params);
  }
}

class SessionStore {
  constructor(options) {
    try {
      this.options = options || {};
      if (isNaN(this.options.ttl)) throw new SessionStoreException('options.ttl must be specified and a number');

      this.etcd = new Etcd3({ hosts: etcdEndpoints });
    } catch (e) {
      throw new SessionStoreException(e, 'Critical: Failed to initialize session store');
    }
  }

  async set(key, value) {
    try {
      value.lastupdate = epochNow();
      const lease = this.etcd.lease(this.options.ttl);
      await lease.put(key).value(value.toString()).exec();
      lease.release();
    } catch (e) {
      throw new SessionStoreException(e, 'Could not set session value');
    }
  }

  async get(key) {
    try {
      const result = await this.etcd.get(key).exec();
      if (result.count == 0) return undefined;

      const value = result.kvs[0].value.toString();

      const ctx = Context.fromString(value);

      const ctxAge = epochNow() - ctx.lastupdate;
      if (ctxAge > this.options.ttl) {
        console.debug(`KEY: ${key} -- ttl expired: ${ctxAge} > ${this.options.ttl}`);
        await this.delete(key);
        return undefined;
      }

      await this.set(key, ctx); // updates TTL
      return ctx;
    } catch (e) {
      throw new SessionStoreException(e, 'Could not get session value');
    }
  }

  async delete(key) {
    return await this.etcd.delete().key(key).exec();
  }
}

module.exports = SessionStore;
