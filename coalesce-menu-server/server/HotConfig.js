const { Etcd3 } = require('etcd3');
const CoalesceError  = require('@coalesce/coalesceError') ;

const CONST_ETCD_RETRY_INTERVAL=process.env.ETCD_RETRY_INTERVAL || 30 * 1000

// TODO CS_CONFIG_PEERS seems an odd name for the ETCD URL
// We shouldn't be just using variables used by the ETCD distribution 
// just for the sake of it.
// how about CS_ETCD_INSTANCE or similar ?


class HotConfigException extends CoalesceError {
	constructor(...params) {
		super(HotConfigException , ...params);
	}
}



class HotConfig {
	constructor(hotConfigHosts){
		this.hosts = hotConfigHosts;
		this.etcd = null;

	}

	initialize(){
				if (this.etcd === null) {
					this.etcd = new Etcd3({hosts: this.hosts});
				}
	}


	async createAndWatch(key, defaultValue, updateConfig) {
		return new Promise(async (resolve,reject)=>{
			try {
				let value = await this.etcd.get(key).string();

				if (!key) throw new HotConfigException(null, `createAndWatch failed because key is not set`);

				if (value === null ) {
					if (defaultValue === null) {
						throw new HotConfigException(null, `createAndWatch failed because key '${key}' was not found in etcd and no defaultValue has been set`);
					}

					await this.etcd.put(key).value(defaultValue);
					value = defaultValue;
				}
				updateConfig(value, key);
				console.info(`Attempting to watch ETCD key ||${key}||`)
				this.etcd.watch()
					.key(key)
					.create()
					.then(watcher => {
						watcher
							.on('disconnected', () => console.info('ETCD client disconnected...'))
							.on('connected', () => {
								console.info('ETCD client successfully connected!');
							})
							.on('put', res => {
								updateConfig(res.value.toString(), key)
							});
					});
				resolve();
			}
			catch (e) {
				reject(new HotConfigException(e,"Failed to watch etcd for " + key + " on " + this.hosts+ "\n"));
			}
		});
	}
}

/** @type {HotConfig} */
let hotConfig = null;

const hotConfigHostsD = (process.env.CS_CONFIG_PEERS || "http://localhost:2379").split(",");

function initializeHotConfig(hotConfigHosts = hotConfigHostsD) {
	if (!hotConfig ) { 
		hotConfig =  new HotConfig(hotConfigHosts);
	}
	hotConfig.initialize();

	return hotConfig;
}

function getHotConfig(){
	if (!hotConfig) initializeHotConfig();
	return hotConfig;
}

module.exports =   {initializeHotConfig, getHotConfig}
