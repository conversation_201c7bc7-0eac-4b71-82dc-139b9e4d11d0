const Templates = {

    /**
     * Generate the HTML for the set log level route
     * @param {string} message 
     * @returns 
     */
    logLevelHtml: function (message) {
        return `
           <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Log Level</title>
    <style>
        /* Centering the box */
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f4f4f4;
            margin: 0;
            font-family: Arial, sans-serif;
        }

        /* Styled message box */
        .message-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 600px;
        }

        .message-box h2 {
            margin-bottom: 10px;
            font-size: 22px;
            color: #3b571c;
            border-bottom: 1px solid gray;
            padding-bottom: 15px;
        }

        .message-box p {
            font-size: 16px;
            color: #333;
        }

        span.colored {
            color: green;
        }

        span.colored.b {
            color: #035291;
        }

        span.colored.c {
            color:rgb(97, 0, 81);
        }
    </style>
</head>
<body>

    <div class="message-box">
        <h2>Log Level</h2>
        <p>${message}</p>
    </div>

</body>
</html>
        `;
    }

}

module.exports = Templates;