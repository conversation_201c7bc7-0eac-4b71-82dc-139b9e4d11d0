#!/usr/bin/env node
const { End, GotoEnd, Goto, Ask, Clear, SetUssdPath, Assign, AssignNull, JumpBack, Jump, Match, If, MenuIf, configureMessages, getGlobalMessageContent, getMessageContent, Response, Component, Step, Label, DisplayText, Display, CallMenu } = require("./ins")
const { Context } = require("./Context")
const { v4: uuidv4 } = require('uuid');

const CONST_GO_BACK = "__back";
const CONST_HAS_MENU_ENDING = true;
const CONST_PROGRESSES_MENU = false;
const CONST_MENU_RETRY = {
  description: "Invalid menu item retry option",
  content: {
    eng: "Retry",
    fra: "Retenter",
    ewe: "Retenter"
  }
};
const CONST_MENU_CANCEL = {
  description: "Invalid menu item cancel option",
  content: {
    eng: "Cancel",
    fra: "Annuler",
    ewe: "Annuler"
  }
};

class Script {
  constructor(cfg) {
    this.lines = [];
    this.labels = new Map();
    this.end = new End();
    this.response = new Response();
    this.ussdMatches = [];
    this.load(cfg);
  }

  dump() {
    console.debug("Steps: " + this.lines.length);
    for (const i in this.lines) {
      const line = this.lines[i];
      if (typeof line.asm !== 'function') {
        console.warn("dump() method :: Fault here :: typeof line.asm !== 'function'");
      }
      console.debug('%d: %s', i, line.asm());
    }
  }

  step(ctx) {
    //ctx.output = '';
    while (true) {
      //console.debug('SP: ' + ctx.sp);
      if (ctx.sp >= this.lines.length) {
        // bodged code to make it return false when running of the end of the lines array
        return false;
      }
      const r = this.lines[ctx.sp].execute(ctx);
      if (r == null) return false;

      if (typeof r === 'string') {
        if (r.startsWith(CONST_GO_BACK)) {
          let offset = parseInt(r.substr(CONST_GO_BACK.length))
          ctx.sp -= offset;
          continue;
        }
        else {
          ctx.sp = this.labels.get(r);
          continue;
        }
      }

      if (r > 0)
        ctx.sp += r;
      else
        break;
    }

    return true;
  }

  stepOver(ctx) {
    while (true) {
      const i = this.lines[ctx.sp];
      const r = i.execute(ctx);
      if (r == null) return false;

      if (typeof r === 'string') {
        ctx.sp = this.labels.get(r);
      }
      else {
        ctx.sp += r;
      }

      if (ctx.waiting) {
        break;
      }

      const j = this.lines[ctx.sp];
      if (j.id) {
        ctx.node_id = j.id;
        break;
      }
    }

    return true;
  }

  isComponentCall(ctx) {
    const s = this.lines[ctx.sp];
    return s instanceof Component ? s.name : undefined;
  }

  findStep(id) {
    for (let i = 0; i < this.lines.length; ++i) {
      if (this.lines[i].id === id)
        return i;
    }

    return -1;
  }

  asm(ctx) {
    return this.lines[ctx.sp].asm();
  }

  isValid() {
    if (this.lines.length > 0)
      return true;

    return false;
  }

  matchOneshotRequest(ussdString) {
    // Remove leading *, trailing # and split string into an array
    const request = ussdString.trim().slice(1, -1).split('*')
    const response = {
      gotolabel: null,
      matched: false,
      ussdVars: {}
    }
    for (const [index, match] of this.ussdMatches.entries()) {
      if (request.length === match.parts.length) {
        let oneshotmatch = true;
        for (const [index, part] of match.parts.entries()) {
          let matches = part.match(/^%([a-z0-9]+)%$/)
          if (null === matches) {
            // We are matching a fixed option
            if (part !== request[index]) {
              oneshotmatch = false
              break
            }
          }
          else {
            // We are processing a variable
            response.ussdVars[matches[1]] = request[index]
          }
        }
        if (oneshotmatch) {
          response.gotolabel = match.gotolabel
          response.matched = true
          break
        }
      }
    }
    return response
    /*
      Sample response
      response = {
        matched: true,
        gotolabel: "ussd_match_3",
        ussdVars: {
            msisdnb: "01238736",
            userpin: "0000"
        }
      }
    */
  }

  // private methods
  load(conf) {
    const cfg = typeof conf === 'string' ? JSON.parse(conf) : conf;
    
    configureMessages(cfg.messages, cfg.global_messages)

    for (const i in cfg.menus) {
      const menu = cfg.menus[i];
      this.lines.push(new Label(i));
      this.lines.push(...this.loadMenu(menu, CONST_HAS_MENU_ENDING));
    }
    this.lines.push(this.end);

    // Load oneshot matches from menu definition
    for (let i = 0; i < cfg.ussd_matches.length; ++i) {
      const match = cfg.ussd_matches[i].request.trim().slice(1, -1);// trim whitespace then remove leading * and trailing # from string
      const label = "ussd_match_" + i;

      const matchDetails = {
        gotolabel: "ussd_match_" + i,
        parts: match.split('*')
      }

      this.ussdMatches.push(matchDetails);
      this.lines.push(new Label(label));
      this.lines.push(...this.loadBody(cfg.ussd_matches[i].body));
      this.lines.push(this.end);
    }

    this.loadLabels();
  }

  loadLabels() {
    this.labels.clear();
    for (let i = 0; i < this.lines.length; ++i) {
      const l = this.lines[i];
      if (l instanceof Label) {
        console.debug('::%s:: => %d', l.target, i);
        this.labels.set(l.target, i);
      }
    }
  }

  loadMenu(cfg, hasend) {
    const lines = [];
    const displays = [];
    const invalidresponse = [];
    const labels = [];
    const components = [];
    const assignments = [];
    const menuLabelEnd = uuidv4();

    let itemCount = 0;
    for (const r of cfg.content) {
      if (r.label) {
        labels.push(new Label(r.label.name));
      }
      else if (r.callmenu) {
        lines.push(new CallMenu(r.callmenu));
      }
      else if (r.assign) {
        assignments.push(new Assign(r.assign));
      }
      else if (r.display) {
        displays.push(new Display(r.display));
      }
      else if (r.item) {
        const [header, code] = this.loadItem(r.item);
        itemCount++;
        displays.push(header);
        lines.push(...code);
        lines.push(new Goto({ target: menuLabelEnd }));
      }
      else if (r.component) {
        components.push(new Component(r.component));
        //lines.push(new Component(r.component));
      }
      else if (r.if) {
        displays.push(...this.loadMenuIf(r.if));
      }
      else if (r.end) {
        displays.push(new GotoEnd(r.end));
      }
    }

    invalidresponse.push(new DisplayText(getGlobalMessageContent('invalid_option'), undefined, '\n\n'));
    invalidresponse.push(new DisplayText(getGlobalMessageContent('menu_retry', CONST_MENU_RETRY), '1', '\n'));
    invalidresponse.push(new DisplayText(getGlobalMessageContent('menu_cancel', CONST_MENU_CANCEL), '2', '\n'));
    invalidresponse.push(this.response);

    invalidresponse.push(new MenuIf('2', 2));
    invalidresponse.push(new Jump(5));
    invalidresponse.push(new MenuIf('1', 2));
    invalidresponse.push(new Jump(2));
    invalidresponse.push(new JumpBack(invalidresponse.length));

    let menuRepeat = null;
    let finalmenu = null;
    if (hasend) { // only false when called from the menuIf statement.
      if (displays.length > 0 && itemCount > 0) {
        lines.push(new Jump(invalidresponse.length + 2));
        menuRepeat = [...displays, this.response, ...lines, ...invalidresponse];
      }
      else {
        menuRepeat = [...displays, ...lines];
      }

      if (displays.length > 0 && itemCount > 0) menuRepeat.push(new JumpBack(menuRepeat.length));
      finalmenu = [...assignments, ...components, ...labels, ...menuRepeat]
    }
    else {
      finalmenu = [...assignments, ...components, ...labels, ...displays, ...lines]
    }

    finalmenu.push(new Label(menuLabelEnd));

    return finalmenu;
  }

  loadMenuIf(cfg) {
    const lines = [];
    const t = this.loadMenu({ content: cfg.body }, CONST_PROGRESSES_MENU);
    //const f = this.loadMenu({content: cfg.else}, false);
    //t.push(new Jump(f.length + 1));

    lines.push(new If(cfg, t.length + 1));
    lines.push(...t);
    //lines.push(...f);
    return lines;
  }

  loadItem(cfg) {
    const header = new DisplayText(getMessageContent(cfg.message_id), cfg.selector);
    header.setNoSpace();

    const body = this.loadBody(cfg.body);
    //body.push(this.end);

    const lines = [];
    lines.push(new SetUssdPath(cfg));
    lines.push(new MenuIf(cfg.selector, body.length + 3));
    lines.push(...body);
    return [header, lines];
  }

  loadBody(cfg) {
    const lines = [];
    for (const i of cfg) {
      if (i.display) {
        lines.push(new Display(i.display));
      }
      else if (i.assign) {
        lines.push(new Assign(i.assign));
      }
      else if (i.callmenu) {
        lines.push(new CallMenu(i.callmenu));
      }
      else if (i.assign_null) {
        lines.push(new AssignNull(i.assign_null));
      }
      else if (i.clear) {
        lines.push(new Clear(i.clear));
      }
      else if (i.if) {
        lines.push(...this.loadIf(i.if));
      }
      else if (i.ask) {
        lines.push(new Display(i.ask));
        lines.push(new Ask(i.ask));
      }
      else if (i.goto) {
        lines.push(new Goto(i.goto));
      }
      else if (i.label) {
        lines.push(new Label(i.label.name));
      }
      else if (i.menu) {
        lines.push(...this.loadMenu(i.menu, CONST_HAS_MENU_ENDING));
      }
      else if (i.component) {
        lines.push(new Component(i.component));
      }
      else if (i.end) {
        lines.push(new GotoEnd(i.end));
      }
    }

    return lines;
  }

  loadIf(cfg) {
    const lines = [];
    const t = this.loadBody(cfg.body);
    const f = this.loadBody(cfg.else);
    t.push(new Jump(f.length + 1));

    lines.push(new If(cfg, t.length));
    lines.push(...t);
    lines.push(...f);
    return lines;
  }
}

function test() {
  const readlineSync = require('readline-sync')
    , config = require('../test/sample_menu2.json')
    ;

  ctx = new Context();
  script = new Script(config);

  script.dump();

  console.debug('Executing script:');
  while (script.step(ctx)) {
    console.debug('### MENU');
    ctx.input = readlineSync.question(ctx.output);
    ctx.output = '';
  }

  console.debug('ctx.output: ', ctx.output);
}

if (require.main === module)
  test();

module.exports = {
  Script,
  Context
}
