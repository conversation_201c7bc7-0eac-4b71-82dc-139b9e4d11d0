const fs = require("fs");
const path = require("path");
const { getGlobalMessageContent, getMessageContent, DisplayText } = require("./ins");
const requireFromString = require("require-from-string");
const kComponentsDir = path.join(__dirname, "components");
require("dotenv").config();

const { getHotConfig } = require("./HotConfig");
const CoalesceError = require("@coalesce/coalesceError");
const { ENDPOINTS_ETCD_KEY } = require("@coalesce/env");
const endpointConnectionIndexer = require("../common/@coalesce/EndpointConnectionIndexer");
const MenuData = require("./utils/menuData");


// ------------------- Load Builtin components from file -------------------

let builtInComponents = {};

{

  let dir = kComponentsDir;
  console.debug("Loading components from: " + dir);

  if (!fs.existsSync(dir)) throw new ComponentManagerException(null, "./components directory does not exists");

  const stat = fs.statSync(dir);
  if (!stat.isDirectory()) throw new ComponentManagerException(null, "./components is not a directory");

  const comps = fs.readdirSync(dir);
  for (const c of comps) {
    const stat = fs.statSync(path.join(dir, c));
    if (!stat.isDirectory()) continue;

    const componentFile = path.join(dir, c, "component.js");
    if (!fs.existsSync(componentFile)) {
      console.debug("Skipping Missing BUILT-IN Component: " + componentFile);
      continue;
    }

    const component = require(componentFile);
    if (component.init) {
      try {
        component.call({}, { new: true });
      } catch (e) {
        console.error(new ComponentManagerException(e, "Failed to initialize component"));
        continue;
      }
    }

    console.debug("Loading component: " + c);
    builtInComponents[c] = component.call;

  }

}



// ----------------------------------------------------------------------------


class ComponentManagerException extends CoalesceError {
  constructor(...params) {
    super(ComponentManagerException, ...params);
  }
}

class ComponentManager {
  constructor() {
    this.components = {};
    this.connectors = {};
    this.connectorData = {};

    this.componentCache = new Map();
  }

  newInstance() {
    const cm = new ComponentManager();

    cm.components = { ...this.components };
    cm.connectors = { ...this.connectors };
    cm.connectorData = this.connectorData;

    return cm;
  }

  getGlobalMessage(vars, id) {
    const display = new DisplayText(getGlobalMessageContent(id), undefined, "\n\n");
    const displayCtx = {
      variables: vars,
      output: "",
    };
    display.execute(displayCtx);
    return displayCtx.output;
  }

  getMessage(vars, id) {
    const display = new DisplayText(getMessageContent(id), undefined, "\n\n");
    const displayCtx = {
      variables: vars,
      output: "",
    };
    display.execute(displayCtx);
    return displayCtx.output;
  }

  async execute(ctx, componentName) {
    let result = {
      success: false,
    };

    // --------- Get the component ---------

    const ussdServiceCode = ctx.USSDServiceCode || ctx.variables.USSDServiceCode || ctx.request.USSDServiceCode;

    if (!ussdServiceCode) {
      // @todo-wahab: Call our custom log error function
      console.error("USSD Service Code not found in the context for context: ", ctx);
    }

    // First we will try to get from the this.components, because this object only contains the global components and if this server is previewer then it will contain the previewer components as well
    let component = this.components[componentName];

    // If it's not found in the global components then it means it can be user coded component (Coded on the gui), so we will get from the ussd code processed menu components
    if (!component) {

      // Get the component from the ussd code processed menu components
      const compResponse = await MenuData.local_getComponentByNameAndUssdCode(componentName, ussdServiceCode);

      // If success then set the component
      if (compResponse.success) {
        component = compResponse.data;
      } else {
        // Log the error
        console.error(`Error: #1234 -> Menu Component not found for the ussd code: ${ussdServiceCode} and for component name: ${componentName}`);
      }

    }

    // -------------------------------------

    console.debug("Starting debugger.execute() for Component Name: " + componentName);
    if (!component) {
      console.error("Component not loaded: %s", componentName);
      return result;
    }

    let componentContext = ctx.components.get(componentName);
    if (!componentContext) {
      ctx.componentName = componentName;
      componentContext = {
        components: new Map([...ctx.components]),
        menuState: {},
        request: ctx.request,
        ussdpath: ctx.ussdpath ? `${ctx.ussdpath}#` : "#",
        new: true,
        done: true,
        output: "",
        respond: false,
        raw: null,
        params: ctx.compParams,
        //connectors: this.connectors,
        getGlobalMessage: this.getGlobalMessage,
        getMessage: this.getMessage,
      };
      ctx.components.set(componentName, { ...componentContext });
    } else {
      componentContext.new = false;
      componentContext.request = ctx.request;
      //componentContext.connectors = this.connectors;
      componentContext.params = ctx.compParams;
    }

    // We would like to not change connectors info for sessions which are already in progress
    componentContext.connectors = ctx.connectors;
    componentContext.variables = ctx.variables || {};
    componentContext.input = ctx.input;

    try {
      await component(ctx.variables, componentContext);
    } catch (e) {
      let componentManagerError = new ComponentManagerException(
        e,
        "componentManager.execute(...) :: component(...) Failed"
      );
      console.error(componentManagerError);
      ctx.variables.result = componentManagerError.toString();
      componentContext.request.failed = true;
    }

    result.done = componentContext.done;
    result.ctx = componentContext;

    if (!componentContext.request.failed) {
      result.success = true;
    }

    if (componentContext.done) {
      //ctx.components.delete(componentName)
    }

    console.debug("Component finished: ", componentName);
    return result;
  }

  /*
    Used when loading connectors into debugger
    */

  loadConnectorsFromConfig(connectors) {
    if (typeof connectors != "object") throw new ComponentManagerException(null, "Invalid connectors object");

    this.connectors = connectors;
  }

  /*
    Used when loading components into debugger
    */
  loadFromConfig(components) {
    let modulePath = process.cwd() + "/common";
    for (const [name, code] of Object.entries(components)) {
      const c = requireFromString(code, name, {
        appendPaths: [modulePath],
      });
      if (c.init) c.init();

      this.components[name] = c.call;
    }
  }

  /*
    Used when loading connectors and components from filesystem
    */
  async load() {

    // Load the components
    this.loadComponents();

  }

  loadPreviewer() {
    this.loadComponents();
  }

  loadComponents() {

    // Add the built-in components
    this.components = { ...builtInComponents, ...this.components };
  }

  getEndpointsFor(type) {
    return this.connectors[type] || {}; // provides empty connector if invalid/empty type - i.e. no endpoints defined for this connector
  }

  getConnectorData(id) {
    return this.connectorData[id] || {};
  }
}

const componentManager = new ComponentManager();





// ------------ connectorsFromEtcd ------------

{

  /** The function to be called with the data when hotConfig changes */
  const setConnectorsFn = (connectorsRaw, etcdKey) => {
    if (!connectorsRaw) {
      throw new ComponentManagerException(
        null,
        `No endpoints information provided to retrieve. Corresponding Key: ${etcdKey}`
      );
    }

    const _connectorsFromEtcd = JSON.parse(connectorsRaw);

    for (const [connectorName, connector] of Object.entries(_connectorsFromEtcd)) {

      for (const [endpointName, endpoints] of Object.entries(connector)) {

        endpointConnectionIndexer.createConnectionIndex(connectorName + "." + endpointName, endpoints.length);
        connector[endpointName] = endpoints;
      }
    }


    // Update the componentManager with the new connectors
    componentManager.connectors = _connectorsFromEtcd;

  };

  // Ready the key and create the watcher
  const key = ENDPOINTS_ETCD_KEY;
  const hotConfig = getHotConfig();
  hotConfig.createAndWatch(key, null, setConnectorsFn);

}

// ---------------------------------------------



module.exports = componentManager;
