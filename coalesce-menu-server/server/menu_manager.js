const CoalescError = require('@coalesce/coalesceError');
const { getLanguageMap } = require("@coalesce/utils");
const componentManager = require('./componentManager');
const requireFromString = require("require-from-string");
const menu = require('./menu');
const Context = require('./Context')
const endpointConnectionIndexer = require('@coalesce/EndpointConnectionIndexer');
const SessionStore = require('./sessionStore-redis');
const { configureMessages } = require('./ins');

const CONST_SESSION_TTL_SECONDS = Number(process.env.MENU_SERVER_SESSION_TTL) || 5 * 60;

const languageMap = getLanguageMap();

// Init the subscriber session store
const subscriberSession = new SessionStore({ ttl: CONST_SESSION_TTL_SECONDS });

class MenuManagerException extends CoalescError {
	constructor(...params) {
		super(MenuManagerException, ...params);
	}
}


class MenuManager {

	constructor() {
		this.subscriberSession = subscriberSession

		/**  @type {import('./utils/menuData').Doc_MenuData_MenuData_Item["menu_code"]["messages"]} */
		this.messages = {}

		/** @type {Object.<string, function(ctx) => void} */
		this.components = {}
	}

	/**
	 * Load the configuration for the menu manager
	 * @param {import('./utils/menuData').Doc_MenuData_MenuData_Item["menu_code"]} config 
	 * @param {import('./utils/menuData').Doc_MenuData_MenuData_Item["components"]} components
	 * @param {Boolean} nodump 
	 */
	async load(config, components, nodump) {

		if (typeof config !== 'undefined') this.script = new menu.Script(config);
		if (typeof nodump === 'undefined') this.script.dump();

		// Set the messages, to so that we can track the messages ids in order to removed them from the object when updating the menu
		this.messages = config.messages;

		// Load the custom coded components
		this.loadCustomCodedComponents(components);

		await componentManager.load();
	}


	/**
	 * Load the custom coded (Component coded in the GUI) components into our this.components object
	 * @param {import('./utils/menuData').Doc_MenuData_MenuData_Item["components"]} components 
	 */
	loadCustomCodedComponents(components) {

		let modulePath = process.cwd() + "/common";
		for (const [name, code] of Object.entries(components)) {

			const c = requireFromString(code, name, {
				appendPaths: [modulePath],
			});
			if (c.init) c.init();

			this.components[name] = c.call;
		}
	}

	checkRequest(ctx, request) {
		let result = false;
		ctx.lastupdate = Math.floor(new Date() / 1000);
		if (typeof request.response === 'string' && request.response === 'false') result = true;
		ctx.output = '';
		return result;
	}

	/**
	 * create/update the subscriber context (session) & execute the request based upon different parameters
	 *
	 * @param {object} request
	 * @returns {Array} array containing 4 items ie, [done, text, language, componentContext, optionalSpecialObject]
	 * @example
	 * Input: {
			TransactionTime: Mon Sep 27 2020 04:21:44 GMT+0500
			MSISDN: "1234567",
			USSDRequestString: "#",
			response: "false",
			USSDEncoding: "GSM0338",
			USSDServiceCode: "111",
			TransactionId: "1000000006",
		}
	 * Returns: [false, '1- Balance oPackages to Moov\n4- Elite Package\n5- MEGA Promo\n', 'ENG', null]
	 */

	async executeRequest(request, requestvars) {
		let ctx;
		let lastComponentName = null; // Track the last component executed

		try {
			const MSISDN = request.MSISDN;

			let nextStep = true;
			const subscriberSessionKey = this.getSessionKey(request);
			ctx = await this.subscriberSession.get(subscriberSessionKey);

			if (!ctx || this.checkRequest(ctx, request)) {
				console.debug('Is NEW incoming request');
				ctx = new Context();
				ctx.variables.MSISDN = MSISDN;
				ctx.variables.language = request.LANG;
				ctx.variables.langmap = languageMap; // for i18n
				ctx.setvars(requestvars);

				ctx.connectors = {};

				let componentManagerConnectors = componentManager.connectors;

				ctx.connectors = endpointConnectionIndexer.flattenConnectionArray(componentManagerConnectors);
			}
			else if (request.response === 'true') {
				console.debug('Is CONTINUED request');
				ctx.input = request.USSDRequestString;
				ctx.setvars(requestvars);
				const componentName = this.script.isComponentCall(ctx);

				if (componentName) {
					let result = await componentManager.execute(Object.assign(ctx, { request }), componentName);
					if (!result.success) {
						await this.subscriberSession.delete(subscriberSessionKey);
						return [true, 'Internal server error', null, null];
					}

					nextStep = result.done
					console.trace("=== RESULT CONTEXT ===", result.ctx);
					if (!result.done) {
						await this.subscriberSession.set(subscriberSessionKey, ctx);

						let finalResult = [false, result.ctx.output, ctx.variables.language, result.ctx];
						/**
						 * WARNING: This is not fully tested, the ability to use unlimited length responses after a component call
						 */
						if (ctx.variables.unlimitedUSSDResponseStringLength === true) {
							finalResult.push({ unlimitedUSSDResponseStringLength: true })
						}
						console.debug('Completed componentManager.execute(...), returning...', finalResult);

						return finalResult;
					}
					ctx.output += result.ctx.output;
				}
			}

			if (nextStep) {
				console.debug('Going to nextStep in menu');
				ctx.input = request.USSDRequestString;
				if (typeof request.gotolabel === 'string') {
					ctx.sp = this.script.labels.get(request.gotolabel);
				}
				while (true) {
					let isDone = !this.script.step(ctx);
					let output = ctx.output;

					console.debug("Looping through steps, isDone / output:", isDone, output);

					if (isDone) {
						await this.subscriberSession.delete(subscriberSessionKey);
					}
					else {
						const componentName = this.script.isComponentCall(ctx);
						if (componentName) {
							lastComponentName = componentName; // Track the component name
							let result = await componentManager.execute(Object.assign(ctx, { request }), componentName);
							if (!result.success) {
								return [true, 'Internal server error', null, null];
							}

							/*
							* if user would set this variable (byPass) in the component
							* then xmlResponse (that will be sent back to the user) must be
							* constructed inside that component and assined to a variable called "raw"
							* So result.ctx must contain "raw" field
							* User's session will be terminated afterwards
							*/

							if (result.ctx.byPass) {
								const output = result.ctx.output || ''
								const componentCtx = result.ctx
								const lang = ctx.variables.language

								if (!componentCtx.raw || !componentCtx.respond) {

									/*
									* xmlResponse cannot constructed if any of these fields (respond or raw) are missing from component,
									* please check the component and make sure all required fields are present including "raw" field
									* containing xmlResponse that will be sent back to the user directly
									*/

									console.error(`missing required filds "raw/respond" in the component: ${componentName}`)

									return [true, 'Internal server error', null, null];
								}

								// remove user context and delete user
								ctx = undefined;
								await this.subscriberSession.delete(subscriberSessionKey);

								return [false, output, lang, componentCtx];
							}


							if (result.ctx.respond) {
								ctx.output += result.ctx.output;
								isDone = result.ctx.done
							}

							if (result.ctx.done)
								continue;
							else
								output = ctx.output;
						}
					}

					let finalResult = [isDone, output, ctx.variables.language, null];
					if (ctx.variables.unlimitedUSSDResponseStringLength === true) {
						finalResult.push({ unlimitedUSSDResponseStringLength: true })
					}
					// Add component name and variables for debugging/checking in processRequest
					finalResult.push({
						componentName: lastComponentName,
						enableAutoRespond: ctx.variables.enableAutoRespond,
						autoRespondValue: ctx.variables.autoRespondValue
					});
					console.info('Completed executeRequest(...), returning...', finalResult);

					if (isDone) await this.subscriberSession.delete(subscriberSessionKey);
					else await this.subscriberSession.set(subscriberSessionKey, ctx);

					return finalResult;
				}
			}
		}
		catch (err) {
			console.error('Execute Request failed', err)
			let executeRequestError = new MenuManagerException(err, "executeRequest failed");
			console.error(executeRequestError)
		}

		return [true, '', ctx.variables.language, null];
	}

	executeOneshot(request) {
		console.debug("[DEBUG] processRequest - START, request:", JSON.stringify(request));
		//let match = '*'+request.USSDServiceCode+request.USSDRequestString;
		let match = request.USSDRequestString;
		request.gotolabel = null;
		let result = this.script.matchOneshotRequest(match);
		if (result.matched) request.gotolabel = result.gotolabel;
		return result;
	}

	async processRequest(request) {
		let newrequest = Object.assign({}, request);
		let ussdCommands = newrequest.USSDRequestString.toString().split('*');
		let result = null;
		let oneshot = this.executeOneshot(newrequest)

		const subscriberSessionKey = this.getSessionKey(request);

		if (ussdCommands.length > 1 && !oneshot.matched) {
			// This is not a ineshiot request
			newrequest.response = "false";
			console.debug("[DEBUG] processRequest - taking session path");
			console.debug("[DEBUG] processRequest - subscriberSessionKey:", subscriberSessionKey);

			for (let i = 0; i < ussdCommands.length; i++) {
			console.debug("[DEBUG] processRequest - ussdCommands:", JSON.stringify(ussdCommands), "length:", ussdCommands.length);
				if (result) result[1] = '';
				if (ussdCommands[i] === '') {
					delete newrequest.USSDRequestString;
				}
				else {
					if (ussdCommands[i].endsWith("#"))
						newrequest.USSDRequestString = ussdCommands[i].substr(0, ussdCommands[i].length - 1);
					else
						newrequest.USSDRequestString = ussdCommands[i];
				}

				console.debug("[DEBUG] processRequest - about to call executeRequest for iteration:", i);
				console.debug("[DEBUG] processRequest - newrequest:", JSON.stringify(newrequest));

				// Get session context BEFORE executeRequest (in case it gets deleted)
				let sessionContextBeforeExecution = null;
				try {
					sessionContextBeforeExecution = await this.subscriberSession.get(subscriberSessionKey);
					console.debug("[FIX] Session context before execution:", sessionContextBeforeExecution ? JSON.stringify(sessionContextBeforeExecution.variables) : "null");
				} catch (e) {
					console.debug("[FIX] Could not get session context before execution:", e.message);
				}

				result = await this.executeRequest(newrequest);
				console.debug("[DEBUG] processRequest - session path executeRequest returned:", JSON.stringify(result));
				const [isDone] = result;
				console.debug("[DEBUG] processRequest - component finished but more commands to process");
				if (isDone && i < (ussdCommands.length - 1)) {
					// Check if this is a HUX component with auto-respond scenario
					const [, output, , componentContext, serverOptions, componentInfo] = result;
					console.debug("[FIX] Debugging - componentContext:", JSON.stringify(componentContext));
					console.debug("[FIX] Debugging - output:", JSON.stringify(output));
					console.debug("[FIX] Debugging - componentInfo:", JSON.stringify(componentInfo));

					// Check if this is a HUX component with auto-respond enabled
					const hasValidOutput = output && typeof output === "string" && output.trim().length > 0;
					const isHuxComponent = componentInfo && componentInfo.componentName &&
										  componentInfo.componentName.toLowerCase().includes("hux");
					const hasAutoRespond = componentInfo && componentInfo.enableAutoRespond &&
										  componentInfo.autoRespondValue !== undefined;

					console.debug("[FIX] hasValidOutput:", hasValidOutput, "isHuxComponent:", isHuxComponent, "hasAutoRespond:", hasAutoRespond);

					if (isHuxComponent && hasAutoRespond && hasValidOutput) {
						console.debug("[FIX] HUX auto-respond component with valid output, using component result:", output.substring(0, 50) + "...");
						return [true, output, null, null, serverOptions];
					} else {
						console.debug("[FIX] Not HUX auto-respond scenario, using 'Invalid USSD Command'");
						return [true, 'Invalid USSD Command', null, null];
					}
				}
				if (isDone) {
					await this.subscriberSession.delete(subscriberSessionKey);
				}
				newrequest.response = "true";
			}
		}
		else {
			console.debug('processing oneshot request');
			result = await this.executeRequest(newrequest, oneshot.ussdVars);
			console.debug("[DEBUG] processRequest - about to call executeRequest");
			const [isDone, output, , , serverOptions] = result;
			console.debug("[DEBUG] processRequest - executeRequest returned:", JSON.stringify(result));
			if (isDone) {
				console.debug("[DEBUG] processRequest - isDone:", isDone, "output:", JSON.stringify(output), "output.length:", output.length);
				let msg = output.length > 0 ? output : 'Invalid USSD Command'
				console.debug("[DEBUG] processRequest - msg will be:", msg);
				await this.subscriberSession.delete(subscriberSessionKey);
				/**
				 * We are handling SERVER OPTIONS here.
				 *  If a CAD adds very specific variables in ctx.variables
				 *  we will validate these as special server options
				 *  
				 *  At the time of coding this, the only entry in 'serverOptions' is:
				 *   { unlimitedUSSDResponseStringLength: true }
				 */
				return [true, msg, null, null, serverOptions];
			}
		}

		return result;
	}


	/**
	 * Ready the session key by the request
	 */
	getSessionKey(request) {
		return request.MSISDN
		//return `${request.MSISDN}:${request.USSDServiceCode}`;
	}
}

module.exports = MenuManager;
