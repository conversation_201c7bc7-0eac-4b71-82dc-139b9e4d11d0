const util = require('util');
const Step = require('./Step');

const xmlrpc = require('@coalesce/xmlrpc')
const { sendRequest, getOptions } = require("@coalesce/request");

module.exports = class CallMenu extends Step {
	constructor(cfg) {
		super();
		this.hostname = cfg.hostname
		this.requestString = cfg.requestString
		this.variable = cfg.variable
	}

	/*const params =  {
			TransactionId: 123,
			TransactionTime: new Date(),
			MSISDN: '27821234573',
			USSDServiceCode: '9999',
			USSDRequestString: '*LANGUAGEID#',
			USSDEncoding: 'GSM0338',
			response: false
		};
	*/

	 execute(ctx) {
		let msisdn = ''
		let port = 20000
		if (typeof ctx.variables.MSISDN === 'string') msisdn = ctx.variables.MSISDN
		const params =  {
			TransactionId: String( Math.floor(Math.random() * 1000000) + 100000000 ),
			TransactionTime: new Date(),
			MSISDN: msisdn,
			USSDServiceCode: '9999',
			USSDRequestString: this.requestString,
			USSDEncoding: 'GSM0338',
			response: 'false'
		};

		const huxurl = `http://${this.hostname}:${port}/RPC2`;

		const body = xmlrpc.encodeRequest('handleUSSDRequest', params);

		let { headers, timeout } = getOptions({});

		let headersText = JSON.stringify(headers)

		console.debug(`HuX request URL ${huxurl}`);
		console.trace(`HuX request content ${body}`);
		console.debug(`HuX request headers ${headersText}`);
		let xml = null;
		try {
			xml = sendRequest({ url: huxurl, body, headers, timeout });
		}
		catch(err) {
			console.error(err)
		}
		
		console.trace('HuX response content', xml);

		const response = xmlrpc.parseResponse(xml);
		console.trace('Parsed Hux Response', response);

		ctx.output += response.USSDResponseString;
		
		return 1
	}

	asm() {
		return util.format("CallMenu %s with requestString: %s, response variable %s", this.name, this.requestString, this.variable);
	}
}