
const messages = {
	kb: {
		ENG: 'KB'
		, FRA: 'KO'
	},
	mb: {
		ENG: 'MB'
		, FRA: 'MO'
	},
	gb: {
		ENG: 'GB'
		, FRA: 'GO'
	}
};

const KB = 1024;
const MB = 1024 * KB;
const GB = 1024 * MB;

function min(a) { return Math.min(...a); }
function max(a) { return Math.max(...a); }
function sum(a) { return a.reduce((i, j) => i + Number(j), 0); }
function data(a) {
	if (a > GB) return String((a/GB).toFixed(2)) + ' %gb%';
	if (a > MB) return String((a/MB).toFixed(2)) + ' %mb%';
	return String((a/KB).toFixed(2)) + ' %kb%';
}

function now() { return new Date; }

function evaluate(vars, exp) {
	//console.debug('## Evaluate:', exp, vars);
	const s = `with (vars) { ${exp}; }`;
	//console.debug('EVAL', s);
	return eval(s);
}

function expandText(ctx, text) {

	return text ? (typeof text == 'string' ? 
			text.replace(/\$(.+?)\$/g, (m, k) => evaluate(ctx.variables, k))
			//.replace(/%(.+?)%/g, (m, k) => ctx.variables[k] || m);
			.replace(/%(.+?)%/g, (m, k) => ctx.variables[k] || (messages[k] ? messages[k][ctx.variables.language] : m)) : text) : undefined;
}

module.exports.expandText = expandText