const util = require('util');
const Step = require('./Step');
const {expandText} = require("./textUtil")

const lang = process.env.DEFAULT_LANGUAGE||'eng';

module.exports = class DisplayText extends Step {
	constructor(content, prefix, suffix) {
		super();
		this.content = content
		this.msg = content[lang];
		this.prefix = prefix;
		this.suffix = suffix;
		this.nospace == false;
	}

	setNoSpace() {
		this.nospace = true;
	}

	execute(ctx) {
		//console.debug('DISPLAY:', this.msg, ctx.variables);
		let currentLang = lang
		if (typeof ctx.variables.language === 'string') {
			currentLang = ctx.variables.language.toLowerCase()
			this.msg = this.content[currentLang]
		}

		if (typeof this.prefix === 'string') {
			ctx.output += this.prefix;
			if (!this.nospace) ctx.output += ' ';
		}

		ctx.output += expandText(ctx, this.msg);

		if (typeof this.suffix === 'string') {
			ctx.output += this.suffix;
		}

		return 1; 
	}

	asm() {
		return util.format('print ("%s")', (typeof this.msg === 'undefined')?'':this.msg.trim());
	}
}