const { GLOBAL_MESSAGES_ETCD_KEY } = require("../../common/@coalesce/env");
const { getHotConfig } = require("../HotConfig");

let messages = {}


function deleteMessageByKey(key) {

	try {
		delete messages[key];
	} catch (err) { }
}

// --------------------- Global messages -------------------------
let globalMessages = { "invalid_option": { "description": "Subscriber has selected an invalid option", "content": { "eng": "Invalid choice. try again..", "fra": "Choix invalide. Réessayer.", "ewe": "Choix invalide. Réessayer." } }, "no_ussd_match": { "description": "No USSD request has matched", "content": { "eng": "Invalid USSD code.", "fra": "Code USSD non valide.", "ewe": "Code USSD non valide." } }, "menu_retry": { "description": "Invalid menu item retry option", "content": { "eng": "Retry", "fra": "Retenter", "ewe": "Retenter" } }, "menu_cancel": { "description": "Invalid menu item cancel option", "content": { "eng": "Cancel", "fra": "Annuler", "ewe": "Annuler" } }, "service_unavailable": { "description": "Remote service is not accessible", "content": { "eng": "Service Unavailable", "fra": "Service Indisponible", "ewe": "Service Indisponible" } } }


// Ready the key and create the watcher for the global messages
const hotConfig = getHotConfig();
hotConfig.createAndWatch(GLOBAL_MESSAGES_ETCD_KEY, null, (rawData, etcdKey) => {


	try {
		if (rawData) {

			const idata = JSON.parse(rawData);

			if (typeof idata === 'object') {
				globalMessages = idata
				console.info(`Global Messages are updated!`)
			}

		}
		
	} catch (err) {
		console.error(`Error while parsing the global messages: ${err}`)
	}

});
// ----------------------------------------------------------------


const CONST_MENU_EMPTY = {
	description: "",
	content: {
		eng: "",
		fra: "",
		ewe: ""
	}
};

function configure(msgs) {

	/**
	 * Note: K&8 decoupling.
	 * Regarding the decoupling from the k&8, I carefully check the codes the Phenomina for the messages are as following:
	 * 	-1. When the MenuManager load function called then it loads the messages using the configure function.
	 * 	-2. Then all of the menu items, messages display etc are loaded into the object using the getMessageContent function.
	 * 
	 * However while I was tracing the the usage of the getMessageContent, that it is adding on the context and passed to the custom coded component. So if the custom coded
	 * function uses the get message content then it will get the message from the object.
	 * So I only refactored the below code to update/add the messages to the object instead of replacing the object
	 * as the messages ids are 122 Random Bits so it's not possible for repeating message id even after generating 1 billion ids. So this way it is safely decoupled.
	 * At the same time if we have different message for the same id then it will be updated.
	 */
	messages = { ...msgs }
}

function getGlobalMessageContent(id, msg) {
	//console.debug('Global Message ID: ', id);
	let message = CONST_MENU_EMPTY.content;
	if (typeof globalMessages[id] !== 'undefined' && typeof globalMessages[id].content !== 'undefined') {
		message = globalMessages[id].content;
	}
	else {
		if (typeof msg !== 'undefined' && typeof msg.content !== 'undefined') {
			message = msg.content;
		}
	}

	//if (typeof message === 'undefined') message = CONST_MENU_EMPTY;
	return message;
}

function getMessageContent(id) {

	//console.debug('Message ID: ', id);
	return messages[id].content;
}



module.exports.getGlobalMessageContent = getGlobalMessageContent
module.exports.getMessageContent = getMessageContent
module.exports.configure = configure
module.exports.deleteMessageByKey = deleteMessageByKey