const util = require('util');
const Response = require('./Response');

module.exports = class Ask extends Response {
	constructor(cfg) {
		super();
		this.variable = cfg.variable;
	}

	execute(ctx) {
		if (ctx.waiting) {
			ctx.variables[this.variable] = ctx.input;
			ctx.waiting = false;
			return 1;
		}

		ctx.waiting = true;
		return 0;
	}

	asm() {
		return util.format("%s = INPUT", this.variable);
	}
}