const util = require('util');
const Step = require('./Step');
const {expandText} = require("./textUtil")

module.exports = class Assign extends Step {
	constructor(cfg) {
		super()
		this.id = cfg.node_id;
		this.value = cfg.value;
		this.variable = cfg.variable;
	}

	execute(ctx) {
		//ctx.variables[this.variable] = this.value;
		ctx.variables[this.variable] = expandText(ctx, this.value);
		
		return 1;
	}

	asm() {
		return util.format("%s = %s", this.variable, this.value);
	}
}