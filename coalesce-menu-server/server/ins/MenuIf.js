const util = require('util');
const Step = require('./Step');

const CONST_IGNORE_MATCH_CHARS='.-:_ ';

module.exports = class MenuIf extends Step {
	constructor(selector, skip) {
		super();
		this.selector = selector;
		this.skip = skip;
	}

	/*
	*/
	sanitize(content) {
		// FIXME
		// Observed error on site from content ... where 'split' indicates the content is _undefined_
		// we should further sanitize by converting content to an empty string if undefined...
		for (var i=0; i<CONST_IGNORE_MATCH_CHARS.length; i++) {
			let match = CONST_IGNORE_MATCH_CHARS[i];
			content = content.split(match).join('');
		}
		return content;
	}

	execute(ctx) {
		ctx.input = this.sanitize(ctx.input);
		this.selector = this.sanitize(this.selector);
		if (ctx.input == this.selector)
			return 1;

		return this.skip;
	}

	asm() {
		return util.format("if input == %s, else jump: %d", this.selector, this.skip);
	}
}