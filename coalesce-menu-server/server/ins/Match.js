const util = require('util');
const Step = require('./Step');

module.exports = class Match extends Step {
	constructor(cfg, skip) {
		super()

		this.id = cfg.node_id;
		this.match = cfg.match
		this.skip = skip;
	}

	execute(ctx) {
		const value = ctx.match;
		// TODO match here
		const result = this.op(value);

		if (result)
			return 1;

		return this.skip;
	}

	asm() {
		return util.format("Match %s else jump: %d", this.match, this.skip);
	}
}