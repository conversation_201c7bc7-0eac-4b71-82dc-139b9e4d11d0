const Response = require('./Response');
const util = require('util');

module.exports = class Component extends Response {
	constructor(cfg) {
		super();
		this.calltype = cfg.calltype||'none'
		if (this.calltype === 'menu') {
			this.name = cfg.name;
			this.params = {
				name: cfg.name,
				calltype: cfg.calltype||'none',
				hostname: cfg.hostname,
				requestString: cfg.requestString,
				variable: cfg.variable
			}
		}
		else {
			this.params = {
				calltype: cfg.calltype||'none'
			};
			this.id = cfg.node_id;
			this.name = cfg.name;
			this.config = cfg.config;
			this.args = cfg.args;
		}
		if (!this.args) this.args = {};
	}

	execute(ctx) {
		if (ctx.waiting) {
			ctx.waiting = false;
			return 1;
		}

		for (const [k, v] of Object.entries(this.args))
			ctx.variables[k] = ctx.variables[v];

		ctx.compParams = this.params;
		ctx.waiting = true;
		return 0;
	}

	asm() {
		return util.format("COMPONENT %s", this.name);
	}
}