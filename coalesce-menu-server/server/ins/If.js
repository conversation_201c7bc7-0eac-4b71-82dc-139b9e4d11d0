const util = require('util');
const Step = require('./Step');
const {expandText} = require("./textUtil")

module.exports = class If extends Step {
	constructor(cfg, skip) {
		super()

		this.id = cfg.node_id;
		this.operator = cfg.operator
		this.variable = cfg.variable
		this.rhsValue = cfg.value == 'undefined' ? undefined : cfg.value
		this.skip = skip;

		const eq = (v, q) => { return q === v; }
		const ne = (v, q) => { return q !== v; }
		const lt = (v, q) => { return v < q; }
		const le = (v, q) => { return v <= q; }
		const gt = (v, q) => { return v > q; }
		const ge = (v, q) => { return v >= q; }

		this.op = function() { return false; }
		switch (cfg.operator) {
			case 'eq':
			case '=':
				this.op = eq;
				break;

			case 'ne':
			case '!=':
				this.op = ne;
				break;

			case 'lt':
			case '<':
				this.op = lt;
				break;

			case 'le':
			case '<=':
				this.op = le;
				break;

			case 'gt':
			case '>':
				this.op = gt;
				break;

			case 'ge':
			case '>=':
				this.op = ge;
				break;
		}
	}

	execute(ctx) {
		let varval = ctx.variables[this.variable];

		let rval = expandText(ctx, this.rhsValue);
		if (typeof rval === 'string') {
			try {
				let tmp = parseInt(rval);
				if (!isNaN(tmp)) rval = tmp
			}
			catch(err){}
		}
		if (typeof varval === 'string') {
			try {
				let tmp = parseInt(varval);
				if (!isNaN(tmp)) varval = tmp
			}
			catch(err){}
		}

		const result = this.op(varval, rval);

		if (result)
			return 1;

		return this.skip;
	}

	asm() {
		return util.format("if %s %s N, else jump: %d", this.variable, this.operator, this.skip);
	}
}