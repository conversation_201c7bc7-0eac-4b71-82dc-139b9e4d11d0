const { Server } = require("./Server.js");
const server = new Server();
const v8 = require("v8");

setTimeout(function () {
  server
    .start()
    .then(() => {
      const totalHeap = v8.getHeapStatistics().total_available_size;
      const totalHeapSizeInGB = (totalHeap / 1024 / 1024 / 1024).toFixed(2);
      console.info(`nodejs total heap memory size = ${totalHeapSizeInGB} GB`);
    })
    .catch((error) => {
      if (error.innerError) {
        console.error("Caused by: " + error.innerError.toString() + "\n" + error.innerError.stack);
        console.error(`full server Startup error`, error);
      }

      console.fatal("Server Startup FAILED: " + error.toString() + "\n" + error.stack);

      process.exit(1);
    });
}, process.env.WAIT_FOR_ISTIO_PROXY || 5000);
