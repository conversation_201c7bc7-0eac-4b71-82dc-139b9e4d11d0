const { redisKeys, redisClient, logRedisClusterInfo } = require('./utils/redis-client');
const Context = require('./Context');
const { redisKey } = require('./utils/redis-client');

const CoalescError = require('@coalesce/coalesceError');
const { epochNow } = require('@coalesce/utils');

const MIN_TTL_SECONDS = 3;
const MAX_TTL_SECONDS = 60 * 30; // 30 minutes

class SessionStoreException extends CoalescError {
  constructor(...params) {
    super(SessionStoreException, ...params);
  }
}

/**
 * Session store implementation using Redis
 */
class SessionStoreRedis {
  constructor(options) {
    this.redisClient = redisClient.getClient.bind(redisClient);
    try {
      this.options = options || {};
      if (isNaN(this.options.ttl)) {
        throw new SessionStoreException('options.ttl must be specified and a number');
      }
    } catch (e) {
      throw new SessionStoreException(e, 'Critical: Failed to initialize session store');
    }
  }

  /**
   * Sets a session value in Redis
   * @param {string} key - The key to set the session value for
   * @param {Object} value - The value to set in the session
   * @throws {SessionStoreException} If the session value cannot be set
   */
  async set(key, value) {

    key = redisKeys.session(key);

    console.info(`[Redis] Session, setting session for the key "${key}"`);

    try {
      value.lastupdate = epochNow();
      const stringValue = value.toString();
      await this.redisClient().setex(key, this.options.ttl, stringValue); // Set key with expiry
    } catch (e) {
      throw new SessionStoreException(e, 'Could not set session value');
    }
  }
  

  /**
   * Retrieves a session value from Redis
   * @param {string} key - The key to retrieve the session value for
   * @returns {Object} The session value
   * @throws {SessionStoreException} If the session value cannot be retrieved
   */
  async get(key) {
    key = redisKeys.session(key);

    console.info(`[Redis] Session, getting session for the key "${key}"`);

    try {
      const client = this.redisClient();
      const value = await client.get(key);

      if (!value) return undefined;

      // Refresh TTL
      await client.expire(key, this.options.ttl);

      const ctx = Context.fromString(value.toString());
      return ctx;
    } catch (e) {
      throw new SessionStoreException(e, 'Could not get session value');
    }
  }

  /**
   * Deletes a session value from Redis
   * @param {string} key - The key to delete the session value for
   * @returns {boolean} True if the session value was deleted, false otherwise
   * @throws {SessionStoreException} If the session value cannot be deleted
   */
  async delete(key) {
    key = redisKeys.session(key);

    console.info(`[Redis] Session, deleting session for the key "${key}"`);

    try {
      return (await this.redisClient().del(key)) > 0;
    } catch (e) {
      throw new SessionStoreException(e, 'Could not delete session value');
    }
  }
}

module.exports = SessionStoreRedis;
