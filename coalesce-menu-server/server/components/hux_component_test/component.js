const hux = require("@coalesce/hux");

async function call(vars, ctx) {
  console.info("Call 'hux_component_test' custom Component");

  hux.setEndpoint("ecds", ctx);

  // Helper to generate a timestamp in UTC (+0200 offset) .
  // Format: YYYYMMDDThh:mm:ss+0200
  // Uncomment and use `TransactionTime: getTransactionTimeWithOffset(120)`
  // if you get issue like request is stale or the service you are connecting maybe in different time zone and your next menu request might be rejected.
  // function getTransactionTimeWithOffset(offsetMinutes = 120) {
  //   const now = new Date(Date.now() + offsetMinutes * 60 * 1000);
  //   const pad = (n) => (n < 10 ? "0" + n : n);

  //   const YYYY = now.getUTCFullYear();
  //   const MM = pad(now.getUTCMonth() + 1);
  //   const DD = pad(now.getUTCDate());
  //   const hh = pad(now.getUTCHours());
  //   const mm = pad(now.getUTCMinutes());
  //   const ss = pad(now.getUTCSeconds());

  //   return `${YYYY}${MM}${DD}T${hh}:${mm}:${ss}+0200`;
  // }

  if (!ctx.request) ctx.request = {};

  const ussdCode = vars.huxcode;
  ctx.request.USSDServiceCode = ussdCode;

  const additionalParams = {
   // TransactionTime: getTransactionTimeWithOffset(120),
  };

  const enableAutoRespond = vars.enableAutoRespond;
  const autoRespondValue = vars.autoRespondValue;

  try {
    // Step 1: Send initial request
    const response = await hux.sendUSSD(ussdCode, ctx, additionalParams);
    const responseStr = response.USSDResponseString || "";

    console.debug("[HUX Component] Initial USSD response:", responseStr);

    const shouldAutoRespond =
      enableAutoRespond &&
      typeof autoRespondValue !== "undefined";

    if (shouldAutoRespond) {
      console.debug(`[HUX Component] Auto-responding with '${autoRespondValue}'...`);

      const originalServiceCode = ussdCode;
      const originalTransactionId = ctx.request.TransactionId;

      // Setup request context for follow-up
      ctx.new = false;
      ctx.request.response = true;
      ctx.request.USSDRequestString = autoRespondValue;
      ctx.request.USSDServiceCode = originalServiceCode;
      ctx.request.TransactionId = originalTransactionId;

      const confirmParams = {
       // TransactionTime: getTransactionTimeWithOffset(120),
      };

      console.debug("HUX component TT", confirmParams.TransactionTime);

      const confirmResponse = await hux.sendUSSD(
        ctx.variables.huxcode,
        ctx,
        confirmParams
      );

      ctx.output = confirmResponse.USSDResponseString || "Auto-response done.";
      ctx.respond = confirmResponse.action === "request";
      return { done: confirmResponse.action !== "request", success: true, ctx };
    }

    // Step 3: Normal flow
    ctx.output = responseStr || "No response.";
    ctx.respond = response.action === "request";
    return { done: response.action !== "request", success: true, ctx };

  } catch (e) {
    console.error("[HUX Component] Error during USSD request:", e);
    ctx.output = "An error occurred while processing your request.";
    return { done: true, success: false, ctx };
  }
}

module.exports = { call };
