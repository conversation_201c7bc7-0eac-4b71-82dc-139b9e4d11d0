
const hux = require('@coalesce/xmlrpc')
	, request = require('@coalesce/request')
;


const CoalesceError = require("@coalesce/coalesceError");

class PccGWError extends CoalesceError {
	constructor(...params) {
		super(PccGWError,...params);
	}
}


// const langs = new Map([
// 	[1, 'eng']
// 	[2, 'fra']
// ]);

let componentConfig = {
	"pcc" : {
		"url": "http://127.0.0.1:4021/RPC2"
	}
	, "air": {
		"url": "http://127.0.0.1:10011/Air"
		, "nodetype": "EXT"
		, "hostname": "hxc"
		, "nai": 2
	}
	, "langs": ["eng", "eng", "fra"]
}

async function call(vars, ctx) {

	// Initialisation run
	if (!ctx.request) return;

	console.info("Calling 'pccgw' built-in component");

	let r;
	let xml = hux.encodeRequest('handleUSSDRequest', {
		TransactionId: getTransactionId()
		, TransactionTime: new Date
		, MSISDN: ctx.request.MSISDN
		, USSDServiceCode: vars.ussd.code
		, USSDRequestString: vars.ussd.string
		, response: 'false'
	});

	let msg;
	try {
		console.debug('Sending request to pcc', xml);
		xml = await postRequest({ url: componentConfig.pcc.url, body: xml });
		r = hux.parseResponse(xml);
		console.debug('PCC reponse', r);

		msg = r.USSDResponseString;
	}
	catch (e) {
		let pccGWError  = new PccGWError(e,'Failed to send HuX request')
		console.error(pccGWError);  
		vars.result = 'Network error';
		return;
	}

	// PCC replys with 101 if sucriber is not in 'ash_subscription'
	if (r.ResponseCode != 101) {
		// just forward it
		vars.result = r.USSDResponseString;
		return;
	}

	// msisdn = subs.get(r.TransactionId); 
	// if (!msisdn) {
	// 	console.error('Unknown TID:', r.TransactionId);
	// 	res.statusCode = 400;
	// 	res.end();
	// 	return;
	// }

	r = setupUcipRequest(ctx.request.MSISDN, componentConfig.air);
	//r.dedicatedAccountSelection = true;
	r.requestActiveOffersFlag = true;

	xml = hux.encodeRequest('GetBalanceAndDate', r);
	//console.debug(xml);
	console.debug('Sending UCIP request');

	try {
		xml = await postRequest({ url: componentConfig.air.url, body: xml });
	}
	catch (e) {
		let pccGWError  = new PccGWError(e,'Failed to send UCIP request');
		console.error(pccGWError  );
		vars.result = 'Network Error';
		return;
	}

	console.debug('UCIP response received');
	r = hux.parseResponse(xml);
	//console.debug('AIR reponse', r);

	if (r.responseCode == 102) {
		console.debug('AIR: subscriber does not exists');
		vars.result = msg;
		return;
	}

	if (r.dedicatedAccountInformation)
		for (const da of r.dedicatedAccountInformation)
			vars['DA' + da.dedicatedAccountID] = da.dedicatedAccountValue1;

	if (r.offerInformationList)
		for (const o of r.offerInformationList)
			if (o.usageCounterUsageThresholdInformation)
				for (const uc of o.usageCounterUsageThresholdInformation)
					vars['UC' + uc.usageCounterID] = uc.usageThresholdInformation[0].usageThresholdValue - uc.usageCounterValue; 

	//console.debug(vars);
	vars.balance = r.accountValue1;
	vars.currency = r.currency1;
	vars.serviceClass = r.serviceClassCurrent;

	const lang = r.languageIDCurrent;
	vars.lang = lang < componentConfig.langs.length ? componentConfig.langs[lang] : componentConfig.lang[0];
	vars.overwrite = true;
	console.debug('DONE');
}

module.exports = {
	call
}

function handleResponse(error, response, body, resolve, reject) {
	if (error) {
		console.error('pccgw built-in component handleResponse error', error)
		reject(error);
	} else if (response.statusCode >= 200 && response.statusCode <= 299) {
		//console.debug('Successful REST request, HTTP status code:', response.statusCode);
		if (body) {
			//console.trace('RESPONSE body:\n', body)
			resolve(body);
		}
		else {
			resolve();
		}
	}
	else {
		//console.error('REST error:\n', response);
		let status = -1
		if (response.statusCode)
			status = response.statusCode;

		reject({ error: { status } });
	}
}

async function postRequest(req) {

	if (req.body) {
		if (req.headers) req.headers['Content-Type'] = 'text/xml';
		else req.headers = { 'Content-Type': 'text/xml' };
		//req.body = JSON.stringify(req.body);

		req.headers['SOAPAction'] = '\"\"';
	}

	return new Promise((resolve, reject) => {
		request.post(req, (error, response, body) => {
			handleResponse(error, response, body, resolve, reject);
		})
	});

}

function setupUcipRequest(msisdn, cfg) {
	return {
		originNodeType: cfg.nodetype
		, originHostName: cfg.hostname 
		, originTransactionID: getTransactionId()
		, originTimeStamp: new Date()
		, subscriberNumberNAI: cfg.nai
		, subscriberNumber: msisdn
	};
}

function getTransactionId() {
	return String(Math.floor(Math.random() * 1000000) + 100000000);
}
