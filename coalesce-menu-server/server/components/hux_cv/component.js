const hux = require("@coalesce/hux");

async function call(vars, ctx) {
  console.info("Call 'hux_cv' custom Component");

  hux.setEndpoint("ecds", ctx);

  if (!ctx.request) ctx.request = {};

  const ussdCode = vars.huxcode;
  ctx.request.USSDServiceCode = ussdCode;

  const additionalParams = {};

  const enableAutoRespond = vars.enableAutoRespond;
  const autoRespondValue = vars.autoRespondValue;

  try {
    // Step 1: Send initial request
    const response = await hux.sendUSSD(ussdCode, ctx, additionalParams);
    const responseStr = response.USSDResponseString || "";

    console.debug("[HUX_CV Component] Initial USSD response:", responseStr);

    const shouldAutoRespond =
      enableAutoRespond &&
      typeof autoRespondValue !== "undefined";

    if (shouldAutoRespond) {
      console.debug(`[HUX_CV Component] Auto-responding with '${autoRespondValue}'...`);

      const originalServiceCode = ussdCode;
      const originalTransactionId = ctx.request.TransactionId;

      // Setup request context for follow-up
      ctx.new = false;
      ctx.request.response = true;
      ctx.request.USSDRequestString = autoRespondValue;
      ctx.request.USSDServiceCode = originalServiceCode;
      ctx.request.TransactionId = originalTransactionId;

      const confirmParams = {};

      console.debug("HUX_CV component TT", confirmParams.TransactionTime);

      const confirmResponse = await hux.sendUSSD(
        ctx.variables.huxcode,
        ctx,
        confirmParams
      );

      // Set context properties directly (don't return result object)
      ctx.output = confirmResponse.USSDResponseString || "Auto-response done.";
      ctx.done = confirmResponse.action !== "request";
      ctx.respond = true;
      
      // Add newline if there's output (like built-in component does)
      ctx.output += ctx.output.length !== 0 ? "\n" : "";
      
      return; // Don't return result object
    }

    // Step 3: Normal flow
    ctx.output = responseStr || "No response.";
    ctx.done = response.action !== "request";
    ctx.respond = true;
    
    // Add newline if there's output (like built-in component does)
    ctx.output += ctx.output.length !== 0 ? "\n" : "";

  } catch (e) {
    console.error("[HUX_CV Component] Error during USSD request:", e);
    ctx.output = "An error occurred while processing your request.\n";
    ctx.done = true;
    ctx.respond = true;
  }
}

module.exports = { call };
