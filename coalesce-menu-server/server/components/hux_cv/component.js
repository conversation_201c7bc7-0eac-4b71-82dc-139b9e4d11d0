const hux = require("@coalesce/hux");

async function call(vars, ctx) {
  console.info("Call 'hux_cv' custom Component");
  console.debug("[HUX_CV Component] Variables received:", JSON.stringify(vars));

  hux.setEndpoint("ecds", ctx);

  if (!ctx.request) ctx.request = {};

  const ussdCode = vars.huxcode;
  ctx.request.USSDServiceCode = ussdCode;

  const additionalParams = {};

  const enableAutoRespond = vars.enableAutoRespond;
  const autoRespondValue = vars.autoRespondValue;

  console.debug("[HUX_CV Component] enableAutoRespond:", enableAutoRespond, "autoRespondValue:", autoRespondValue);

  try {
    // Step 1: Send initial request
    const response = await hux.sendUSSD(ussdCode, ctx, additionalParams);
    const responseStr = response.USSDResponseString || "";

    console.debug("[HUX_CV Component] Initial USSD response:", responseStr);
    console.debug("[HUX_CV Component] Response action:", response.action);

    const shouldAutoRespond =
      enableAutoRespond &&
      typeof autoRespondValue !== "undefined";

    console.debug("[HUX_CV Component] shouldAutoRespond:", shouldAutoRespond);

    if (shouldAutoRespond) {
      console.debug(`[HUX_CV Component] Auto-responding with '${autoRespondValue}'...`);

      const originalServiceCode = ussdCode;
      const originalTransactionId = ctx.request.TransactionId;

      // Setup request context for follow-up
      ctx.new = false;
      ctx.request.response = true;
      ctx.request.USSDRequestString = autoRespondValue;
      ctx.request.USSDServiceCode = originalServiceCode;
      ctx.request.TransactionId = originalTransactionId;

      const confirmParams = {};

      console.debug("HUX_CV component TT", confirmParams.TransactionTime);

      const confirmResponse = await hux.sendUSSD(
        ctx.variables.huxcode || ussdCode,
        ctx,
        confirmParams
      );

      console.debug("[HUX_CV Component] Auto-response result:", confirmResponse.USSDResponseString);
      console.debug("[HUX_CV Component] Auto-response action:", confirmResponse.action);

      // Set context properties directly (don't return result object)
      ctx.output = confirmResponse.USSDResponseString || "Auto-response done.";
      ctx.done = confirmResponse.action !== "request";
      ctx.respond = true;
      
      // Add newline if there's output (like built-in component does)
      ctx.output += ctx.output.length !== 0 ? "\n" : "";
      
      console.debug("[HUX_CV Component] Final context - output:", ctx.output, "done:", ctx.done, "respond:", ctx.respond);
      return; // Don't return result object
    }

    // Step 3: Normal flow
    ctx.output = responseStr || "No response.";
    ctx.done = response.action !== "request";
    ctx.respond = true;
    
    // Add newline if there's output (like built-in component does)
    ctx.output += ctx.output.length !== 0 ? "\n" : "";

    console.debug("[HUX_CV Component] Normal flow - output:", ctx.output, "done:", ctx.done, "respond:", ctx.respond);

  } catch (e) {
    console.error("[HUX_CV Component] Error during USSD request:", e);
    ctx.output = "An error occurred while processing your request.\n";
    ctx.done = true;
    ctx.respond = true;
  }
}

module.exports = { call };
