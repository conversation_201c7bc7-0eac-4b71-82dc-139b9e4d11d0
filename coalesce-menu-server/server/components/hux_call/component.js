const { performance } = require('perf_hooks');
const hux = require("@coalesce/xmlrpc");
// wrapper around nodejs 'request' so that we can log timeouts among other things correctly
const { ComponentRequestError, sendRequest, getOptions } = require("@coalesce/request");
const { encodeURIPath } = require("@coalesce/utils");
const CoalesceError = require("@coalesce/coalesceError");

const LIBRARY_NAME = "components/hux_call"

function randomTransactionId() {
  return Math.floor(100000000 + Math.random() * 900000000);
}

const DEFAULT_ENDPOINT = "smartshop";

class HuxError extends CoalesceError {
  constructor(...params) {
    super(HuxError, ...params);
  }
}

async function call(vars, ctx) {
  console.info("Call 'hux_call' built-in Component");

  const useEndpoint = vars.hux_call_endpoint || vars.set_endpoint || DEFAULT_ENDPOINT;

  if (!vars.huxcode) {
    console.error("No huxcode specified in 'vars'");
    return;
  }

  if (!ctx.connectors.hux || !ctx.connectors.hux[useEndpoint]) {
    throw new HuxError(null,"Missing hux connector endpoint");
  }

  const MSISDN = ctx.request.MSISDN;

  const startTime = performance.now();

  const config = ctx.connectors.hux[useEndpoint];

  const encodedPath = encodeURIPath(config.url.replace(/^\/+/, ""));

  const huxurl = `http://${config.hostname}:${config.port}/${encodedPath}`;
  console.info(`LIB: ${LIBRARY_NAME} | START EXT Endpoint [${huxurl}] USSD Request [${vars.huxcode}] subject [${MSISDN}]`);

  const params = {
    TransactionId: ctx.request.TransactionId ? ctx.request.TransactionId : randomTransactionId(),
    TransactionTime: new Date(),
    MSISDN,
    USSDRequestString: ctx.new ? "#" : ctx.request.USSDRequestString,
    USSDEncoding: "GSM0338",
    response: ctx.new ? false : ctx.request.response,
  };

  if (ctx.new) {
    let huxcode = vars.huxcode;
    if (huxcode[0] === "*") huxcode = vars.huxcode.substr(1, vars.huxcode.length - 1);
    if (huxcode[huxcode.length - 1] === "#") huxcode = huxcode.substr(0, huxcode.length - 1);

    let parts = huxcode.split("*");
    params.USSDServiceCode = parts[0];

    if (parts.length > 1) {
      parts.shift();
      params.USSDRequestString = "*";
      params.USSDRequestString += parts.join("*");
      params.USSDRequestString += "#";
    }
  } else {
    let parts = vars.huxcode.split("*");
    params.USSDServiceCode = parts[0];
    //parts.shift();
    //params.USSDRequestString = '*';
    //params.USSDRequestString += parts.join('*');
    //params.USSDRequestString += '#';
  }

  const body = hux.encodeRequest("handleUSSDRequest", params);

  let { headers, timeout } = getOptions(config);
  let headersText = headers ? JSON.stringify(headers) : {};

  console.debug(`HuX request URL ${huxurl}`);
  console.trace(`HuX request content ${body}`);
  console.debug(`HuX request headers ${headersText}`);
  let xml;
  let response;
  try {
    const callResponse = await sendRequest({ url: huxurl, body, headers, timeout });
    xml = callResponse.data;
    response = hux.parseResponse(xml);
    console.trace('HuX response content', xml);
    const endTime = performance.now()
    console.info(
      `LIB: ${LIBRARY_NAME} | ` +
      `END EXT Endpoint [${huxurl}] USSD Request [${vars.huxcode}] ` +
      `subject [${MSISDN}] ` +
      `USSDResponseString [${response.USSDResponseString}] ` +
      `duration [${(endTime - startTime).toFixed(3)}ms]`
    );
  } catch (error) {
    const endTime = performance.now()

    let errorResponseString = 'Exception-Thrown';
    if (error instanceof ComponentRequestError) {
      errorResponseString = `HTTP_NETWORK_ERROR:${error.getErrorDescription()}`;
    }

    console.error(
      `LIB: ${LIBRARY_NAME} | EXCEPTION | ` +
      `END EXT Endpoint [${huxurl}] USSD Request [${vars.huxcode}] ` +
      `subject [${MSISDN}] ` +
      `response [${errorResponseString}] ` +
      `duration [${(endTime - startTime).toFixed(3)}ms]`
    );
    let huxError = new HuxError(error,"Hux request to Smartshop failed");
    throw huxError;
  }

  //const post = util.promisify(request.post);
  //const xml = await post({ url: airurl, body });

  response = hux.parseResponse(xml);
  console.trace('Hux Parsed Response', response);
  if (response.action === "request") {
    ctx.done = false; //Mark as not done
    ctx.respond = true; // Mark to send response to user
    ctx.output = response.USSDResponseString;
    ctx.raw = xml;
  } else {
    ctx.done = true; //Mark as done
    ctx.respond = true; // Mark to send response to user
    if (vars.store_response && vars.store_response === "1") {
      ctx.output = "";
    } else {
      ctx.output = response.USSDResponseString;
    }

    ctx.raw = xml;
  }

  // XML parser trims the new line at the end of message
  ctx.output += ctx.output.length !== 0 ? "\n" : "";

  vars.response = response.USSDResponseString;
}

module.exports = {
  call,
};
