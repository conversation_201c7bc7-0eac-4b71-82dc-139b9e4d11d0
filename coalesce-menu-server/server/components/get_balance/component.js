const ucip = require("@coalesce/ucip");

const CoalesceError = require("@coalesce/coalesceError");

class GetBalanceError extends CoalesceError{
  constructor(...params){
    super(GetBalanceError ,...params);
  }
}

async function call(vars, ctx) {
  if (!ctx.request) return;

  console.info("==== get_balance (builtin component) ====");

  const subscriberNumber = ctx.request.MSISDN;

  if (typeof subscrberNumber != "string") {
    let missingSubscriberError = new GetBalanceError(null,"= Something is wrong, missing subscriberNumber in context :/" );
    console.error(missingSubscriberError);
    throw missingSubscriberError;
  }

  const params = {
    subscriberNumber,
    requestedInformationFlags: { requestMasterAccountBalanceFlag: true },
    requestPamInformationFlag: true,
    //requestActiveOffersFlag: true,
    requestAttributesFlag: true,
  };

  const response = await ucip.getAccountDetails(params, ctx);

  if (response.responseCode >= 100) {
    let airResponseError = new GetBalanceError(null,"= Air failed with code: " + String(response.responseCode))
    console.error(airResponseError);
    throw airResponseError;
  }

  vars.balance = response.accountValue1;
  vars.accountValue = response.accountValue1;
}

module.exports = {
  call,
};
