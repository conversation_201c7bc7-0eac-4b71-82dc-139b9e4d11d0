
const CoalesceError = require ('@coalesce/coalesceError');
const hux = require('@coalesce/xmlrpc')
const { sendRequest, getOptions } = require("@coalesce/request");
//
class MenuCallError extends CoalesceError{
	constructor (...params){
		super(MenuCallError,...params);
	}
}

function randomTransactionId() {
	return Math.floor(100000000 + Math.random() * 900000000);
}

async function call(vars, ctx) {
	console.info("Calling 'menucall' built-in component");

	if ( ! ctx || !ctx.request || ! ctx.params ) return;

	const config=ctx.params;

	const huxurl = `http://${config.hostname}:5000/RPC2`;

	const params =  {
		TransactionId: randomTransactionId(),
		TransactionTime: new Date(),
		MSISDN: ctx.request.MSISDN,
		//USSDServiceCode: ussdCode,
		USSDRequestString: config.requestString,
		USSDEncoding: 'GSM0338',
		response: ctx.new?'false':ctx.request.response
	};

	if (vars.language)
		params.LANG = ctx.variables.language || vars.language;

	let huxcode = vars.huxcode||'9999';
	if (huxcode[0] === '*') huxcode = vars.huxcode.substr(1, vars.huxcode.length - 1);
	if (huxcode[huxcode.length-1] === '#') huxcode = huxcode.substr(0, huxcode.length - 1);
	let parts = huxcode.split('*');
	params.USSDServiceCode = parts[0];
	if (ctx.new) {
		if (parts.length > 1) {
			parts.shift();
			params.USSDRequestString = '*';
			params.USSDRequestString += parts.join('*');
			params.USSDRequestString += '#';
		}
		
	}
	else {
		params.USSDRequestString = ctx.request.USSDRequestString
	}

	const body = hux.encodeRequest('handleUSSDRequest', params);

	let { headers, timeout } = getOptions(config);
	let headersText = JSON.stringify(headers)

	console.debug(`HuX request URL ${huxurl}`);
	console.trace(`HuX request content ${body}`);
	console.debug(`HuX request headers ${headersText}`);
	let xml = null;
	try {
		const menuResponse  = await sendRequest({ url: huxurl, body, headers, timeout });
		xml = menuResponse.data
		console.trace('HuX response content', xml);
	}
	catch(err) {
		let menuCallError = new MenuCallError(err,ctx.getGlobalMessage(vars, 'service_unavailable'));

		console.error(`HuX Error ${err}`);
		vars[config.variable] = ctx.getGlobalMessage(vars, 'service_unavailable')
		ctx.done = true  //Mark as done
		ctx.respond = true // Mark to send response to user
		ctx.output = vars[config.variable]
		ctx.raw = xml
	}

	if (xml != null) {
		const response = hux.parseResponse(xml);
		console.trace('Parsed Hux Response', response);
		if (response.action === 'request') {
			ctx.done = false  //Mark as not done
			ctx.respond = true // Mark to send response to user
			ctx.output = response.USSDResponseString
			ctx.raw = xml
		}
		else {
			ctx.done = true  //Mark as done
			ctx.respond = true // Mark to send response to user
			ctx.output = response.USSDResponseString
			ctx.raw = xml
		}

		// XML parser trims the new line at the end of message
		ctx.output += '\n'

		vars[config.variable] = response.USSDResponseString
	}
}

module.exports = {
	call
}
