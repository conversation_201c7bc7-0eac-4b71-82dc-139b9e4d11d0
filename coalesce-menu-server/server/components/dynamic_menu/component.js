async function call(vars, ctx) {
  const Menu = require('@coalesce/menujs');
  const I18n = require('@coalesce/i18n');

  // Begin by creating the menu structure if the context is NEW - Initialisation
  if (ctx.new && ctx.variables) {
    console.debug('Initial creation of Dynamic Menu');

    const i18n = new I18n(ctx.variables.language); // if invalid/empty will automatically be set to default
    ctx._rootMenu = new Menu(); // menu passed into CTX for subsequent requests
    const menu = ctx._rootMenu;
    const vacationMenu = new Menu();
    const funMenu = new Menu();

    menu.addText(
      i18n.from({
        eng: 'EN: Welcome to dynamic menus',
        fra: 'FR: Welcome to dynamic menus',
        ewe: 'EW: Welcome to dynamic menus',
      }),
    );

    menu.addItem(
      i18n.from({
        eng: 'EN: Check your data',
        fra: 'FR: Check your data',
        ewe: 'EW: Check your data',
      }),
      (ctx) => {
        Menu.display(
          ctx,
          i18n.from({
            eng: 'EN: You have 1000 MB',
            fra: 'FR: You have 1000 MB',
            ewe: 'EW: You have 1000 MB',
          }),
        );
      },
    );

    menu.addItem(
      i18n.from({
        eng: 'EN: Check your balance',
        fra: 'FR: Check your balance',
        ewe: 'EW: Check your balance',
      }),
      (ctx) => {
        Menu.ask(
          ctx,
          i18n.from({
            eng: 'EN: Are you sure?',
            fra: 'FR: Are you sure?',
            ewe: 'EW: Are you sure?',
          }),
          () => {
            if (ctx.input == 'y')
              Menu.display(
                ctx,
                i18n.from({
                  eng: 'EN: You have 100$',
                  fra: 'FR: You have 100$',
                  ewe: 'EW: You have 100$',
                }),
              );
            else
              Menu.ask(
                ctx,
                i18n.from({
                  eng: 'EN: Are you really sure?',
                  fra: 'FR: Are you really sure?',
                  ewe: 'EW: Are you really sure?',
                }),
                (ctx) => {
                  const _menuCtx = Menu.getNestedContext(ctx);

                  if (_menuCtx.input == 'y')
                    Menu.display(
                      ctx,
                      i18n.from({
                        eng: 'EN: Bye',
                        fra: 'FR: Bye',
                        ewe: 'EW: Bye',
                      }),
                    );
                  else
                    Menu.display(
                      ctx,
                      i18n.from({
                        eng: 'EN: You have 120$ ;)',
                        fra: 'FR: You have 120$ ;)',
                        ewe: 'EW: You have 120$ ;)',
                      }),
                    );
                },
              );
          },
        );
      },
    );

    menu.addItem(
      i18n.from({
        eng: 'EN: Nested menu',
        fra: 'FR: Nested menu',
        ewe: 'EW: Nested menu',
      }),
      (ctx) => {
        Menu.callMenu(ctx, funMenu);
      },
    );

    /*
    menu.addItem(
      i18n.from({
        eng: 'EN: SmartShop call',
        fra: 'FR: SmartShop call',
        ewe: 'EW: SmartShop call',
      }),
      async (ctx) => {
        await Menu.callComponent(
          ctx,
          {
            name: 'hux_call',
            config: { url: 'http://127.0.0.1:4021/RPC2', ussd: '*353*1#' },
          },
          (ctx) => {
            Menu.display(ctx, ctx.response);

            const _menuCtx = Menu.getNestedContext(ctx);

            if (!_menuCtx.waiting)
              Menu.display(
                ctx,
                i18n.from({
                  eng: '\nEN: Thank you for using our service',
                  fra: '\nFR: Thank you for using our service',
                  ewe: '\nEW: Thank you for using our service',
                }),
              );
          },
        );
      },
		);
		*/

    menu.addItem(
      i18n.from({
        eng: 'EN: Module call',
        fra: 'FR: Module call',
        ewe: 'EW: Module call',
      }),
      (ctx) => {
        Menu.display(
          ctx,
          i18n.from({
            eng: '\nEN: Thank you for using our service',
            fra: '\nFR: Thank you for using our service',
            ewe: '\nEW: Thank you for using our service',
          }),
        );
      },
    );

    // Fun menu
    funMenu.addItem(
      i18n.from({
        eng: 'EN: Get some beer',
        fra: 'FR: Get some beer',
        ewe: 'EW: Get some beer',
      }),
      (ctx) => {
        Menu.display(
          ctx,
          i18n.from({
            eng: 'EN: No beer for you today',
            fra: 'FR: No beer for you today',
            ewe: 'EW: No beer for you today',
          }),
        );
      },
    );

    funMenu.addItem(
      i18n.from({
        eng: 'EN: Get some piza',
        fra: 'FR: Get some piza',
        ewe: 'EW: Get some piza',
      }),
      (ctx) => {
        Menu.ask(
          ctx,
          i18n.from({
            eng: 'EN: Do you really want pizza?\ny - yes',
            fra: 'FR: Do you really want pizza?\ny - yes',
            ewe: 'EW: Do you really want pizza?\ny - yes',
          }),
          (ctx) => {
            const _menuCtx = Menu.getNestedContext(ctx);
            if (_menuCtx.input == 'y')
              Menu.display(
                ctx,
                i18n.from({
                  eng: 'EN: No pizza without beer',
                  fra: 'FR: No pizza without beer',
                  ewe: 'EW: No pizza without beer',
                }),
              );
            else
              Menu.display(
                ctx,
                i18n.from({
                  eng: 'EN: No pizza for you today',
                  fra: 'FR: No pizza for you today',
                  ewe: 'EW: No pizza for you today',
                }),
              );
          },
        );
      },
    );

    funMenu.addItem(
      i18n.from({
        eng: 'EN: Would you like a vacation?',
        fra: 'FR: Would you like a vacation?',
        ewe: 'EW: Would you like a vacation?',
      }),
      (ctx) => {
        Menu.ask(
          ctx,
          i18n.from({
            eng: 'EN: Do you really need a vacation?',
            fra: 'FR: Do you really need a vacation?',
            ewe: 'EW: Do you really need a vacation?',
          }),
          (ctx) => {
            const _menuCtx = Menu.getNestedContext(ctx);
            if (_menuCtx.input == 'y') Menu.callMenu(ctx, vacationMenu);
            else
              Menu.display(
                ctx,
                i18n.from({
                  eng: 'EN: Bye. Have a nice day',
                  fra: 'FR: Bye. Have a nice day',
                  ewe: 'EW: Bye. Have a nice day',
                }),
              );
          },
        );
      },
    );

    // Vacation menu
    vacationMenu.addText(
      i18n.from({
        eng: 'EN: Vacation app',
        fra: 'FR: Vacation app',
        ewe: 'EW: Vacation app',
      }),
    );
    vacationMenu.addItem(
      i18n.from({
        eng: 'EN: One day',
        fra: 'FR: One day',
        ewe: 'EW: One day',
      }),
      (ctx) => {
        Menu.display(
          ctx,
          i18n.from({
            eng: 'EN: OK. You can have one day of vacation',
            fra: 'FR: OK. You can have one day of vacation',
            ewe: 'EW: OK. You can have one day of vacation',
          }),
        );
      },
    );

    vacationMenu.addItem(
      i18n.from({
        eng: 'EN: One week',
        fra: 'FR: One week',
        ewe: 'EW: One week',
      }),
      (ctx) => {
        Menu.display(
          ctx,
          i18n.from({
            eng: 'EN: No. You cannot take so much time off',
            fra: 'FR: No. You cannot take so much time off',
            ewe: 'EW: No. You cannot take so much time off',
          }),
        );
      },
    );
  }

  console.debug('Printing message .... ');
  console.debug('The Complete Context:', ctx);

  if (ctx._rootMenu) {
    console.debug('Printing message .... ');
    console.debug('The ROOT Context:', ctx);

    // Create the menu context if it does not exist...
    if (!Menu.hasNestedContext(ctx)) {
      Menu.createNestedContext(ctx);
      ctx.respond = true;
    } else {
      Menu.forNestedContextIn(ctx).write('input', ctx.request.USSDRequestString);
      // ctx._menuCtx.input = ctx.request.USSDRequestString;
      ctx.respond = false;
    }

    const _menuCtx = Menu.getNestedContext(ctx);

    // Run the menu request so user output is set!
    await ctx._rootMenu.execute(ctx);

    // Retrieve the menu context from within the component's own context

    // process output of dynamic menu in the root context
    ctx.output = _menuCtx.output;

    vars.response = _menuCtx.output;
    ctx.done = !_menuCtx.waiting;

    // Clear the output of the _menuCtx that is in the root context
    Menu.forNestedContextIn(ctx).write('output', '');
    // ctx._menuCtx.output = '';
  } else {
    console.debug('No rootMenu, this is a previewer initialisationrun, exiting from component...');
  }
}

module.exports = {
  call,
};
