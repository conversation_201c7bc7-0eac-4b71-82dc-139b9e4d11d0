const http = require('http');
//http://10.82.30.11:8558/400?ORIG=22896996564&USSD_PARAMS=1&MAP_VAR=CONCURRENT&SessionOp=new
//
//

const CoalesceError = require("@coalesce/coalesceError");

class CallOpenCodeError extends CoalesceError {
	constructor(...params){
		super (CallOpenCodeError, ...params);
	}
}


async function call(vars, ctx) {

	// Initialisation with no vars
	if (!ctx.request.MSISDN) return;

	http.get(`http://10.82.30.11:8558/400?ORIG=${ctx.request.MSISDN}&USSD_PARAMS=1&MAP_VAR=CONCURRENT&SessionOp=new`, (resp) => {
		let data = '';
	
		// A chunk of data has been recieved.
		resp.on('data', (chunk) => {
			data += chunk;
		});
	
		// The whole response has been received. Print out the result.
		resp.on('end', () => {
			console.debug(JSON.parse(data).explanation);
		});
	
	}).on("error", (err) => {
		let callOpenCodeError = new CallOpenCodeError(err,"calling opencode failed");
		console.error(callOpenCodeError);
	});
}


module.exports = {
	call
}
