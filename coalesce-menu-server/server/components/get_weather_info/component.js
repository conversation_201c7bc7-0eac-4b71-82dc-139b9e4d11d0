
const UPDATE_INTERVAL = 1000 * 60 * 5;
const CITIES = ['London', 'Johannesburg', 'Sofia'];
const STATES = ['sunny', 'cloudy', 'rainy'];
const DIRECTIONS = ['south', 'north', 'west', 'east', 'south west', 'south east', 'north west', 'north east']
const CoalesceError = require("@coalesce/coalesceError");

const cities = new Map;
var update_time = new Date();

class GetWeatherInfoError extends CoalesceError {
	constructor(...params){
		super (GetWeatherInfoError,...params );
	}
}

function init() {
	for (const c of CITIES) {
		cities.set(c, {
			current: 0,
			feels: 0,
			humidity: 0,
			state: '',
			winddir: ''
		});
	}

	update();
}

async function call(vars, ctx) {
	if (!ctx.request) return;

	console.debug(vars);

	const city = vars.city;
	if (!city) {
		let cityInputError = new GetWeatherInfoError(null,"city input variable is missing");
		console.error(cityInputError);
		throw cityInputError;
	}

	const info = cities.get(city);
	if (!info) {
		let cityInfoError = new GetWeatherInfoError(null,`No information for '${city}'`);
		console.error(cityInfoError );
		throw cityInfoError;
	}

	vars.degrees = info.current;
	vars.feel_degrees = info.feels;
	vars.humidity = info.humidity;
	vars.state = STATES[info.state];
	vars.wind_direction = DIRECTIONS[info.state];
	vars.last_update = update_time.toTimeString();

	console.debug('Weather info component done');
}

function update() {

	cities.forEach(v => {
		v.current = 40 - Math.floor(Math.random() * 60);
		v.feels = v.current + (5 - Math.floor(Math.random() * 10));
		v.humidity = Math.floor(Math.random() * 100);
		v.state = Math.floor(Math.random() * STATES.length);
		v.winddir = Math.floor(Math.random() * DIRECTIONS.length);
	});

	update_time = new Date;

	setTimeout(update, UPDATE_INTERVAL);
}

module.exports = {
	init,
	call
}
