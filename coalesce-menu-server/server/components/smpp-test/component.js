const CoalesceError = require("@coalesce/coalesceError");

class SmscError extends CoalesceError {
	constructor(...params){
		super(SmscError,...params );
	}
}

async function call(vars, ctx) {
	try {
		const smpp = require('smsc');

		smpp.setEndpoint('smsc', ctx);

		vars.SMS = 'SMPP test. Hello !!!';
		vars.MSISDNB = '12345678';

		await smpp.sendSms(vars, ctx);
	} catch (error){
		let smscError= new SmscError(error,"smsc failed: ")
		console.error(smscError);
		throw smscError;
	}
}

module.exports = {
	call,
};
