async function call(vars, ctx) {
  if (!ctx.request) return;

  console.info("==== panthera (builtin component) ====");

  const subscriberNumber = ctx.request.MSISDN;
  const ussdRequestString = String(ctx.request.USSDRequestString).slice(0, -1);
  const ussdRequest = ussdRequestString.split('*');
  vars.response =  -1;
  vars.msisdnb =  ussdRequest[2];

  try {

  }
  catch (err) {
    console.warn(err)
    vars.response =  -1;
  }
}

module.exports = {
  call,
};