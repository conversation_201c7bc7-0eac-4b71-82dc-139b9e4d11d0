const ucip = require("@coalesce/ucip");

const CoalesceError  = require ('@coalesce/coalesceError');


class CommonComponentError extends CoalesceError {
  constructor(...params) {
    super(CommonComponentError ,...params);
  }
}


async function call(vars, ctx) {

  // Initialisation with empty context (only { new: true })
  if (!ctx.request) return;

  console.info("==== Running common_properties built-in component ====");

  // ---- IMPORTANT, subscriberNumber MUST be typeof === "String"
  const subscriberNumber = String(ctx.request.MSISDN);
  // ---- AIR almost always accepts INTERNATIONAL (1)		0=national-w-prefix, 1=international, 2=national-w/o-prefix

  const params = {
    subscriberNumber,
    requestedInformationFlags: {
      requestMasterAccountBalanceFlag: true,
    },
    requestPamInformationFlag: true,
  };

  let response = null;

  try {
    response = await ucip.getAccountDetails(params, ctx);
  } catch (error){
    throw new CommonComponentError(error,"= Air failed with exception: ");
  }

  console.trace("=== AIR Response from common_properties ===\n", JSON.stringify(response));

  if (response.responseCode >= 100) {
    console.error("= Air failed with code: ", String(response.responseCode));
    throw new CommonComponentError(null ,`= Air failed with code: ${String(response.responseCode)}`);
  }

  vars.serviceclass = response.serviceClassCurrent;
  vars.languageID = response.languageIDCurrent;
  vars.language = vars.langmap[vars.languageID];
  vars.accountValue = response.accountValue1;
  vars.currency1 = response.currency1;

  // For testing purposes, we can uncomment the code below
  /*
  vars.serviceclass = 4;
  vars.languageID = 0;
  vars.language = vars.langmap[vars.languageID];
  vars.accountValue = "100";
  vars.currency1 = "USD";
  */

}

module.exports = {
  call,
};
