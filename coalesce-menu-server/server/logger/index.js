/**
 * @deprecated This file is deprecated and replaced by logger.js
 */

// /* eslint-disable class-methods-use-this */
// const log4js = require("log4js");
// const { getHotConfig } = require("../HotConfig");
// const path = require("path");
// const configDefaults = require("./defaults.json");
// const { Roarr } = require("roarr");
// const { nanoid } = require("nanoid");
// require("dotenv").config();

// const CONST = require("@coalesce/constants");
// const { LOG_LEVEL_ETCD_KEY } = require("@coalesce/env");
// const LOG_LEVEL_KEY = LOG_LEVEL_ETCD_KEY;

// const { Client } = require("@elastic/elasticsearch");

// const DEFAULT_LOG_LEVEL = "info";

// const customer = process.env.CS_CUSTOMER_NAME || configDefaults.customer;
// const environment = process.env.CS_ENVIRONMENT_NAME || configDefaults.environment;

// const elasticConfig = {
//   url: process.env.ELASTIC_URL,
//   username: process.env.ELASTIC_USERNAME,
//   password: process.env.ELASTIC_PASSWORD,
//   indexNamePrefix: `${customer}-${environment}-tdr`,
//   clientTimeoutMilliseconds: 5000,
//   clientMaxRetries: 7,
// };

// async function initializeLogger() {
//   const client = new Client({
//     node: elasticConfig.url,
//     maxRetries: elasticConfig.clientMaxRetries,
//     requestTimeout: elasticConfig.clientTimeoutMilliseconds,
//     auth: {
//       username: elasticConfig.username,
//       password: elasticConfig.password,
//     },
//   });

//   log4js.configure(path.join(__dirname, "log4js.json"));

//   const logger = log4js.getLogger();

//   const ACCEPTED_LOG_LEVELS = ["fatal", "error", "warn", "info", "debug", "trace"];

//   Object.defineProperty(console, "level", {
//     set: (newLevel) => {
//       const newLogLevel = String(newLevel).toLowerCase();
//       if (ACCEPTED_LOG_LEVELS.includes(newLogLevel)) {
//         logger.level = newLogLevel;
//         logger[newLogLevel](`Log Level has just been updated to: ${String(newLogLevel).toUpperCase()}`);
//       } else {
//         logger.level = DEFAULT_LOG_LEVEL;
//         console.warn("Unable to change log level, unrecognised level: ", newLogLevel);
//         console.warn('Forcing log level to internal DEFAULT: ', DEFAULT_LOG_LEVEL);
//       }
//     },
//     get: () => {
//       return logger.level.levelStr;
//     },
//   });

//   function logToMemory(level, message) {
//     const levelIndex = LOG_LEVELS.indexOf(level);
//     if (levelIndex > LOG_LEVEL_INDEX) return; // Ignore logs below the set log level

//     // Get existing Roarr context
//     const roarrCtx = Roarr.getContext();

//     // If no context is set, initialize with default values
//     const ctx = roarrCtx.msisdnA
//       ? roarrCtx
//       : {
//         msisdnA: "*****",
//         tid: null,
//       };

//     // Log using Roarr (without adopt)
//     Roarr[level](`[${level.toUpperCase()}] ${message}`);

//     // Store log in memory for periodic flushing
//     const logEntry = `[${new Date().toISOString()}] [${ctx.tid}] [${level.toUpperCase()}] ${message}`;
//     logMemory.push(logEntry);
//   }

//   // @todo-wahab: Uncomment the below lines

//   //console.trace = (...args) => transform("trace", ...args);
//   //console.error = (...args) => transform("error", ...args);
//   console.warn = (...args) => transform("warn", ...args);
//   console.info = (...args) => transform("info", ...args);
//   console.debug = (...args) => transform("debug", ...args);
//   //console.fatal = (...args) => transform("fatal", ...args);
//   // //NOTE: console.log translates to TRACE level. This is a FALLBACK in case logs are incorrectly written to 'console.log'
//   console.fatal = console.error;


//   await getHotConfig().createAndWatch(LOG_LEVEL_KEY, DEFAULT_LOG_LEVEL, (logLevel, key) => {
//     console.info(`Log level set to '${logLevel}' for ETCD key ${key}`);
//     console.level = logLevel;
//   });

//   console.debug(`Using '${elasticConfig.indexNamePrefix}' as the TDR index prefix`);
//   /*
//    * USAGE:
//    * console.tdr(CTX, TDR_Object);
//    *
//    * Where CTX : Context of a COMPONENT in the menu server
//    * Where TDR_Object: an object where keys will be used as field names in elasticsearch
//    */
//   console.tdr = async (...args) => {
//     const originalArgs = [...args]; // copy instead of reference
//     try {
//       const context = args[0];
//       args.shift();

//       const { componentName } = context || {};
//       const { MSISDN, USSDServiceCode } = context?.request || {};
//       const USSDRequestString = context?.ussdpath;

//       if (!MSISDN || !componentName || !USSDServiceCode || !USSDRequestString) {
//         console.warn("Missing menu server information when writing TDR. Unwritten TDR info:\n", originalArgs);
//         throw new Error(
//           "Context is missing One or more of the following mandatory request fields: MSISDN, componentName, USSDServiceCode, USSDRequestString"
//         );
//       }

//       console.debug("Context used with TDR:\n", JSON.stringify(context, null, 2));

//       const timestamp = new Date().toISOString();

//       const body = {
//         timestamp,
//         msisdn: MSISDN,
//         application: process.env.MODULE_NAME || "CS-Previewer",
//         componentName,
//         USSDServiceCode,
//         USSDRequestString,
//       };

//       const disallowFieldDuplication = [
//         "timestamp",
//         "msisdn",
//         "componentName",
//         "USSDServiceCode",
//         "USSDRequestString",
//         "application",
//       ];

//       if (typeof args[0] === "object") {
//         Object.keys(args[0]).forEach((key) => {
//           const objectKey = String(key);
//           if (!disallowFieldDuplication.includes(objectKey)) {
//             body[key] = args[0][key] || "[EMPTY]";
//           } else {
//             body[`__${key}`] = args[0][key];
//           }
//         });
//       } else {
//         body.data = args[0];
//       }

//       await client.index({
//         index: `${elasticConfig.indexNamePrefix}-${timestamp.slice(0, 10)}`,
//         body,
//       });
//       console.info("Elasticsearch client successfully written tdr");
//     } catch (err) {
//       console.error("Unable to write TDR, reason:\n", err);
//       console.error("Unwritten TDR Entry:", JSON.stringify(originalArgs));
//     }
//   };
// }

// module.exports = { initializeLogger };
