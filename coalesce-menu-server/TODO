1. Save session state in Redis/etcd
2. Config reloading in the backend - keep old config until there are subscribers using it (potentially saving it in Redis/etcd state)
3. Component loading and versioning in the backend - potentially via symlinks and calling 'require' on the files inside the container
4. Method to build a Docker image from code written in the GUI (WEB)
5. Investigate Kubernetes metrics
6. Support local arguments to component calls
7. Support component error handling (potentially implemented by exceptions)
8. Support debugging (potentially via internal protocol that is not HuX, e.g. JSON-RPC)
9. package.json: script for installing the components i.e. "post-install": "install-components.sh"