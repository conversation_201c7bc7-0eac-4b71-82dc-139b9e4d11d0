# Coalesce Menu Server

Coalesce Menu Server


## Debugger

1. Start debugger
```
server/debugger.js
```

2. Start command line client
```
test/grpcdb.js -c test/sample_menu.json
```

* Show help
```
# h
```

* Set breakpoint
```
# b bc56db60-971f-11e9-a6f8-2be4f01619c9
```

* Start execution
```
# c
```

* Sample output
```
eng: Welcome!
1. eng: Check usage
2. eng: Enable roaming
3. eng: Check Balance
9. eng: Exit menu

>
```

## Dynamic menus 2

With Dynamic menus, the software designer can create menus inside the components

### * Sample menu

Menu+ is used as an entry point for the menu execution. There is only one instruction which calls the Dynamic menu component `check_balance`
```
menu:
  - component:
        name: check_balance
```
and the component's code
```
const menu = require('@coalesce/dynamicmenu');

function* checkBalance(vars, ctx) {

    menu.display(ctx, `Welcome ${vars.MSISDN}`);
    menu.item(ctx, 1, 'Check balance');

    yield * menu.answer(ctx);

    switch (ctx.input) {

        case '1':
        menu.display(ctx, 'You have 100 EUR');
        break;
    }
}

await menu.execute(vars, ctx, checkBalance);
```
The menu rendered to the end user
```
Welcome 27711234562
1. Check balance
> 1
You have 100 EUR
```

### * Language support

There two instructions which can render text, `display` and `item`. Both of these can accept **String** or **Object** as argument. With **Object**, the deisgner can define messages for all the languages needed.

```
menu.display(ctx, { eng: 'You have 100€', fra: 'Vous avez 100€' });
```

### * Invalid option built-in menu
There is a built-in menu which will be displayed if the user selects an invalid option. Let's use the example above.

```
Welcome 27711234561
1. Check balance
> 2
You have selected an invalid option
1. Retry
2. Exit
> 1
Welcome 27711234561
1. Check balance
> 1
You have 100 EUR
```

### * Confirmation question
```
menu.display(ctx, `Welcome ${vars.MSISDN}`);

menu.item(ctx, 1, 'Check balance');
menu.item(ctx, 2, 'Enable roaming');

yield * menu.answer(ctx);

switch (ctx.input) {

    case '1':
    menu.display(ctx, 'You have 100 EUR');
    break;

    case '2': {
        menu.display(ctx, 'Are you sure? y/n');

        yield * menu.answer(ctx);

        if (ctx.input == 'y') {
            menu.display(ctx, 'You have successfully enabled roaming!');
        }
        else {
            menu.display(ctx, 'You have not enabled roaming, please try again!');
        }
    }
    break;
}
```
and the result
```
Welcome 27711234561
1. Check balance
2. Enable roaming
> 2
Are you sure? y/n
> y
You have successfully enabled roaming!
```

### * Nested menu
With Dynamic menu engine it's very easy to create nested menus. Let's extend the example above

```
menu.display(ctx, `Welcome ${vars.MSISDN}`);

menu.item(ctx, 1, 'Check balance');
menu.item(ctx, 2, 'Check data');

yield * menu.answer(ctx);

switch (ctx.input) {

    case '1':
    menu.display(ctx, 'You have 100 EUR');
    break;

    case '2': {
        menu.item(ctx, 1, 'National');
        menu.item(ctx, 2, 'Roaming');

        yield * menu.answer(ctx);

        switch (ctx.input) {
            case '1': // national
            menu.display(ctx, 'You have 200 MB left');
            break;

            case '2': // roaming
            menu.display(ctx, 'You have 50 MB left');
            break;
        }
    }
    break;
}
```
and the result
```
Welcome 27711234561
1. Check balance
2. Check data
> 2
1. National
2. Roaming
> 1
You have 200 MB left
```

### * Returning to the parent menu

Let's add a **back** option in the example above
```
menu.display(ctx, `Welcome ${vars.MSISDN}`);

menu.item(ctx, 1, 'Check balance');
menu.item(ctx, 2, 'Check data');

yield * menu.answer(ctx);

switch (ctx.input) {

    case '1':
    menu.display(ctx, 'You have 100 EUR');
    break;

    case '2': {
        menu.item(ctx, 1, 'National');
        menu.item(ctx, 2, 'Roaming');
        menu.item(ctx, 9, 'Back');

        yield * menu.answer(ctx);

        switch (ctx.input) {
            case '1': // national
            menu.display(ctx, 'You have 200 MB left');
            break;

            case '2': // roaming
            menu.display(ctx, 'You have 50 MB left');
            break;

            case '9':
            menu.back(ctx);
            break;
        }
    }
    break;
}
```
and the result
```
Welcome 27711234561
1. Check balance
2. Check data
> 2
1. National
2. Roaming
9. Back
> 9
Welcome 27711234561
1. Check balance
2. Check data
```

### * Advanced usage. Calling external system

In order to call an external system, the first thing is to declare the generator function as **async**

```
const menu = require('@coalesce/dynamicmenu');
const request = require('request');

async function* checkBalance(vars, ctx) {

    menu.display(ctx, `Welcome ${vars.MSISDN}`);
    menu.item(ctx, 1, 'Check balance');

    yield * menu.answer(ctx);

    switch (ctx.input) {

        case '1': {
            const r = await request.get('http://...');

            menu.display(ctx, `You have ${r.balance} ${r.currency}`);
        }
        break;
    }
}

await menu.execute(vars, ctx, checkBalance);
```
In this example we get user's details by sending an HTTP GET request to an external system