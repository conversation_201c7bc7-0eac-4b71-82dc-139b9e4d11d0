{"name": "coalesce-menu-server", "version": "0.1.0", "description": "Coalesce Menu Server", "main": "server/startServer.js", "config": {"entrypoint": "server/startServer.js"}, "scripts": {"start": "env ROARR_LOG=true NODE_PATH=common node $npm_package_config_entrypoint", "start-fake-elastic": "env ELASTIC_URL=http://localhost:9200 ROARR_LOG=true NODE_PATH=common node $npm_package_config_entrypoint", "start:dev": "env ROARR_LOG=true NODE_PATH=common nodemon $npm_package_config_entrypoint", "start-dev": "sed -i \"s/%NAMESPACE%/$NAMESPACE/g\" server/config.json", "dbg": "env ROARR_LOG=true CS_MENU_ENVIRONMENT=previewer NODE_PATH=common node $npm_package_config_entrypoint", "test": "env ROARR_LOG=true ELASTIC_URL=http://localhost:9200 ELASTIC_USERNAME=elastic ELASTIC_PASSWORD=changeme NODE_PATH=common jest --passWithNoTests --reporters='default' --reporters='jest-junit' --reporters='jest-html-reporter'", "test-on-console": "env ROARR_LOG=true ELASTIC_URL=http://localhost:9200 ELASTIC_USERNAME=elastic ELASTIC_PASSWORD=changeme NODE_PATH=common jest"}, "repository": {"type": "git", "url": "**************:csys/products/coalesce-studio/microservices/coalesce-menu-server.git"}, "keywords": ["menu", "server"], "dependencies": {"@elastic/elasticsearch": "^7.13.0", "@grpc/proto-loader": "^0.6.4", "axios": "^0.21.1", "axios-retry": "^3.1.9", "dotenv": "^10.0.0", "etcd3": "^1.1.0", "express": "^4.17.1", "fast-xml-parser": "^3.19.0", "fs": "0.0.1-security", "generic-pool": "^3.7.8", "grpc": "^1.24.10", "ioredis": "^4.28.5", "log4js": "^6.3.0", "mariadb": "^2.5.4", "md5": "^2.3.0", "nanoid": "^3.1.25", "require-from-string": "^2.0.2", "roarr": "^7.0.3", "smpp": "^0.5.0", "socket.io": "^4.5.4", "uuid": "^8.3.2", "xml2json": "^0.12.0", "yamljs": "^0.3.0"}, "devDependencies": {"@semantic-release/gitlab-config": "^8.0.0", "eslint": "^8.51.0", "jest": "^25.5.4", "jest-html-reporter": "^3.4.1", "jest-junit": "^12.2.0", "node-mocks-http": "^1.10.1", "nodemon": "^2.0.9", "prettier": "^2.8.8", "prettier-eslint": "^16.1.2", "readline-sync": "^1.4.10", "semantic-release": "^17.4.4", "supertest": "^6.1.3"}, "author": "Concurrent Systems Ltd.", "license": "ISC", "jest": {"verbose": false, "silent": false}}